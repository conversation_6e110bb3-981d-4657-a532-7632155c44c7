obj_dir/VminiRV_SoC.cpp obj_dir/VminiRV_SoC.h obj_dir/VminiRV_SoC.mk obj_dir/VminiRV_SoC__ConstPool_0.cpp obj_dir/VminiRV_SoC__Syms.cpp obj_dir/VminiRV_SoC__Syms.h obj_dir/VminiRV_SoC__Trace__0.cpp obj_dir/VminiRV_SoC__Trace__0__Slow.cpp obj_dir/VminiRV_SoC___024root.h obj_dir/VminiRV_SoC___024root__DepSet_h17f7d384__0.cpp obj_dir/VminiRV_SoC___024root__DepSet_h17f7d384__0__Slow.cpp obj_dir/VminiRV_SoC___024root__DepSet_h7ff9d9a5__0__Slow.cpp obj_dir/VminiRV_SoC___024root__Slow.cpp obj_dir/VminiRV_SoC__ver.d obj_dir/VminiRV_SoC_classes.mk  : /usr/local/bin/verilator_bin /usr/local/bin/verilator_bin mySoC/ALU.v mySoC/Bridge.v mySoC/Button.v mySoC/Ctrl.v mySoC/Dig.v mySoC/Led.v mySoC/NPC.v mySoC/PC.v mySoC/RF.v mySoC/SEXT.v mySoC/Switch.v mySoC/Timer.v mySoC/defines.vh mySoC/miniRV_SoC.v mySoC/myCPU.v vsrc/ram.v 
