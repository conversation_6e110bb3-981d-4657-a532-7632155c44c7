`timescale 1ns / 1ps

`include "defines.vh"

module Ctrl (
    input  wire [6:0]   opcode,     
    input  wire [2:0]   funct3,    
    input  wire [6:0]   funct7,    
    input  wire         alu_f,    
    output reg  [1:0]   npc_op,    
    output reg          rf_we,     
    output reg  [1:0]   rf_wsel,    
    output reg  [2:0]   sext_op,    
    output reg  [3:0]   alu_op,     
    output reg          alub_sel,  
    output reg          ram_we      
);

    // 根据指令类型生成控制信号
    always @(*) begin
        // 默认值
        npc_op   = `NPC_PC4;
        rf_we    = 1'b0;
        rf_wsel  = `WB_ALU;
        sext_op  = `EXT_I;
        alu_op   = `ALU_ADD;
        alub_sel = `ALUB_RS2;
        ram_we   = 1'b0;

        case (opcode)
            `OP_LUI: begin              // lui
                npc_op   = `NPC_PC4;
                rf_we    = 1'b1;
                rf_wsel  = `WB_EXT;
                sext_op  = `EXT_U;
            end

            `OP_JAL: begin              // jal
                npc_op   = `NPC_PCIMM;
                rf_we    = 1'b1;
                rf_wsel  = `WB_PC4;
                sext_op  = `EXT_J;
            end

            `OP_JALR: begin             // jalr
                npc_op   = `NPC_RD1IMM;         // 无条件跳转到rs1+imm
                rf_we    = 1'b1;
                rf_wsel  = `WB_PC4;
                sext_op  = `EXT_I;
                alu_op   = `ALU_ADD;
                alub_sel = `ALUB_EXT;
            end

            `OP_BRANCH: begin           // beq, blt
                sext_op  = `EXT_B;
                alub_sel = `ALUB_RS2;
                case (funct3)
                    3'b000: begin       // beq
                        alu_op = `ALU_BEQ;
                        npc_op = alu_f ? `NPC_PCIMM : `NPC_PC4;
                    end
                    3'b001: begin       // bne (为了trace测试)
                        alu_op = `ALU_BEQ;
                        npc_op = alu_f ? `NPC_PC4 : `NPC_PCIMM
                    end
                    3'b100: begin       // blt
                        alu_op = `ALU_BLT;
                        npc_op = alu_f ? `NPC_PCIMM : `NPC_PC4;
                    end
                    default: begin
                        alu_op = `ALU_BEQ;
                        npc_op = `NPC_PC4;
                    end
                endcase
            end

            `OP_LOAD: begin             // lw
                npc_op   = `NPC_PC4;
                rf_we    = 1'b1;
                rf_wsel  = `WB_DRAM;
                sext_op  = `EXT_I;
                alu_op   = `ALU_ADD;
                alub_sel = `ALUB_EXT;
            end

            `OP_STORE: begin            // sw
                npc_op   = `NPC_PC4;
                rf_we    = 1'b0;
                sext_op  = `EXT_S;
                alu_op   = `ALU_ADD;
                alub_sel = `ALUB_EXT;
                ram_we   = 1'b1;
            end

            `OP_IMM: begin              // addi, andi, ori, xori, slli, srli, srai
                npc_op   = `NPC_PC4;
                rf_we    = 1'b1;
                rf_wsel  = `WB_ALU;
                alub_sel = `ALUB_EXT;
                case (funct3)
                    3'b000: begin       // addi
                        sext_op = `EXT_I;
                        alu_op  = `ALU_ADD;
                    end
                    3'b111: begin       // andi
                        sext_op = `EXT_I;
                        alu_op  = `ALU_AND;
                    end
                    3'b110: begin       // ori
                        sext_op = `EXT_I;
                        alu_op  = `ALU_OR;
                    end
                    3'b100: begin       // xori
                        sext_op = `EXT_I;
                        alu_op  = `ALU_XOR;
                    end
                    3'b001: begin       // slli
                        sext_op = `EXT_shift;
                        alu_op  = `ALU_SLL;
                    end
                    3'b101: begin       // srli, srai
                        sext_op = `EXT_shift;
                        if (funct7[5] == 1'b0)
                            alu_op = `ALU_SRL;  // srli
                        else
                            alu_op = `ALU_SRA;  // srai
                    end
                    default: begin
                        sext_op = `EXT_I;
                        alu_op  = `ALU_ADD;
                    end
                endcase
            end

            `OP_REG: begin              // add, and, or, xor, sll, srl, sra
                npc_op   = `NPC_PC4;
                rf_we    = 1'b1;
                rf_wsel  = `WB_ALU;
                alub_sel = `ALUB_RS2;
                case (funct3)
                    3'b000: begin       // add
                        if (funct7[5] == 1'b0)
                            alu_op = `ALU_ADD;  // add
                        else
                            alu_op = `ALU_SUB;  // sub
                    end
                    3'b111: alu_op = `ALU_AND;  // and
                    3'b110: alu_op = `ALU_OR;   // or
                    3'b100: alu_op = `ALU_XOR;  // xor
                    3'b001: alu_op = `ALU_SLL;  // sll
                    3'b101: begin               // srl, sra
                        if (funct7[5] == 1'b0)
                            alu_op = `ALU_SRL;  // srl
                        else
                            alu_op = `ALU_SRA;  // sra
                    end
                    default: alu_op = `ALU_ADD;
                endcase
            end

            default: begin
                // 保持默认值
            end
        endcase
    end

endmodule
