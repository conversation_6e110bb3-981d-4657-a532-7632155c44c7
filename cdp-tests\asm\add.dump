
add:     file format elf32-littleriscv


Disassembly of section .text.init:

00000000 <_start>:
   0:	0040006f          	jal	x0,4 <reset_vector>

00000004 <reset_vector>:
   4:	00000093          	addi	x1,x0,0
   8:	00000113          	addi	x2,x0,0
   c:	00208733          	add	x14,x1,x2
  10:	00000393          	addi	x7,x0,0
  14:	00200193          	addi	x3,x0,2
  18:	4c771663          	bne	x14,x7,4e4 <fail>

0000001c <test_3>:
  1c:	00100093          	addi	x1,x0,1
  20:	00100113          	addi	x2,x0,1
  24:	00208733          	add	x14,x1,x2
  28:	00200393          	addi	x7,x0,2
  2c:	00300193          	addi	x3,x0,3
  30:	4a771a63          	bne	x14,x7,4e4 <fail>

00000034 <test_4>:
  34:	00300093          	addi	x1,x0,3
  38:	00700113          	addi	x2,x0,7
  3c:	00208733          	add	x14,x1,x2
  40:	00a00393          	addi	x7,x0,10
  44:	00400193          	addi	x3,x0,4
  48:	48771e63          	bne	x14,x7,4e4 <fail>

0000004c <test_5>:
  4c:	00000093          	addi	x1,x0,0
  50:	ffff8137          	lui	x2,0xffff8
  54:	00208733          	add	x14,x1,x2
  58:	ffff83b7          	lui	x7,0xffff8
  5c:	00500193          	addi	x3,x0,5
  60:	48771263          	bne	x14,x7,4e4 <fail>

00000064 <test_6>:
  64:	800000b7          	lui	x1,0x80000
  68:	00000113          	addi	x2,x0,0
  6c:	00208733          	add	x14,x1,x2
  70:	800003b7          	lui	x7,0x80000
  74:	00600193          	addi	x3,x0,6
  78:	46771663          	bne	x14,x7,4e4 <fail>

0000007c <test_7>:
  7c:	800000b7          	lui	x1,0x80000
  80:	ffff8137          	lui	x2,0xffff8
  84:	00208733          	add	x14,x1,x2
  88:	7fff83b7          	lui	x7,0x7fff8
  8c:	00700193          	addi	x3,x0,7
  90:	44771a63          	bne	x14,x7,4e4 <fail>

00000094 <test_8>:
  94:	00000093          	addi	x1,x0,0
  98:	00008137          	lui	x2,0x8
  9c:	fff10113          	addi	x2,x2,-1 # 7fff <_end+0x5fff>
  a0:	00208733          	add	x14,x1,x2
  a4:	000083b7          	lui	x7,0x8
  a8:	fff38393          	addi	x7,x7,-1 # 7fff <_end+0x5fff>
  ac:	00800193          	addi	x3,x0,8
  b0:	42771a63          	bne	x14,x7,4e4 <fail>

000000b4 <test_9>:
  b4:	800000b7          	lui	x1,0x80000
  b8:	fff08093          	addi	x1,x1,-1 # 7fffffff <_end+0x7fffdfff>
  bc:	00000113          	addi	x2,x0,0
  c0:	00208733          	add	x14,x1,x2
  c4:	800003b7          	lui	x7,0x80000
  c8:	fff38393          	addi	x7,x7,-1 # 7fffffff <_end+0x7fffdfff>
  cc:	00900193          	addi	x3,x0,9
  d0:	40771a63          	bne	x14,x7,4e4 <fail>

000000d4 <test_10>:
  d4:	800000b7          	lui	x1,0x80000
  d8:	fff08093          	addi	x1,x1,-1 # 7fffffff <_end+0x7fffdfff>
  dc:	00008137          	lui	x2,0x8
  e0:	fff10113          	addi	x2,x2,-1 # 7fff <_end+0x5fff>
  e4:	00208733          	add	x14,x1,x2
  e8:	800083b7          	lui	x7,0x80008
  ec:	ffe38393          	addi	x7,x7,-2 # 80007ffe <_end+0x80005ffe>
  f0:	00a00193          	addi	x3,x0,10
  f4:	3e771863          	bne	x14,x7,4e4 <fail>

000000f8 <test_11>:
  f8:	800000b7          	lui	x1,0x80000
  fc:	00008137          	lui	x2,0x8
 100:	fff10113          	addi	x2,x2,-1 # 7fff <_end+0x5fff>
 104:	00208733          	add	x14,x1,x2
 108:	800083b7          	lui	x7,0x80008
 10c:	fff38393          	addi	x7,x7,-1 # 80007fff <_end+0x80005fff>
 110:	00b00193          	addi	x3,x0,11
 114:	3c771863          	bne	x14,x7,4e4 <fail>

00000118 <test_12>:
 118:	800000b7          	lui	x1,0x80000
 11c:	fff08093          	addi	x1,x1,-1 # 7fffffff <_end+0x7fffdfff>
 120:	ffff8137          	lui	x2,0xffff8
 124:	00208733          	add	x14,x1,x2
 128:	7fff83b7          	lui	x7,0x7fff8
 12c:	fff38393          	addi	x7,x7,-1 # 7fff7fff <_end+0x7fff5fff>
 130:	00c00193          	addi	x3,x0,12
 134:	3a771863          	bne	x14,x7,4e4 <fail>

00000138 <test_13>:
 138:	00000093          	addi	x1,x0,0
 13c:	fff00113          	addi	x2,x0,-1
 140:	00208733          	add	x14,x1,x2
 144:	fff00393          	addi	x7,x0,-1
 148:	00d00193          	addi	x3,x0,13
 14c:	38771c63          	bne	x14,x7,4e4 <fail>

00000150 <test_14>:
 150:	fff00093          	addi	x1,x0,-1
 154:	00100113          	addi	x2,x0,1
 158:	00208733          	add	x14,x1,x2
 15c:	00000393          	addi	x7,x0,0
 160:	00e00193          	addi	x3,x0,14
 164:	38771063          	bne	x14,x7,4e4 <fail>

00000168 <test_15>:
 168:	fff00093          	addi	x1,x0,-1
 16c:	fff00113          	addi	x2,x0,-1
 170:	00208733          	add	x14,x1,x2
 174:	ffe00393          	addi	x7,x0,-2
 178:	00f00193          	addi	x3,x0,15
 17c:	36771463          	bne	x14,x7,4e4 <fail>

00000180 <test_16>:
 180:	00100093          	addi	x1,x0,1
 184:	80000137          	lui	x2,0x80000
 188:	fff10113          	addi	x2,x2,-1 # 7fffffff <_end+0x7fffdfff>
 18c:	00208733          	add	x14,x1,x2
 190:	800003b7          	lui	x7,0x80000
 194:	01000193          	addi	x3,x0,16
 198:	34771663          	bne	x14,x7,4e4 <fail>

0000019c <test_17>:
 19c:	00d00093          	addi	x1,x0,13
 1a0:	00b00113          	addi	x2,x0,11
 1a4:	002080b3          	add	x1,x1,x2
 1a8:	01800393          	addi	x7,x0,24
 1ac:	01100193          	addi	x3,x0,17
 1b0:	32709a63          	bne	x1,x7,4e4 <fail>

000001b4 <test_18>:
 1b4:	00e00093          	addi	x1,x0,14
 1b8:	00b00113          	addi	x2,x0,11
 1bc:	00208133          	add	x2,x1,x2
 1c0:	01900393          	addi	x7,x0,25
 1c4:	01200193          	addi	x3,x0,18
 1c8:	30711e63          	bne	x2,x7,4e4 <fail>

000001cc <test_19>:
 1cc:	00d00093          	addi	x1,x0,13
 1d0:	001080b3          	add	x1,x1,x1
 1d4:	01a00393          	addi	x7,x0,26
 1d8:	01300193          	addi	x3,x0,19
 1dc:	30709463          	bne	x1,x7,4e4 <fail>

000001e0 <test_20>:
 1e0:	00000213          	addi	x4,x0,0
 1e4:	00d00093          	addi	x1,x0,13
 1e8:	00b00113          	addi	x2,x0,11
 1ec:	00208733          	add	x14,x1,x2
 1f0:	00070313          	addi	x6,x14,0
 1f4:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 1f8:	00200293          	addi	x5,x0,2
 1fc:	fe5214e3          	bne	x4,x5,1e4 <test_20+0x4>
 200:	01800393          	addi	x7,x0,24
 204:	01400193          	addi	x3,x0,20
 208:	2c731e63          	bne	x6,x7,4e4 <fail>

0000020c <test_21>:
 20c:	00000213          	addi	x4,x0,0
 210:	00e00093          	addi	x1,x0,14
 214:	00b00113          	addi	x2,x0,11
 218:	00208733          	add	x14,x1,x2
 21c:	00000013          	addi	x0,x0,0
 220:	00070313          	addi	x6,x14,0
 224:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 228:	00200293          	addi	x5,x0,2
 22c:	fe5212e3          	bne	x4,x5,210 <test_21+0x4>
 230:	01900393          	addi	x7,x0,25
 234:	01500193          	addi	x3,x0,21
 238:	2a731663          	bne	x6,x7,4e4 <fail>

0000023c <test_22>:
 23c:	00000213          	addi	x4,x0,0
 240:	00f00093          	addi	x1,x0,15
 244:	00b00113          	addi	x2,x0,11
 248:	00208733          	add	x14,x1,x2
 24c:	00000013          	addi	x0,x0,0
 250:	00000013          	addi	x0,x0,0
 254:	00070313          	addi	x6,x14,0
 258:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 25c:	00200293          	addi	x5,x0,2
 260:	fe5210e3          	bne	x4,x5,240 <test_22+0x4>
 264:	01a00393          	addi	x7,x0,26
 268:	01600193          	addi	x3,x0,22
 26c:	26731c63          	bne	x6,x7,4e4 <fail>

00000270 <test_23>:
 270:	00000213          	addi	x4,x0,0
 274:	00d00093          	addi	x1,x0,13
 278:	00b00113          	addi	x2,x0,11
 27c:	00208733          	add	x14,x1,x2
 280:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 284:	00200293          	addi	x5,x0,2
 288:	fe5216e3          	bne	x4,x5,274 <test_23+0x4>
 28c:	01800393          	addi	x7,x0,24
 290:	01700193          	addi	x3,x0,23
 294:	24771863          	bne	x14,x7,4e4 <fail>

00000298 <test_24>:
 298:	00000213          	addi	x4,x0,0
 29c:	00e00093          	addi	x1,x0,14
 2a0:	00b00113          	addi	x2,x0,11
 2a4:	00000013          	addi	x0,x0,0
 2a8:	00208733          	add	x14,x1,x2
 2ac:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 2b0:	00200293          	addi	x5,x0,2
 2b4:	fe5214e3          	bne	x4,x5,29c <test_24+0x4>
 2b8:	01900393          	addi	x7,x0,25
 2bc:	01800193          	addi	x3,x0,24
 2c0:	22771263          	bne	x14,x7,4e4 <fail>

000002c4 <test_25>:
 2c4:	00000213          	addi	x4,x0,0
 2c8:	00f00093          	addi	x1,x0,15
 2cc:	00b00113          	addi	x2,x0,11
 2d0:	00000013          	addi	x0,x0,0
 2d4:	00000013          	addi	x0,x0,0
 2d8:	00208733          	add	x14,x1,x2
 2dc:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 2e0:	00200293          	addi	x5,x0,2
 2e4:	fe5212e3          	bne	x4,x5,2c8 <test_25+0x4>
 2e8:	01a00393          	addi	x7,x0,26
 2ec:	01900193          	addi	x3,x0,25
 2f0:	1e771a63          	bne	x14,x7,4e4 <fail>

000002f4 <test_26>:
 2f4:	00000213          	addi	x4,x0,0
 2f8:	00d00093          	addi	x1,x0,13
 2fc:	00000013          	addi	x0,x0,0
 300:	00b00113          	addi	x2,x0,11
 304:	00208733          	add	x14,x1,x2
 308:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 30c:	00200293          	addi	x5,x0,2
 310:	fe5214e3          	bne	x4,x5,2f8 <test_26+0x4>
 314:	01800393          	addi	x7,x0,24
 318:	01a00193          	addi	x3,x0,26
 31c:	1c771463          	bne	x14,x7,4e4 <fail>

00000320 <test_27>:
 320:	00000213          	addi	x4,x0,0
 324:	00e00093          	addi	x1,x0,14
 328:	00000013          	addi	x0,x0,0
 32c:	00b00113          	addi	x2,x0,11
 330:	00000013          	addi	x0,x0,0
 334:	00208733          	add	x14,x1,x2
 338:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 33c:	00200293          	addi	x5,x0,2
 340:	fe5212e3          	bne	x4,x5,324 <test_27+0x4>
 344:	01900393          	addi	x7,x0,25
 348:	01b00193          	addi	x3,x0,27
 34c:	18771c63          	bne	x14,x7,4e4 <fail>

00000350 <test_28>:
 350:	00000213          	addi	x4,x0,0
 354:	00f00093          	addi	x1,x0,15
 358:	00000013          	addi	x0,x0,0
 35c:	00000013          	addi	x0,x0,0
 360:	00b00113          	addi	x2,x0,11
 364:	00208733          	add	x14,x1,x2
 368:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 36c:	00200293          	addi	x5,x0,2
 370:	fe5212e3          	bne	x4,x5,354 <test_28+0x4>
 374:	01a00393          	addi	x7,x0,26
 378:	01c00193          	addi	x3,x0,28
 37c:	16771463          	bne	x14,x7,4e4 <fail>

00000380 <test_29>:
 380:	00000213          	addi	x4,x0,0
 384:	00b00113          	addi	x2,x0,11
 388:	00d00093          	addi	x1,x0,13
 38c:	00208733          	add	x14,x1,x2
 390:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 394:	00200293          	addi	x5,x0,2
 398:	fe5216e3          	bne	x4,x5,384 <test_29+0x4>
 39c:	01800393          	addi	x7,x0,24
 3a0:	01d00193          	addi	x3,x0,29
 3a4:	14771063          	bne	x14,x7,4e4 <fail>

000003a8 <test_30>:
 3a8:	00000213          	addi	x4,x0,0
 3ac:	00b00113          	addi	x2,x0,11
 3b0:	00e00093          	addi	x1,x0,14
 3b4:	00000013          	addi	x0,x0,0
 3b8:	00208733          	add	x14,x1,x2
 3bc:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 3c0:	00200293          	addi	x5,x0,2
 3c4:	fe5214e3          	bne	x4,x5,3ac <test_30+0x4>
 3c8:	01900393          	addi	x7,x0,25
 3cc:	01e00193          	addi	x3,x0,30
 3d0:	10771a63          	bne	x14,x7,4e4 <fail>

000003d4 <test_31>:
 3d4:	00000213          	addi	x4,x0,0
 3d8:	00b00113          	addi	x2,x0,11
 3dc:	00f00093          	addi	x1,x0,15
 3e0:	00000013          	addi	x0,x0,0
 3e4:	00000013          	addi	x0,x0,0
 3e8:	00208733          	add	x14,x1,x2
 3ec:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 3f0:	00200293          	addi	x5,x0,2
 3f4:	fe5212e3          	bne	x4,x5,3d8 <test_31+0x4>
 3f8:	01a00393          	addi	x7,x0,26
 3fc:	01f00193          	addi	x3,x0,31
 400:	0e771263          	bne	x14,x7,4e4 <fail>

00000404 <test_32>:
 404:	00000213          	addi	x4,x0,0
 408:	00b00113          	addi	x2,x0,11
 40c:	00000013          	addi	x0,x0,0
 410:	00d00093          	addi	x1,x0,13
 414:	00208733          	add	x14,x1,x2
 418:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 41c:	00200293          	addi	x5,x0,2
 420:	fe5214e3          	bne	x4,x5,408 <test_32+0x4>
 424:	01800393          	addi	x7,x0,24
 428:	02000193          	addi	x3,x0,32
 42c:	0a771c63          	bne	x14,x7,4e4 <fail>

00000430 <test_33>:
 430:	00000213          	addi	x4,x0,0
 434:	00b00113          	addi	x2,x0,11
 438:	00000013          	addi	x0,x0,0
 43c:	00e00093          	addi	x1,x0,14
 440:	00000013          	addi	x0,x0,0
 444:	00208733          	add	x14,x1,x2
 448:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 44c:	00200293          	addi	x5,x0,2
 450:	fe5212e3          	bne	x4,x5,434 <test_33+0x4>
 454:	01900393          	addi	x7,x0,25
 458:	02100193          	addi	x3,x0,33
 45c:	08771463          	bne	x14,x7,4e4 <fail>

00000460 <test_34>:
 460:	00000213          	addi	x4,x0,0
 464:	00b00113          	addi	x2,x0,11
 468:	00000013          	addi	x0,x0,0
 46c:	00000013          	addi	x0,x0,0
 470:	00f00093          	addi	x1,x0,15
 474:	00208733          	add	x14,x1,x2
 478:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 47c:	00200293          	addi	x5,x0,2
 480:	fe5212e3          	bne	x4,x5,464 <test_34+0x4>
 484:	01a00393          	addi	x7,x0,26
 488:	02200193          	addi	x3,x0,34
 48c:	04771c63          	bne	x14,x7,4e4 <fail>

00000490 <test_35>:
 490:	00f00093          	addi	x1,x0,15
 494:	00100133          	add	x2,x0,x1
 498:	00f00393          	addi	x7,x0,15
 49c:	02300193          	addi	x3,x0,35
 4a0:	04711263          	bne	x2,x7,4e4 <fail>

000004a4 <test_36>:
 4a4:	02000093          	addi	x1,x0,32
 4a8:	00008133          	add	x2,x1,x0
 4ac:	02000393          	addi	x7,x0,32
 4b0:	02400193          	addi	x3,x0,36
 4b4:	02711863          	bne	x2,x7,4e4 <fail>

000004b8 <test_37>:
 4b8:	000000b3          	add	x1,x0,x0
 4bc:	00000393          	addi	x7,x0,0
 4c0:	02500193          	addi	x3,x0,37
 4c4:	02709063          	bne	x1,x7,4e4 <fail>

000004c8 <test_38>:
 4c8:	01000093          	addi	x1,x0,16
 4cc:	01e00113          	addi	x2,x0,30
 4d0:	00208033          	add	x0,x1,x2
 4d4:	00000393          	addi	x7,x0,0
 4d8:	02600193          	addi	x3,x0,38
 4dc:	00701463          	bne	x0,x7,4e4 <fail>
 4e0:	00301e63          	bne	x0,x3,4fc <pass>

000004e4 <fail>:
 4e4:	00018063          	beq	x3,x0,4e4 <fail>
 4e8:	00119193          	slli	x3,x3,0x1
 4ec:	0011e193          	ori	x3,x3,1
 4f0:	05d00893          	addi	x17,x0,93
 4f4:	00018513          	addi	x10,x3,0
 4f8:	00000073          	ecall

000004fc <pass>:
 4fc:	00100193          	addi	x3,x0,1
 500:	05d00893          	addi	x17,x0,93
 504:	00000513          	addi	x10,x0,0
 508:	00000073          	ecall
 50c:	c0001073          	unimp
 510:	0000                	c.unimp
 512:	0000                	c.unimp
 514:	0000                	c.unimp
 516:	0000                	c.unimp
 518:	0000                	c.unimp
 51a:	0000                	c.unimp
 51c:	0000                	c.unimp
 51e:	0000                	c.unimp
 520:	0000                	c.unimp
 522:	0000                	c.unimp
 524:	0000                	c.unimp
 526:	0000                	c.unimp
 528:	0000                	c.unimp
 52a:	0000                	c.unimp
 52c:	0000                	c.unimp
 52e:	0000                	c.unimp
 530:	0000                	c.unimp
 532:	0000                	c.unimp
 534:	0000                	c.unimp
 536:	0000                	c.unimp
 538:	0000                	c.unimp
 53a:	0000                	c.unimp
 53c:	0000                	c.unimp
 53e:	0000                	c.unimp
 540:	0000                	c.unimp
 542:	0000                	c.unimp
