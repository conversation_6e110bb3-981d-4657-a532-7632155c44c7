// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design implementation internals
// See VminiRV_SoC.h for the primary calling header

#include "verilated.h"

#include "VminiRV_SoC___024root.h"

extern const VlUnpacked<CData/*6:0*/, 16> VminiRV_SoC__ConstPool__TABLE_h122acf83_0;
extern const VlUnpacked<CData/*0:0*/, 128> VminiRV_SoC__ConstPool__TABLE_hdd9a1d96_0;
extern const VlUnpacked<CData/*0:0*/, 128> VminiRV_SoC__ConstPool__TABLE_ha8b9bce6_0;
extern const VlUnpacked<CData/*0:0*/, 128> VminiRV_SoC__ConstPool__TABLE_hec3cfdef_0;
extern const VlUnpacked<CData/*0:0*/, 128> VminiRV_SoC__ConstPool__TABLE_h264d9dbb_0;
extern const VlUnpacked<CData/*0:0*/, 128> VminiRV_SoC__ConstPool__TABLE_h19c0cfb1_0;
extern const VlUnpacked<CData/*0:0*/, 128> VminiRV_SoC__ConstPool__TABLE_hccebc244_0;
extern const VlUnpacked<CData/*0:0*/, 128> VminiRV_SoC__ConstPool__TABLE_hbe5b3f5b_0;
extern const VlUnpacked<CData/*0:0*/, 128> VminiRV_SoC__ConstPool__TABLE_hc9e0ac50_0;

VL_ATTR_COLD void VminiRV_SoC___024root___settle__TOP__0(VminiRV_SoC___024root* vlSelf) {
    if (false && vlSelf) {}  // Prevent unused
    VminiRV_SoC__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    VL_DEBUG_IF(VL_DBG_MSGF("+    VminiRV_SoC___024root___settle__TOP__0\n"); );
    // Init
    CData/*3:0*/ __Vtableidx1;
    CData/*6:0*/ __Vtableidx2;
    // Body
    vlSelf->debug_wb_have_inst = vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__have_prev_inst;
    vlSelf->debug_wb_pc = vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_pc;
    vlSelf->debug_wb_ena = vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_rf_we;
    vlSelf->debug_wb_reg = vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_rd;
    vlSelf->debug_wb_value = vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_rf_wD;
    vlSelf->dig_en = 0U;
    vlSelf->dig_en = ((IData)(vlSelf->dig_en) | (0xffU 
                                                 & ((IData)(1U) 
                                                    << (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))));
    __Vtableidx1 = (0xfU & ((0U == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                             ? vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data
                             : ((1U == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                 ? (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                    >> 4U) : ((2U == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                               ? (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                  >> 8U)
                                               : ((3U 
                                                   == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                                   ? 
                                                  (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                   >> 0xcU)
                                                   : 
                                                  ((4U 
                                                    == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                                    ? 
                                                   (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                    >> 0x10U)
                                                    : 
                                                   ((5U 
                                                     == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                                     ? 
                                                    (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                     >> 0x14U)
                                                     : 
                                                    ((6U 
                                                      == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                                      ? 
                                                     (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                      >> 0x18U)
                                                      : 
                                                     (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                      >> 0x1cU)))))))));
    vlSelf->miniRV_SoC__DOT__U_Dig__DOT__seg_code = 
        VminiRV_SoC__ConstPool__TABLE_h122acf83_0[__Vtableidx1];
    vlSelf->miniRV_SoC__DOT__inst = vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__mem
        [(0x3fffU & (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current 
                     >> 2U))];
    __Vtableidx2 = vlSelf->miniRV_SoC__DOT__U_Dig__DOT__seg_code;
    vlSelf->DN_G0 = VminiRV_SoC__ConstPool__TABLE_hdd9a1d96_0
        [__Vtableidx2];
    vlSelf->DN_F0 = VminiRV_SoC__ConstPool__TABLE_ha8b9bce6_0
        [__Vtableidx2];
    vlSelf->DN_E0 = VminiRV_SoC__ConstPool__TABLE_hec3cfdef_0
        [__Vtableidx2];
    vlSelf->DN_D0 = VminiRV_SoC__ConstPool__TABLE_h264d9dbb_0
        [__Vtableidx2];
    vlSelf->DN_C0 = VminiRV_SoC__ConstPool__TABLE_h19c0cfb1_0
        [__Vtableidx2];
    vlSelf->DN_B0 = VminiRV_SoC__ConstPool__TABLE_hccebc244_0
        [__Vtableidx2];
    vlSelf->DN_A0 = VminiRV_SoC__ConstPool__TABLE_hbe5b3f5b_0
        [__Vtableidx2];
    vlSelf->DN_G1 = VminiRV_SoC__ConstPool__TABLE_hdd9a1d96_0
        [__Vtableidx2];
    vlSelf->DN_F1 = VminiRV_SoC__ConstPool__TABLE_ha8b9bce6_0
        [__Vtableidx2];
    vlSelf->DN_E1 = VminiRV_SoC__ConstPool__TABLE_hec3cfdef_0
        [__Vtableidx2];
    vlSelf->DN_D1 = VminiRV_SoC__ConstPool__TABLE_h264d9dbb_0
        [__Vtableidx2];
    vlSelf->DN_C1 = VminiRV_SoC__ConstPool__TABLE_h19c0cfb1_0
        [__Vtableidx2];
    vlSelf->DN_B1 = VminiRV_SoC__ConstPool__TABLE_hccebc244_0
        [__Vtableidx2];
    vlSelf->DN_A1 = VminiRV_SoC__ConstPool__TABLE_hbe5b3f5b_0
        [__Vtableidx2];
    vlSelf->DN_DP0 = VminiRV_SoC__ConstPool__TABLE_hc9e0ac50_0
        [__Vtableidx2];
    vlSelf->DN_DP1 = VminiRV_SoC__ConstPool__TABLE_hc9e0ac50_0
        [__Vtableidx2];
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_we = 0U;
    if ((0x40U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((0x20U & vlSelf->miniRV_SoC__DOT__inst)) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 4U)))) {
                if ((8U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                            if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_we = 1U;
                            }
                        }
                    }
                } else if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_we = 1U;
                        }
                    }
                }
            }
        }
    } else if ((0x20U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((0x10U & vlSelf->miniRV_SoC__DOT__inst)) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 3U)))) {
                if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_we = 1U;
                        }
                    }
                } else if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_we = 1U;
                    }
                }
            }
        } else if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                             >> 3U)))) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 2U)))) {
                if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_we = 0U;
                    }
                }
            }
        }
    } else if ((0x10U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                      >> 3U)))) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 2U)))) {
                if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_we = 1U;
                    }
                }
            }
        }
    } else if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                         >> 3U)))) {
        if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                      >> 2U)))) {
            if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_we = 1U;
                }
            }
        }
    }
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__ram_we = 0U;
    if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst >> 6U)))) {
        if ((0x20U & vlSelf->miniRV_SoC__DOT__inst)) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 4U)))) {
                if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                              >> 3U)))) {
                    if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                                  >> 2U)))) {
                        if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                            if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__ram_we = 1U;
                            }
                        }
                    }
                }
            }
        }
    }
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel = 0U;
    if ((0x40U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((0x20U & vlSelf->miniRV_SoC__DOT__inst)) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 4U)))) {
                if ((8U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                            if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel = 2U;
                            }
                        }
                    }
                } else if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel = 2U;
                        }
                    }
                }
                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
                if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                              >> 3U)))) {
                    if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                            if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
                            }
                        }
                    } else if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op 
                                = ((0U == (7U & (vlSelf->miniRV_SoC__DOT__inst 
                                                 >> 0xcU)))
                                    ? 0xaU : ((4U == 
                                               (7U 
                                                & (vlSelf->miniRV_SoC__DOT__inst 
                                                   >> 0xcU)))
                                               ? 0xbU
                                               : 0xaU));
                        }
                    }
                }
            } else {
                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
            }
        } else {
            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
        }
    } else if ((0x20U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((0x10U & vlSelf->miniRV_SoC__DOT__inst)) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 3U)))) {
                if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel = 3U;
                        }
                    }
                } else if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel = 0U;
                    }
                }
                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
                if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                              >> 2U)))) {
                    if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op 
                                = ((0x4000U & vlSelf->miniRV_SoC__DOT__inst)
                                    ? ((0x2000U & vlSelf->miniRV_SoC__DOT__inst)
                                        ? ((0x1000U 
                                            & vlSelf->miniRV_SoC__DOT__inst)
                                            ? 2U : 3U)
                                        : ((0x1000U 
                                            & vlSelf->miniRV_SoC__DOT__inst)
                                            ? ((0x40000000U 
                                                & vlSelf->miniRV_SoC__DOT__inst)
                                                ? 7U
                                                : 6U)
                                            : 4U)) : 
                                   ((0x2000U & vlSelf->miniRV_SoC__DOT__inst)
                                     ? 0U : ((0x1000U 
                                              & vlSelf->miniRV_SoC__DOT__inst)
                                              ? 5U : 
                                             ((0x40000000U 
                                               & vlSelf->miniRV_SoC__DOT__inst)
                                               ? 1U
                                               : 0U))));
                        }
                    }
                }
            } else {
                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
            }
        } else {
            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 3U)))) {
                if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                              >> 2U)))) {
                    if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
                        }
                    }
                }
            }
        }
    } else if ((0x10U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                      >> 3U)))) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 2U)))) {
                if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel = 0U;
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op 
                            = ((0x4000U & vlSelf->miniRV_SoC__DOT__inst)
                                ? ((0x2000U & vlSelf->miniRV_SoC__DOT__inst)
                                    ? ((0x1000U & vlSelf->miniRV_SoC__DOT__inst)
                                        ? 2U : 3U) : 
                                   ((0x1000U & vlSelf->miniRV_SoC__DOT__inst)
                                     ? ((0x40000000U 
                                         & vlSelf->miniRV_SoC__DOT__inst)
                                         ? 7U : 6U)
                                     : 4U)) : ((0x2000U 
                                                & vlSelf->miniRV_SoC__DOT__inst)
                                                ? 0U
                                                : (
                                                   (0x1000U 
                                                    & vlSelf->miniRV_SoC__DOT__inst)
                                                    ? 5U
                                                    : 0U)));
                    } else {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
                    }
                } else {
                    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
                }
            } else {
                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
            }
        } else {
            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
        }
    } else if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                         >> 3U)))) {
        if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                      >> 2U)))) {
            if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel = 1U;
                    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
                    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
                } else {
                    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
                }
            } else {
                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
            }
        } else {
            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
        }
    } else {
        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
    }
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1 
        = ((0U == (0x1fU & (vlSelf->miniRV_SoC__DOT__inst 
                            >> 0xfU))) ? 0U : vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers
           [(0x1fU & (vlSelf->miniRV_SoC__DOT__inst 
                      >> 0xfU))]);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alub_sel = 0U;
    if ((0x40U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((0x20U & vlSelf->miniRV_SoC__DOT__inst)) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 4U)))) {
                if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                              >> 3U)))) {
                    if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                            if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alub_sel = 1U;
                            }
                        }
                    } else if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alub_sel = 0U;
                        }
                    }
                }
            }
        }
    } else if ((0x20U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((0x10U & vlSelf->miniRV_SoC__DOT__inst)) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 3U)))) {
                if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                              >> 2U)))) {
                    if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alub_sel = 0U;
                        }
                    }
                }
            }
        } else if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                             >> 3U)))) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 2U)))) {
                if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alub_sel = 1U;
                    }
                }
            }
        }
    } else if ((0x10U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                      >> 3U)))) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 2U)))) {
                if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alub_sel = 1U;
                    }
                }
            }
        }
    } else if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                         >> 3U)))) {
        if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                      >> 2U)))) {
            if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alub_sel = 1U;
                }
            }
        }
    }
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD2 
        = ((0U == (0x1fU & (vlSelf->miniRV_SoC__DOT__inst 
                            >> 0x14U))) ? 0U : vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers
           [(0x1fU & (vlSelf->miniRV_SoC__DOT__inst 
                      >> 0x14U))]);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op = 0U;
    if ((0x40U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((0x20U & vlSelf->miniRV_SoC__DOT__inst)) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 4U)))) {
                if ((8U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                            if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op = 4U;
                            }
                        }
                    }
                } else if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op = 0U;
                        }
                    }
                } else if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op = 2U;
                    }
                }
            }
        }
    } else if ((0x20U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((0x10U & vlSelf->miniRV_SoC__DOT__inst)) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 3U)))) {
                if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op = 3U;
                        }
                    }
                }
            }
        } else if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                             >> 3U)))) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 2U)))) {
                if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op = 1U;
                    }
                }
            }
        }
    } else if ((0x10U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                      >> 3U)))) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 2U)))) {
                if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op 
                            = ((0x4000U & vlSelf->miniRV_SoC__DOT__inst)
                                ? ((0x2000U & vlSelf->miniRV_SoC__DOT__inst)
                                    ? 0U : ((0x1000U 
                                             & vlSelf->miniRV_SoC__DOT__inst)
                                             ? 5U : 0U))
                                : ((0x2000U & vlSelf->miniRV_SoC__DOT__inst)
                                    ? 0U : ((0x1000U 
                                             & vlSelf->miniRV_SoC__DOT__inst)
                                             ? 5U : 0U)));
                    }
                }
            }
        }
    } else if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                         >> 3U)))) {
        if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                      >> 2U)))) {
            if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op = 0U;
                }
            }
        }
    }
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_ext 
        = ((4U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op))
            ? ((2U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op))
                ? (((- (IData)((vlSelf->miniRV_SoC__DOT__inst 
                                >> 0x1fU))) << 0xcU) 
                   | (vlSelf->miniRV_SoC__DOT__inst 
                      >> 0x14U)) : ((1U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op))
                                     ? (0x1fU & (vlSelf->miniRV_SoC__DOT__inst 
                                                 >> 0x14U))
                                     : (((- (IData)(
                                                    (vlSelf->miniRV_SoC__DOT__inst 
                                                     >> 0x1fU))) 
                                         << 0x15U) 
                                        | ((0x100000U 
                                            & (vlSelf->miniRV_SoC__DOT__inst 
                                               >> 0xbU)) 
                                           | ((0xff000U 
                                               & vlSelf->miniRV_SoC__DOT__inst) 
                                              | ((0x800U 
                                                  & (vlSelf->miniRV_SoC__DOT__inst 
                                                     >> 9U)) 
                                                 | (0x7feU 
                                                    & (vlSelf->miniRV_SoC__DOT__inst 
                                                       >> 0x14U))))))))
            : ((2U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op))
                ? ((1U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op))
                    ? (0xfffff000U & vlSelf->miniRV_SoC__DOT__inst)
                    : (((- (IData)((vlSelf->miniRV_SoC__DOT__inst 
                                    >> 0x1fU))) << 0xdU) 
                       | ((0x1000U & (vlSelf->miniRV_SoC__DOT__inst 
                                      >> 0x13U)) | 
                          ((0x800U & (vlSelf->miniRV_SoC__DOT__inst 
                                      << 4U)) | ((0x7e0U 
                                                  & (vlSelf->miniRV_SoC__DOT__inst 
                                                     >> 0x14U)) 
                                                 | (0x1eU 
                                                    & (vlSelf->miniRV_SoC__DOT__inst 
                                                       >> 7U)))))))
                : ((1U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op))
                    ? (((- (IData)((vlSelf->miniRV_SoC__DOT__inst 
                                    >> 0x1fU))) << 0xcU) 
                       | ((0xfe0U & (vlSelf->miniRV_SoC__DOT__inst 
                                     >> 0x14U)) | (0x1fU 
                                                   & (vlSelf->miniRV_SoC__DOT__inst 
                                                      >> 7U))))
                    : (((- (IData)((vlSelf->miniRV_SoC__DOT__inst 
                                    >> 0x1fU))) << 0xcU) 
                       | (vlSelf->miniRV_SoC__DOT__inst 
                          >> 0x14U)))));
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B = 
        ((IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alub_sel)
          ? vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_ext
          : vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD2);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_f = (IData)(
                                                            ((0xaU 
                                                              == 
                                                              (0xeU 
                                                               & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))) 
                                                             & ((1U 
                                                                 & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
                                                                 ? 
                                                                VL_LTS_III(32, vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1, vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B)
                                                                 : 
                                                                (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1 
                                                                 == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B))));
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C = 
        ((8U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
          ? ((4U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
              ? (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1 
                 + vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B)
              : ((2U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
                  ? 0U : ((1U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
                           ? ((vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1 
                               < vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B)
                               ? 1U : 0U) : (VL_LTS_III(32, vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1, vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B)
                                              ? 1U : 0U))))
          : ((4U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
              ? ((2U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
                  ? ((1U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
                      ? VL_SHIFTRS_III(32,32,5, vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1, 
                                       (0x1fU & vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B))
                      : (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1 
                         >> (0x1fU & vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B)))
                  : ((1U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
                      ? (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1 
                         << (0x1fU & vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B))
                      : (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1 
                         ^ vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B)))
              : ((2U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
                  ? ((1U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
                      ? (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1 
                         | vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B)
                      : (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1 
                         & vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B))
                  : ((1U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
                      ? (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1 
                         - vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B)
                      : (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1 
                         + vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B)))));
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op = 0U;
    if ((0x40U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((0x20U & vlSelf->miniRV_SoC__DOT__inst)) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 4U)))) {
                if ((8U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                            if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op = 1U;
                            }
                        }
                    }
                } else if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op = 2U;
                        }
                    }
                } else if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op 
                            = ((0U == (7U & (vlSelf->miniRV_SoC__DOT__inst 
                                             >> 0xcU)))
                                ? ((IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_f)
                                    ? 1U : 0U) : ((4U 
                                                   == 
                                                   (7U 
                                                    & (vlSelf->miniRV_SoC__DOT__inst 
                                                       >> 0xcU)))
                                                   ? 
                                                  ((IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_f)
                                                    ? 1U
                                                    : 0U)
                                                   : 0U));
                    }
                }
            }
        }
    } else if ((0x20U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((0x10U & vlSelf->miniRV_SoC__DOT__inst)) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 3U)))) {
                if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op = 0U;
                        }
                    }
                } else if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op = 0U;
                    }
                }
            }
        } else if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                             >> 3U)))) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 2U)))) {
                if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op = 0U;
                    }
                }
            }
        }
    } else if ((0x10U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                      >> 3U)))) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 2U)))) {
                if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op = 0U;
                    }
                }
            }
        }
    } else if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                         >> 3U)))) {
        if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                      >> 2U)))) {
            if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op = 0U;
                }
            }
        }
    }
    vlSelf->miniRV_SoC__DOT__we_bridge2dig = ((IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__ram_we) 
                                              & (0xfffff000U 
                                                 == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C));
    vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_tim 
        = ((0xfffff020U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C) 
           | (0xfffff024U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C));
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_next 
        = ((0U == (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op))
            ? ((IData)(4U) + vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current)
            : ((1U == (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op))
                ? (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current 
                   + vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_ext)
                : ((2U == (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op))
                    ? (0xfffffffeU & vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                    : ((IData)(4U) + vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current))));
    vlSelf->miniRV_SoC__DOT__we_bridge2tim = ((IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__ram_we) 
                                              & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_tim));
    vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit 
        = (((0xfffffU != (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C 
                          >> 0xcU)) << 5U) | (((0xfffff000U 
                                                == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C) 
                                               << 4U) 
                                              | (((IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_tim) 
                                                  << 3U) 
                                                 | (((0xfffff060U 
                                                      == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C) 
                                                     << 2U) 
                                                    | (((0xfffff070U 
                                                         == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C) 
                                                        << 1U) 
                                                       | (0xfffff078U 
                                                          == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C))))));
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wD = 
        ((2U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel))
          ? ((1U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel))
              ? vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_ext
              : ((IData)(4U) + vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current))
          : ((1U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel))
              ? ((0x20U & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                  ? vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__mem
                 [(0x3fffU & (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C 
                              >> 2U))] : ((0x10U & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                           ? 0xffffffffU
                                           : ((8U & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                               ? ((4U 
                                                   & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                                   ? 0xffffffffU
                                                   : 
                                                  ((2U 
                                                    & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                                    ? 0xffffffffU
                                                    : 
                                                   ((1U 
                                                     & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                                     ? 0xffffffffU
                                                     : 
                                                    ((0xfffff020U 
                                                      == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                                      ? vlSelf->miniRV_SoC__DOT__U_Timer__DOT__counter0
                                                      : 
                                                     ((0xfffff024U 
                                                       == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                                       ? vlSelf->miniRV_SoC__DOT__U_Timer__DOT__threshold
                                                       : 0U)))))
                                               : ((4U 
                                                   & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                                   ? 0xffffffffU
                                                   : 
                                                  ((2U 
                                                    & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                                    ? 
                                                   ((1U 
                                                     & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                                     ? 0xffffffffU
                                                     : 
                                                    ((0xfffff070U 
                                                      == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                                      ? (IData)(vlSelf->sw)
                                                      : 0U))
                                                    : 
                                                   ((1U 
                                                     & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                                     ? 
                                                    ((0xfffff078U 
                                                      == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                                      ? (IData)(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_stable)
                                                      : 0U)
                                                     : 0xffffffffU))))))
              : vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C));
}

VL_ATTR_COLD void VminiRV_SoC___024root___initial__TOP__0(VminiRV_SoC___024root* vlSelf);

VL_ATTR_COLD void VminiRV_SoC___024root___eval_initial(VminiRV_SoC___024root* vlSelf) {
    if (false && vlSelf) {}  // Prevent unused
    VminiRV_SoC__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    VL_DEBUG_IF(VL_DBG_MSGF("+    VminiRV_SoC___024root___eval_initial\n"); );
    // Body
    VminiRV_SoC___024root___initial__TOP__0(vlSelf);
    vlSelf->__Vm_traceActivity[3U] = 1U;
    vlSelf->__Vm_traceActivity[2U] = 1U;
    vlSelf->__Vm_traceActivity[1U] = 1U;
    vlSelf->__Vm_traceActivity[0U] = 1U;
    vlSelf->__Vclklast__TOP__fpga_clk = vlSelf->fpga_clk;
    vlSelf->__Vclklast__TOP__fpga_rst = vlSelf->fpga_rst;
}

VL_ATTR_COLD void VminiRV_SoC___024root___eval_settle(VminiRV_SoC___024root* vlSelf) {
    if (false && vlSelf) {}  // Prevent unused
    VminiRV_SoC__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    VL_DEBUG_IF(VL_DBG_MSGF("+    VminiRV_SoC___024root___eval_settle\n"); );
    // Body
    VminiRV_SoC___024root___settle__TOP__0(vlSelf);
    vlSelf->__Vm_traceActivity[3U] = 1U;
    vlSelf->__Vm_traceActivity[2U] = 1U;
    vlSelf->__Vm_traceActivity[1U] = 1U;
    vlSelf->__Vm_traceActivity[0U] = 1U;
}

VL_ATTR_COLD void VminiRV_SoC___024root___final(VminiRV_SoC___024root* vlSelf) {
    if (false && vlSelf) {}  // Prevent unused
    VminiRV_SoC__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    VL_DEBUG_IF(VL_DBG_MSGF("+    VminiRV_SoC___024root___final\n"); );
}

VL_ATTR_COLD void VminiRV_SoC___024root___ctor_var_reset(VminiRV_SoC___024root* vlSelf) {
    if (false && vlSelf) {}  // Prevent unused
    VminiRV_SoC__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    VL_DEBUG_IF(VL_DBG_MSGF("+    VminiRV_SoC___024root___ctor_var_reset\n"); );
    // Body
    vlSelf->fpga_rst = VL_RAND_RESET_I(1);
    vlSelf->fpga_clk = VL_RAND_RESET_I(1);
    vlSelf->sw = VL_RAND_RESET_I(16);
    vlSelf->button = VL_RAND_RESET_I(5);
    vlSelf->dig_en = VL_RAND_RESET_I(8);
    vlSelf->DN_A0 = VL_RAND_RESET_I(1);
    vlSelf->DN_A1 = VL_RAND_RESET_I(1);
    vlSelf->DN_B0 = VL_RAND_RESET_I(1);
    vlSelf->DN_B1 = VL_RAND_RESET_I(1);
    vlSelf->DN_C0 = VL_RAND_RESET_I(1);
    vlSelf->DN_C1 = VL_RAND_RESET_I(1);
    vlSelf->DN_D0 = VL_RAND_RESET_I(1);
    vlSelf->DN_D1 = VL_RAND_RESET_I(1);
    vlSelf->DN_E0 = VL_RAND_RESET_I(1);
    vlSelf->DN_E1 = VL_RAND_RESET_I(1);
    vlSelf->DN_F0 = VL_RAND_RESET_I(1);
    vlSelf->DN_F1 = VL_RAND_RESET_I(1);
    vlSelf->DN_G0 = VL_RAND_RESET_I(1);
    vlSelf->DN_G1 = VL_RAND_RESET_I(1);
    vlSelf->DN_DP0 = VL_RAND_RESET_I(1);
    vlSelf->DN_DP1 = VL_RAND_RESET_I(1);
    vlSelf->led = VL_RAND_RESET_I(16);
    vlSelf->debug_wb_have_inst = VL_RAND_RESET_I(1);
    vlSelf->debug_wb_pc = VL_RAND_RESET_I(32);
    vlSelf->debug_wb_ena = VL_RAND_RESET_I(1);
    vlSelf->debug_wb_reg = VL_RAND_RESET_I(5);
    vlSelf->debug_wb_value = VL_RAND_RESET_I(32);
    vlSelf->miniRV_SoC__DOT__pll_lock = VL_RAND_RESET_I(1);
    vlSelf->miniRV_SoC__DOT__pll_clk = VL_RAND_RESET_I(1);
    vlSelf->miniRV_SoC__DOT__inst = VL_RAND_RESET_I(32);
    vlSelf->miniRV_SoC__DOT__we_bridge2dig = VL_RAND_RESET_I(1);
    vlSelf->miniRV_SoC__DOT__we_bridge2tim = VL_RAND_RESET_I(1);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current = VL_RAND_RESET_I(32);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_next = VL_RAND_RESET_I(32);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_pc = VL_RAND_RESET_I(32);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_rd = VL_RAND_RESET_I(5);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_rf_we = VL_RAND_RESET_I(1);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_rf_wD = VL_RAND_RESET_I(32);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__have_prev_inst = VL_RAND_RESET_I(1);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1 = VL_RAND_RESET_I(32);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD2 = VL_RAND_RESET_I(32);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wD = VL_RAND_RESET_I(32);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_we = VL_RAND_RESET_I(1);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel = VL_RAND_RESET_I(2);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B = VL_RAND_RESET_I(32);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C = VL_RAND_RESET_I(32);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_f = VL_RAND_RESET_I(1);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = VL_RAND_RESET_I(4);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alub_sel = VL_RAND_RESET_I(1);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_ext = VL_RAND_RESET_I(32);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op = VL_RAND_RESET_I(3);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op = VL_RAND_RESET_I(2);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__ram_we = VL_RAND_RESET_I(1);
    for (int __Vi0=0; __Vi0<32; ++__Vi0) {
        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[__Vi0] = VL_RAND_RESET_I(32);
    }
    vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__i = VL_RAND_RESET_I(32);
    vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__j = VL_RAND_RESET_I(32);
    vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__mem_file = 0;
    for (int __Vi0=0; __Vi0<1048576; ++__Vi0) {
        vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__mem[__Vi0] = VL_RAND_RESET_I(32);
    }
    for (int __Vi0=0; __Vi0<1048576; ++__Vi0) {
        vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__mem_rd[__Vi0] = VL_RAND_RESET_I(32);
    }
    vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_tim = VL_RAND_RESET_I(1);
    vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit = VL_RAND_RESET_I(6);
    vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__i = VL_RAND_RESET_I(32);
    vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__j = VL_RAND_RESET_I(32);
    vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__mem_file = 0;
    for (int __Vi0=0; __Vi0<1048576; ++__Vi0) {
        vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__mem[__Vi0] = VL_RAND_RESET_I(32);
    }
    for (int __Vi0=0; __Vi0<1048576; ++__Vi0) {
        vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__mem_rd[__Vi0] = VL_RAND_RESET_I(32);
    }
    vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data = VL_RAND_RESET_I(32);
    vlSelf->miniRV_SoC__DOT__U_Dig__DOT__pending_data = VL_RAND_RESET_I(32);
    vlSelf->miniRV_SoC__DOT__U_Dig__DOT__data_pending = VL_RAND_RESET_I(1);
    vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt = VL_RAND_RESET_I(3);
    vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_clk_cnt = VL_RAND_RESET_I(16);
    vlSelf->miniRV_SoC__DOT__U_Dig__DOT__update_limit_cnt = VL_RAND_RESET_I(24);
    vlSelf->miniRV_SoC__DOT__U_Dig__DOT__seg_code = VL_RAND_RESET_I(7);
    vlSelf->miniRV_SoC__DOT__U_Timer__DOT__counter0 = VL_RAND_RESET_I(32);
    vlSelf->miniRV_SoC__DOT__U_Timer__DOT__counter1 = VL_RAND_RESET_I(32);
    vlSelf->miniRV_SoC__DOT__U_Timer__DOT__threshold = VL_RAND_RESET_I(32);
    vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_sync1 = VL_RAND_RESET_I(5);
    vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_sync2 = VL_RAND_RESET_I(5);
    vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_stable = VL_RAND_RESET_I(5);
    for (int __Vi0=0; __Vi0<5; ++__Vi0) {
        vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[__Vi0] = VL_RAND_RESET_I(20);
    }
    for (int __Vi0=0; __Vi0<4; ++__Vi0) {
        vlSelf->__Vm_traceActivity[__Vi0] = VL_RAND_RESET_I(1);
    }
}
