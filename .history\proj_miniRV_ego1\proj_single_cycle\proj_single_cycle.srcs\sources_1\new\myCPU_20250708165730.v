`timescale 1ns / 1ps

`include "defines.vh"

module myCPU (
    input  wire         cpu_rst,
    input  wire         cpu_clk,

    // Interface to IROM
`ifdef RUN_TRACE
    output wire [15:0]  inst_addr,
`else
    output wire [13:0]  inst_addr,
`endif
    input  wire [31:0]  inst,
    
    // Interface to Bridge
    output wire [31:0]  Bus_addr,
    input  wire [31:0]  Bus_rdata,
    output wire         Bus_we,
    output wire [31:0]  Bus_wdata

`ifdef RUN_TRACE
    ,// Debug Interface
    output wire         debug_wb_have_inst,
    output wire [31:0]  debug_wb_pc,
    output              debug_wb_ena,
    output wire [ 4:0]  debug_wb_reg,
    output wire [31:0]  debug_wb_value
`endif
);

    // TODO: 完成你自己的单周期CPU设计
    // 内部信号定义
    wire [31:0] pc;
    wire [31:0] pc_plus_4;
    wire [31:0] pc_target;
    wire [1:0]  pc_sel;

    assign inst_addr = pc;

    PC pc_reg(
        .i_clk(cpu_clk),
        .i_rst(cpu_rst),
        .i_pc_sel(pc_sel),
        .i_pc_target(pc_target),
        .o_pc(pc),
        .o_pc_plus_4(pc_plus_4)
    );

    // instruction decode
    wire [6:0] opcode = inst[6:0];
    wire [2:0] funct3 = inst[14:12];
    wire [6:0] funct7 = inst[31:25];
    wire [4:0] rs1 = inst[19:15];
    wire [4:0] rs2 = inst[24:20];
    wire [4:0] rd = inst[11:7];

    // immediate generation
    wire [31:0] imm_i = {{21{inst[31]}}, inst[30:20]};
    wire [31:0] imm_s = {{21{inst[31]}}, inst[30:25], inst[11:7]};
    wire [31:0] imm_b = {{20{inst[31]}}, inst[7], inst[30:25], inst[11:8], 1'b0};
    wire [31:0] imm_u = {inst[31:12], 12'b0};
    wire [31:0] imm_j = {{12{inst[31]}}, inst[19:12], inst[20], inst[30:21], 1'b0};
    wire [31:0] imm_z = {27'b0, inst[19:15]};

    // control unit
    wire        br_sel;
    wire [2:0]  imm_sel;
    wire [3:0]  alu_op;
    wire        rf_we;
    wire [1:0]  wb_sel;

    Ctrl control_unit(
        .i_opcode(opcode),
        .i_funct3(funct3),
        .i_funct7(funct7),
        .o_br_sel(br_sel),
        .o_imm_sel(imm_sel),
        .o_alu_op(alu_op),
        .o_rf_we(rf_we),
        .o_wb_sel(wb_sel),
        .o_pc_sel(pc_sel)
    );

    // register file
    wire [31:0] rf_rD1;
    wire [31:0] rf_rD2;
    wire [31:0] rf_wD;

    RF reg_file(
        .i_clk(cpu_clk),
        .i_rst(cpu_rst),
        .i_rA1(rs1),
        .i_rA2(rs2),
        .i_wA(rd),
        .i_wD(rf_wD),
        .i_wE(rf_we),
        .o_rD1(rf_rD1),
        .o_rD2(rf_rD2)
    );

    // immediate selector
    wire [31:0] imm;
    assign imm = (imm_sel == 3'b000) ? imm_i :
                 (imm_sel == 3'b001) ? imm_s :
                 (imm_sel == 3'b010) ? imm_b :
                 (imm_sel == 3'b011) ? imm_u :
                 (imm_sel == 3'b100) ? imm_j :
                 (imm_sel == 3'b101) ? imm_z :
                 32'b0;

    // ALU
    wire [31:0] alu_a;
    wire [31:0] alu_b;
    wire [31:0] alu_out;
    wire        alu_br;

    assign alu_a = rf_rD1;
    assign alu_b = br_sel ? rf_rD2 : imm;

    ALU alu(
        .i_a(alu_a),
        .i_b(alu_b),
        .i_alu_op(alu_op),
        .o_out(alu_out),
        .o_br(alu_br)
    );

    // data memory interface
    assign Bus_addr = alu_out;
    assign Bus_wdata = rf_rD2;
    assign Bus_we = (opcode == `OP_STORE) ? (
        (funct3 == `F3_SB) ? 4'b0001 << alu_out[1:0] :
        (funct3 == `F3_SH) ? 4'b0011 << alu_out[1:0] :
        (funct3 == `F3_SW) ? 4'b1111 : 4'b0
    ) : 4'b0;

    // write-back selector
    assign rf_wD = (wb_sel == 2'b00) ? alu_out :
                   (wb_sel == 2'b01) ? Bus_rdata :
                   (wb_sel == 2'b10) ? pc_plus_4 :
                   32'b0;

    // next PC logic
    assign pc_target = alu_out;

`ifdef RUN_TRACE


    // 调试信息输出
    always @(posedge cpu_clk) begin
        if (!cpu_rst) begin
            $display("DEBUG: PC=%h, inst=%h, rd=%d, rf_we=%b, rf_wD=%h",
                     pc, inst, rd, rf_we, rf_wD);
        end
    end

    // Debug Interface - 直接输出当前周期的执行结果
    assign debug_wb_have_inst = !cpu_rst;              // 复位后恒为1
    assign debug_wb_pc        = pc;   // 当前执行指令的PC
    assign debug_wb_ena       = rf_we;                  // 当前指令的写使能
    assign debug_wb_reg       = rd;                     // 当前指令的目标寄存器
    assign debug_wb_value     = rf_wD;                  // 当前指令的写回值
`endif

endmodule
