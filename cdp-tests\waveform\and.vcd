$version Generated by VerilatedVcd $end
$date Tue Jul  8 10:26:37 2025 $end
$timescale 1ps $end

 $scope module TOP $end
  $var wire  1 -! DN_A0 $end
  $var wire  1 .! DN_A1 $end
  $var wire  1 /! DN_B0 $end
  $var wire  1 0! DN_B1 $end
  $var wire  1 1! DN_C0 $end
  $var wire  1 2! DN_C1 $end
  $var wire  1 3! DN_D0 $end
  $var wire  1 4! DN_D1 $end
  $var wire  1 ;! DN_DP0 $end
  $var wire  1 <! DN_DP1 $end
  $var wire  1 5! DN_E0 $end
  $var wire  1 6! DN_E1 $end
  $var wire  1 7! DN_F0 $end
  $var wire  1 8! DN_F1 $end
  $var wire  1 9! DN_G0 $end
  $var wire  1 :! DN_G1 $end
  $var wire  5 +! button [4:0] $end
  $var wire  1 @! debug_wb_ena $end
  $var wire  1 >! debug_wb_have_inst $end
  $var wire 32 ?! debug_wb_pc [31:0] $end
  $var wire  5 A! debug_wb_reg [4:0] $end
  $var wire 32 B! debug_wb_value [31:0] $end
  $var wire  8 ,! dig_en [7:0] $end
  $var wire  1 )! fpga_clk $end
  $var wire  1 (! fpga_rst $end
  $var wire 16 =! led [15:0] $end
  $var wire 16 *! sw [15:0] $end
  $scope module miniRV_SoC $end
   $var wire 32 c Bus_addr [31:0] $end
   $var wire 32 C! Bus_rdata [31:0] $end
   $var wire 32 e Bus_wdata [31:0] $end
   $var wire  1 d Bus_we $end
   $var wire  1 -! DN_A0 $end
   $var wire  1 .! DN_A1 $end
   $var wire  1 /! DN_B0 $end
   $var wire  1 0! DN_B1 $end
   $var wire  1 1! DN_C0 $end
   $var wire  1 2! DN_C1 $end
   $var wire  1 3! DN_D0 $end
   $var wire  1 4! DN_D1 $end
   $var wire  1 ;! DN_DP0 $end
   $var wire  1 <! DN_DP1 $end
   $var wire  1 5! DN_E0 $end
   $var wire  1 6! DN_E1 $end
   $var wire  1 7! DN_F0 $end
   $var wire  1 8! DN_F1 $end
   $var wire  1 9! DN_G0 $end
   $var wire  1 :! DN_G1 $end
   $var wire 32 c addr_bridge2btn [31:0] $end
   $var wire 32 c addr_bridge2dig [31:0] $end
   $var wire 32 c addr_bridge2dram [31:0] $end
   $var wire 32 c addr_bridge2led [31:0] $end
   $var wire 32 c addr_bridge2sw [31:0] $end
   $var wire 32 c addr_bridge2tim [31:0] $end
   $var wire  5 +! button [4:0] $end
   $var wire  1 )! clk_bridge2btn $end
   $var wire  1 )! clk_bridge2dig $end
   $var wire  1 )! clk_bridge2dram $end
   $var wire  1 )! clk_bridge2led $end
   $var wire  1 )! clk_bridge2sw $end
   $var wire  1 )! clk_bridge2tim $end
   $var wire  1 )! cpu_clk $end
   $var wire  1 @! debug_wb_ena $end
   $var wire  1 >! debug_wb_have_inst $end
   $var wire 32 ?! debug_wb_pc [31:0] $end
   $var wire  5 A! debug_wb_reg [4:0] $end
   $var wire 32 B! debug_wb_value [31:0] $end
   $var wire  8 ,! dig_en [7:0] $end
   $var wire  1 )! fpga_clk $end
   $var wire  1 (! fpga_rst $end
   $var wire 32 b inst [31:0] $end
   $var wire 16 a inst_addr [15:0] $end
   $var wire 16 =! led [15:0] $end
   $var wire  1 J! pll_clk $end
   $var wire  1 I! pll_lock $end
   $var wire 32 G! rdata_btn2bridge [31:0] $end
   $var wire 32 D! rdata_dram2bridge [31:0] $end
   $var wire 32 F! rdata_sw2bridge [31:0] $end
   $var wire 32 E! rdata_tim2bridge [31:0] $end
   $var wire  1 (! rst_bridge2btn $end
   $var wire  1 (! rst_bridge2dig $end
   $var wire  1 (! rst_bridge2led $end
   $var wire  1 (! rst_bridge2sw $end
   $var wire  1 (! rst_bridge2tim $end
   $var wire 16 *! sw [15:0] $end
   $var wire 32 e wdata_bridge2dig [31:0] $end
   $var wire 32 e wdata_bridge2dram [31:0] $end
   $var wire 32 e wdata_bridge2led [31:0] $end
   $var wire 32 e wdata_bridge2tim [31:0] $end
   $var wire  1 g we_bridge2dig $end
   $var wire  1 f we_bridge2dram $end
   $var wire  1 i we_bridge2led $end
   $var wire  1 h we_bridge2tim $end
   $scope module Bridge $end
    $var wire  6 p access_bit [5:0] $end
    $var wire  1 o access_btn $end
    $var wire  1 k access_dig $end
    $var wire  1 m access_led $end
    $var wire  1 j access_mem $end
    $var wire  1 n access_sw $end
    $var wire  1 l access_tim $end
    $var wire 32 c addr_from_cpu [31:0] $end
    $var wire 32 c addr_to_btn [31:0] $end
    $var wire 32 c addr_to_dig [31:0] $end
    $var wire 32 c addr_to_dram [31:0] $end
    $var wire 32 c addr_to_led [31:0] $end
    $var wire 32 c addr_to_sw [31:0] $end
    $var wire 32 c addr_to_tim [31:0] $end
    $var wire  1 )! clk_from_cpu $end
    $var wire  1 )! clk_to_btn $end
    $var wire  1 )! clk_to_dig $end
    $var wire  1 )! clk_to_dram $end
    $var wire  1 )! clk_to_led $end
    $var wire  1 )! clk_to_sw $end
    $var wire  1 )! clk_to_tim $end
    $var wire 32 G! rdata_from_btn [31:0] $end
    $var wire 32 D! rdata_from_dram [31:0] $end
    $var wire 32 F! rdata_from_sw [31:0] $end
    $var wire 32 E! rdata_from_tim [31:0] $end
    $var wire 32 C! rdata_to_cpu [31:0] $end
    $var wire  1 (! rst_from_cpu $end
    $var wire  1 (! rst_to_btn $end
    $var wire  1 (! rst_to_dig $end
    $var wire  1 (! rst_to_led $end
    $var wire  1 (! rst_to_sw $end
    $var wire  1 (! rst_to_tim $end
    $var wire 32 e wdata_from_cpu [31:0] $end
    $var wire 32 e wdata_to_dig [31:0] $end
    $var wire 32 e wdata_to_dram [31:0] $end
    $var wire 32 e wdata_to_led [31:0] $end
    $var wire 32 e wdata_to_tim [31:0] $end
    $var wire  1 d we_from_cpu $end
    $var wire  1 g we_to_dig $end
    $var wire  1 f we_to_dram $end
    $var wire  1 i we_to_led $end
    $var wire  1 h we_to_tim $end
   $upscope $end
   $scope module Core_cpu $end
    $var wire 32 c Bus_addr [31:0] $end
    $var wire 32 C! Bus_rdata [31:0] $end
    $var wire 32 e Bus_wdata [31:0] $end
    $var wire  1 d Bus_we $end
    $var wire 32 z alu_A [31:0] $end
    $var wire 32 } alu_B [31:0] $end
    $var wire 32 c alu_C [31:0] $end
    $var wire  1 ~ alu_f $end
    $var wire  4 !! alu_op [3:0] $end
    $var wire  1 "! alub_sel $end
    $var wire  1 )! cpu_clk $end
    $var wire  1 (! cpu_rst $end
    $var wire  1 @! debug_wb_ena $end
    $var wire  1 >! debug_wb_have_inst $end
    $var wire 32 ?! debug_wb_pc [31:0] $end
    $var wire  5 A! debug_wb_reg [4:0] $end
    $var wire 32 B! debug_wb_value [31:0] $end
    $var wire  3 x funct3 [2:0] $end
    $var wire  7 y funct7 [6:0] $end
    $var wire  1 - have_prev_inst $end
    $var wire 32 b inst [31:0] $end
    $var wire 16 a inst_addr [15:0] $end
    $var wire  2 %! npc_op [1:0] $end
    $var wire 32 s npc_pc4 [31:0] $end
    $var wire  7 t opcode [6:0] $end
    $var wire 32 q pc_current [31:0] $end
    $var wire 32 r pc_next [31:0] $end
    $var wire 32 s pc_plus4 [31:0] $end
    $var wire 32 ) prev_pc [31:0] $end
    $var wire  5 * prev_rd [4:0] $end
    $var wire 32 , prev_rf_wD [31:0] $end
    $var wire  1 + prev_rf_we $end
    $var wire  1 d ram_we $end
    $var wire  5 w rd [4:0] $end
    $var wire 32 z rf_rD1 [31:0] $end
    $var wire 32 e rf_rD2 [31:0] $end
    $var wire 32 H! rf_wD [31:0] $end
    $var wire  1 { rf_we $end
    $var wire  2 | rf_wsel [1:0] $end
    $var wire  5 u rs1 [4:0] $end
    $var wire  5 v rs2 [4:0] $end
    $var wire 32 #! sext_ext [31:0] $end
    $var wire  3 $! sext_op [2:0] $end
    $scope module U_ALU $end
     $var wire 32 z A [31:0] $end
     $var wire 32 } B [31:0] $end
     $var wire 32 c C [31:0] $end
     $var wire  4 !! alu_op [3:0] $end
     $var wire  1 ~ f $end
    $upscope $end
    $scope module U_Ctrl $end
     $var wire  1 ~ alu_f $end
     $var wire  4 !! alu_op [3:0] $end
     $var wire  1 "! alub_sel $end
     $var wire  3 x funct3 [2:0] $end
     $var wire  7 y funct7 [6:0] $end
     $var wire  2 %! npc_op [1:0] $end
     $var wire  7 t opcode [6:0] $end
     $var wire  1 d ram_we $end
     $var wire  1 { rf_we $end
     $var wire  2 | rf_wsel [1:0] $end
     $var wire  3 $! sext_op [2:0] $end
    $upscope $end
    $scope module U_NPC $end
     $var wire 32 c ALU_C [31:0] $end
     $var wire 32 #! IMM [31:0] $end
     $var wire 32 q PC [31:0] $end
     $var wire 32 r npc [31:0] $end
     $var wire  2 %! npc_op [1:0] $end
     $var wire 32 s pc4 [31:0] $end
    $upscope $end
    $scope module U_PC $end
     $var wire  1 )! clk $end
     $var wire 32 r din [31:0] $end
     $var wire 32 q pc [31:0] $end
     $var wire  1 (! rst $end
    $upscope $end
    $scope module U_RF $end
     $var wire  1 )! clk $end
     $var wire 32 z rD1 [31:0] $end
     $var wire 32 e rD2 [31:0] $end
     $var wire  5 u rR1 [4:0] $end
     $var wire  5 v rR2 [4:0] $end
     $var wire 32 A registers[0] [31:0] $end
     $var wire 32 K registers[10] [31:0] $end
     $var wire 32 L registers[11] [31:0] $end
     $var wire 32 M registers[12] [31:0] $end
     $var wire 32 N registers[13] [31:0] $end
     $var wire 32 O registers[14] [31:0] $end
     $var wire 32 P registers[15] [31:0] $end
     $var wire 32 Q registers[16] [31:0] $end
     $var wire 32 R registers[17] [31:0] $end
     $var wire 32 S registers[18] [31:0] $end
     $var wire 32 T registers[19] [31:0] $end
     $var wire 32 B registers[1] [31:0] $end
     $var wire 32 U registers[20] [31:0] $end
     $var wire 32 V registers[21] [31:0] $end
     $var wire 32 W registers[22] [31:0] $end
     $var wire 32 X registers[23] [31:0] $end
     $var wire 32 Y registers[24] [31:0] $end
     $var wire 32 Z registers[25] [31:0] $end
     $var wire 32 [ registers[26] [31:0] $end
     $var wire 32 \ registers[27] [31:0] $end
     $var wire 32 ] registers[28] [31:0] $end
     $var wire 32 ^ registers[29] [31:0] $end
     $var wire 32 C registers[2] [31:0] $end
     $var wire 32 _ registers[30] [31:0] $end
     $var wire 32 ` registers[31] [31:0] $end
     $var wire 32 D registers[3] [31:0] $end
     $var wire 32 E registers[4] [31:0] $end
     $var wire 32 F registers[5] [31:0] $end
     $var wire 32 G registers[6] [31:0] $end
     $var wire 32 H registers[7] [31:0] $end
     $var wire 32 I registers[8] [31:0] $end
     $var wire 32 J registers[9] [31:0] $end
     $var wire 32 H! wD [31:0] $end
     $var wire  5 w wR [4:0] $end
     $var wire  1 { we $end
    $upscope $end
    $scope module U_SEXT $end
     $var wire 32 #! ext [31:0] $end
     $var wire 32 b inst [31:0] $end
     $var wire  3 $! sext_op [2:0] $end
    $upscope $end
   $upscope $end
   $scope module Mem_DRAM $end
    $var wire 32 K! ADDR_BITS [31:0] $end
    $var wire 16 &! a [15:0] $end
    $var wire  1 )! clk $end
    $var wire 32 e d [31:0] $end
    $var wire 32 # i [31:0] $end
    $var wire 32 $ j [31:0] $end
    $var wire 32 % mem_file [31:0] $end
    $var wire 32 D! spo [31:0] $end
    $var wire  1 f we $end
   $upscope $end
   $scope module Mem_IROM $end
    $var wire 32 K! ADDR_BITS [31:0] $end
    $var wire 16 '! a [15:0] $end
    $var wire 32 & i [31:0] $end
    $var wire 32 ' j [31:0] $end
    $var wire 32 ( mem_file [31:0] $end
    $var wire 32 b spo [31:0] $end
   $upscope $end
   $scope module U_Button $end
    $var wire 32 c addr [31:0] $end
    $var wire  5 +! button [4:0] $end
    $var wire  5 0 button_stable [4:0] $end
    $var wire  5 . button_sync1 [4:0] $end
    $var wire  5 / button_sync2 [4:0] $end
    $var wire  1 )! clk $end
    $var wire 20 1 debounce_cnt[0] [19:0] $end
    $var wire 20 2 debounce_cnt[1] [19:0] $end
    $var wire 20 3 debounce_cnt[2] [19:0] $end
    $var wire 20 4 debounce_cnt[3] [19:0] $end
    $var wire 20 5 debounce_cnt[4] [19:0] $end
    $var wire 32 G! rdata [31:0] $end
    $var wire  1 (! rst $end
   $upscope $end
   $scope module U_Dig $end
    $var wire  1 -! DN_A0 $end
    $var wire  1 .! DN_A1 $end
    $var wire  1 /! DN_B0 $end
    $var wire  1 0! DN_B1 $end
    $var wire  1 1! DN_C0 $end
    $var wire  1 2! DN_C1 $end
    $var wire  1 3! DN_D0 $end
    $var wire  1 4! DN_D1 $end
    $var wire  1 ;! DN_DP0 $end
    $var wire  1 <! DN_DP1 $end
    $var wire  1 5! DN_E0 $end
    $var wire  1 6! DN_E1 $end
    $var wire  1 7! DN_F0 $end
    $var wire  1 8! DN_F1 $end
    $var wire  1 9! DN_G0 $end
    $var wire  1 :! DN_G1 $end
    $var wire 32 c addr [31:0] $end
    $var wire  1 )! clk $end
    $var wire  4 < current_digit [3:0] $end
    $var wire  1 8 data_pending $end
    $var wire  8 ,! dig_en [7:0] $end
    $var wire 32 6 display_data [31:0] $end
    $var wire 32 7 pending_data [31:0] $end
    $var wire  1 (! rst $end
    $var wire 16 : scan_clk_cnt [15:0] $end
    $var wire  3 9 scan_cnt [2:0] $end
    $var wire  7 = seg_code [6:0] $end
    $var wire 24 ; update_limit_cnt [23:0] $end
    $var wire 32 e wdata [31:0] $end
    $var wire  1 g we $end
   $upscope $end
   $scope module U_Led $end
    $var wire 32 c addr [31:0] $end
    $var wire  1 )! clk $end
    $var wire 16 =! led [15:0] $end
    $var wire  1 (! rst $end
    $var wire 32 e wdata [31:0] $end
    $var wire  1 i we $end
   $upscope $end
   $scope module U_Switch $end
    $var wire 32 c addr [31:0] $end
    $var wire  1 )! clk $end
    $var wire 32 F! rdata [31:0] $end
    $var wire  1 (! rst $end
    $var wire 16 *! sw [15:0] $end
   $upscope $end
   $scope module U_Timer $end
    $var wire 32 c addr [31:0] $end
    $var wire  1 )! clk $end
    $var wire 32 > counter0 [31:0] $end
    $var wire 32 ? counter1 [31:0] $end
    $var wire 32 E! rdata [31:0] $end
    $var wire  1 (! rst $end
    $var wire 32 @ threshold [31:0] $end
    $var wire 32 e wdata [31:0] $end
    $var wire  1 h we $end
   $upscope $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#9
b00000000000100000000000000000000 #
b00000000000100000000000000000000 $
b10000000000000000000000000101100 %
b00000000000100000000000000000000 &
b00000000000100000000000000000000 '
b10000000000000000000000000101011 (
b00000000000000000000000000000000 )
b00000 *
0+
b00000000000000000000000000000000 ,
0-
b00000 .
b00000 /
b00000 0
b00000000000000000000 1
b00000000000000000000 2
b00000000000000000000 3
b00000000000000000000 4
b00000000000000000000 5
b00000000000000000000000000000000 6
b00000000000000000000000000000000 7
08
b000 9
b0000000000000000 :
b000000000000000000000000 ;
b0000 <
b0111111 =
b00000000000000000000000000000000 >
b00000000000000000000000000000000 ?
b00000000000000000000000000000000 @
b00000000000000000000000000000000 A
b00000000000000000000000000000000 B
b00000000000000000000000000000000 C
b00000000000000000000000000000000 D
b00000000000000000000000000000000 E
b00000000000000000000000000000000 F
b00000000000000000000000000000000 G
b00000000000000000000000000000000 H
b00000000000000000000000000000000 I
b00000000000000000000000000000000 J
b00000000000000000000000000000000 K
b00000000000000000000000000000000 L
b00000000000000000000000000000000 M
b00000000000000000000000000000000 N
b00000000000000000000000000000000 O
b00000000000000000000000000000000 P
b00000000000000000000000000000000 Q
b00000000000000000000000000000000 R
b00000000000000000000000000000000 S
b00000000000000000000000000000000 T
b00000000000000000000000000000000 U
b00000000000000000000000000000000 V
b00000000000000000000000000000000 W
b00000000000000000000000000000000 X
b00000000000000000000000000000000 Y
b00000000000000000000000000000000 Z
b00000000000000000000000000000000 [
b00000000000000000000000000000000 \
b00000000000000000000000000000000 ]
b00000000000000000000000000000000 ^
b00000000000000000000000000000000 _
b00000000000000000000000000000000 `
b0000000000000000 a
b00000000010000000000000001101111 b
b00000000000000000000000000000000 c
0d
b00000000000000000000000000000000 e
0f
0g
0h
0i
1j
0k
0l
0m
0n
0o
b100000 p
b00000000000000000000000000000000 q
b00000000000000000000000000000100 r
b00000000000000000000000000000100 s
b1101111 t
b00000 u
b00100 v
b00000 w
b000 x
b0000000 y
b00000000000000000000000000000000 z
1{
b10 |
b00000000000000000000000000000000 }
0~
b0000 !!
0"!
b00000000000000000000000000000100 #!
b100 $!
b01 %!
b0000000000000000 &!
b0000000000000000 '!
1(!
0)!
b0000000000000000 *!
b00000 +!
b00000001 ,!
1-!
1.!
1/!
10!
11!
12!
13!
14!
15!
16!
17!
18!
09!
0:!
0;!
0<!
b0000000000000000 =!
0>!
b00000000000000000000000000000000 ?!
0@!
b00000 A!
b00000000000000000000000000000000 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 E!
b00000000000000000000000000000000 F!
b00000000000000000000000000000000 G!
b00000000000000000000000000000100 H!
0I!
0J!
b00000000000000000000000000010000 K!
#10
b00000001011111010111100001000000 @
1)!
#15
0)!
#19
#20
1)!
#25
0)!
#29
#30
1)!
#35
0)!
#39
#40
1)!
#45
0)!
#49
#50
1)!
#55
0)!
#59
#60
1)!
#65
0)!
#69
#70
1)!
#75
0)!
#79
#80
1)!
#85
0)!
#89
#90
1)!
#95
0)!
#99
#100
1)!
#105
0)!
#109
#110
1)!
#115
0)!
#119
#120
1)!
#125
0)!
#129
#130
1)!
#135
0)!
#139
#140
1)!
#145
0)!
#149
#150
1)!
#155
0)!
#159
#160
1)!
#165
0)!
#169
#170
1)!
#175
0)!
#179
#180
1)!
#185
0)!
#189
#190
1)!
#195
0)!
#199
#200
1)!
#205
0)!
#209
0(!
#210
1+
b00000000000000000000000000000100 ,
1-
b0000000000000001 :
b000000000000000000000001 ;
b00000000000000000000000000000001 ?
b0000000000000100 a
b11111111000000010000000010110111 b
b00000000000000000000000000000100 q
b00000000000000000000000000001000 r
b00000000000000000000000000001000 s
b0110111 t
b00010 u
b10000 v
b00001 w
b1111111 y
b11 |
b11111111000000010000000000000000 #!
b011 $!
b00 %!
b0000000000000001 '!
1)!
1>!
1@!
b00000000000000000000000000000100 B!
b11111111000000010000000000000000 H!
#215
0)!
#219
#220
b00000000000000000000000000000100 )
b00001 *
b11111111000000010000000000000000 ,
b0000000000000010 :
b000000000000000000000010 ;
b00000000000000000000000000000010 ?
b11111111000000010000000000000000 B
b0000000000001000 a
b11110000000000001000000010010011 b
b11111111000000001111111100000000 c
b00000000000000000000000000001000 q
b00000000000000000000000000001100 r
b00000000000000000000000000001100 s
b0010011 t
b00001 u
b00000 v
b1111000 y
b11111111000000010000000000000000 z
b00 |
b11111111111111111111111100000000 }
1"!
b11111111111111111111111100000000 #!
b000 $!
b0011111111000000 &!
b0000000000000010 '!
1)!
b00000000000000000000000000000100 ?!
b00001 A!
b11111111000000010000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b11111111000000001111111100000000 H!
#225
0)!
#229
#230
b00000000000000000000000000001000 )
b11111111000000001111111100000000 ,
b0000000000000011 :
b000000000000000000000011 ;
b00000000000000000000000000000011 ?
b11111111000000001111111100000000 B
b0000000000001100 a
b00001111000011110001000100110111 b
b00000000000000000000000000000000 c
b00000000000000000000000000001100 q
b00000000000000000000000000010000 r
b00000000000000000000000000010000 s
b0110111 t
b11110 u
b10000 v
b00010 w
b001 x
b0000111 y
b00000000000000000000000000000000 z
b11 |
b00000000000000000000000000000000 }
0"!
b00001111000011110001000000000000 #!
b011 $!
b0000000000000000 &!
b0000000000000011 '!
1)!
b00000000000000000000000000001000 ?!
b11111111000000001111111100000000 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00001111000011110001000000000000 H!
#235
0)!
#239
#240
b00000000000000000000000000001100 )
b00010 *
b00001111000011110001000000000000 ,
b0000000000000100 :
b000000000000000000000100 ;
b00000000000000000000000000000100 ?
b00001111000011110001000000000000 C
b0000000000010000 a
b11110000111100010000000100010011 b
b00001111000011110000111100001111 c
b00000000000000000000000000010000 q
b00000000000000000000000000010100 r
b00000000000000000000000000010100 s
b0010011 t
b00010 u
b01111 v
b000 x
b1111000 y
b00001111000011110001000000000000 z
b00 |
b11111111111111111111111100001111 }
1"!
b11111111111111111111111100001111 #!
b000 $!
b0000001111000011 &!
b0000000000000100 '!
1)!
b00000000000000000000000000001100 ?!
b00010 A!
b00001111000011110001000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b00001111000011110000111100001111 H!
#245
0)!
#249
#250
b00000000000000000000000000010000 )
b00001111000011110000111100001111 ,
b0000000000000101 :
b000000000000000000000101 ;
b00000000000000000000000000000101 ?
b00001111000011110000111100001111 C
b0000000000010100 a
b00000000001000001111011100110011 b
b00001111000000000000111100000000 c
b00001111000011110000111100001111 e
b00000000000000000000000000010100 q
b00000000000000000000000000011000 r
b00000000000000000000000000011000 s
b0110011 t
b00001 u
b00010 v
b01110 w
b111 x
b0000000 y
b11111111000000001111111100000000 z
b00001111000011110000111100001111 }
b0010 !!
0"!
b00000000000000000000000000000010 #!
b0000001111000000 &!
b0000000000000101 '!
1)!
b00000000000000000000000000010000 ?!
b00001111000011110000111100001111 B!
b00001111000000000000111100000000 H!
#255
0)!
#259
#260
b00000000000000000000000000010100 )
b01110 *
b00001111000000000000111100000000 ,
b0000000000000110 :
b000000000000000000000110 ;
b00000000000000000000000000000110 ?
b00001111000000000000111100000000 O
b0000000000011000 a
b00001111000000000001001110110111 b
b00000000000000000000000000000000 c
b00000000000000000000000000000000 e
b00000000000000000000000000011000 q
b00000000000000000000000000011100 r
b00000000000000000000000000011100 s
b0110111 t
b00000 u
b10000 v
b00111 w
b001 x
b0000111 y
b00000000000000000000000000000000 z
b11 |
b00000000000000000000000000000000 }
b0000 !!
b00001111000000000001000000000000 #!
b011 $!
b0000000000000000 &!
b0000000000000110 '!
1)!
b00000000000000000000000000010100 ?!
b01110 A!
b00001111000000000000111100000000 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00001111000000000001000000000000 H!
#265
0)!
#269
#270
b00000000000000000000000000011000 )
b00111 *
b00001111000000000001000000000000 ,
b0000000000000111 :
b000000000000000000000111 ;
b00000000000000000000000000000111 ?
b00001111000000000001000000000000 H
b0000000000011100 a
b11110000000000111000001110010011 b
b00001111000000000000111100000000 c
b00000000000000000000000000011100 q
b00000000000000000000000000100000 r
b00000000000000000000000000100000 s
b0010011 t
b00111 u
b00000 v
b000 x
b1111000 y
b00001111000000000001000000000000 z
b00 |
b11111111111111111111111100000000 }
1"!
b11111111111111111111111100000000 #!
b000 $!
b0000001111000000 &!
b0000000000000111 '!
1)!
b00000000000000000000000000011000 ?!
b00111 A!
b00001111000000000001000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b00001111000000000000111100000000 H!
#275
0)!
#279
#280
b00000000000000000000000000011100 )
b00001111000000000000111100000000 ,
b0000000000001000 :
b000000000000000000001000 ;
b00000000000000000000000000001000 ?
b00001111000000000000111100000000 H
b0000000000100000 a
b00000000001000000000000110010011 b
b00000000000000000000000000000010 c
b00001111000011110000111100001111 e
b00000000000000000000000000100000 q
b00000000000000000000000000100100 r
b00000000000000000000000000100100 s
b00000 u
b00010 v
b00011 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000000010 }
b00000000000000000000000000000010 #!
b0000000000000000 &!
b0000000000001000 '!
1)!
b00000000000000000000000000011100 ?!
b00001111000000000000111100000000 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000010 H!
#285
0)!
#289
#290
b00000000000000000000000000100000 )
b00011 *
b00000000000000000000000000000010 ,
b0000000000001001 :
b000000000000000000001001 ;
b00000000000000000000000000001001 ?
b00000000000000000000000000000010 D
b0000000000100100 a
b01001000011101110001110001100011 b
b00000000000000000000000000000000 c
b00001111000000000000111100000000 e
b00000000000000000000000000100100 q
b00000000000000000000000000101000 r
b00000000000000000000000000101000 s
b1100011 t
b01110 u
b00111 v
b11000 w
b001 x
b0100100 y
b00001111000000000000111100000000 z
0{
b00001111000000000000111100000000 }
1~
b1010 !!
0"!
b00000000000000000000010010011000 #!
b010 $!
b0000000000001001 '!
1)!
b00000000000000000000000000100000 ?!
b00011 A!
b00000000000000000000000000000010 B!
b00000000000000000000000000000000 H!
#295
0)!
#299
#300
b00000000000000000000000000100100 )
b11000 *
0+
b00000000000000000000000000000000 ,
b0000000000001010 :
b000000000000000000001010 ;
b00000000000000000000000000001010 ?
b0000000000101000 a
b00001111111100000001000010110111 b
b00000000000000000000000000000000 e
b00000000000000000000000000101000 q
b00000000000000000000000000101100 r
b00000000000000000000000000101100 s
b0110111 t
b00000 u
b11111 v
b00001 w
b0000111 y
b00000000000000000000000000000000 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b00001111111100000001000000000000 #!
b011 $!
b0000000000001010 '!
1)!
b00000000000000000000000000100100 ?!
0@!
b11000 A!
b00000000000000000000000000000000 B!
b00001111111100000001000000000000 H!
#305
0)!
#309
#310
b00000000000000000000000000101000 )
b00001 *
1+
b00001111111100000001000000000000 ,
b0000000000001011 :
b000000000000000000001011 ;
b00000000000000000000000000001011 ?
b00001111111100000001000000000000 B
b0000000000101100 a
b11111111000000001000000010010011 b
b00001111111100000000111111110000 c
b00000000000000000000000000101100 q
b00000000000000000000000000110000 r
b00000000000000000000000000110000 s
b0010011 t
b00001 u
b10000 v
b000 x
b1111111 y
b00001111111100000001000000000000 z
b00 |
b11111111111111111111111111110000 }
1"!
b11111111111111111111111111110000 #!
b000 $!
b0000001111111100 &!
b0000000000001011 '!
1)!
b00000000000000000000000000101000 ?!
1@!
b00001 A!
b00001111111100000001000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b00001111111100000000111111110000 H!
#315
0)!
#319
#320
b00000000000000000000000000101100 )
b00001111111100000000111111110000 ,
b0000000000001100 :
b000000000000000000001100 ;
b00000000000000000000000000001100 ?
b00001111111100000000111111110000 B
b0000000000110000 a
b11110000111100001111000100110111 b
b00000000000000000000000000110000 q
b00000000000000000000000000110100 r
b00000000000000000000000000110100 s
b0110111 t
b01111 v
b00010 w
b111 x
b1111000 y
b00001111111100000000111111110000 z
b11 |
b00000000000000000000000000000000 }
0"!
b11110000111100001111000000000000 #!
b011 $!
b0000000000001100 '!
1)!
b00000000000000000000000000101100 ?!
b00001111111100000000111111110000 B!
b11110000111100001111000000000000 H!
#325
0)!
#329
#330
b00000000000000000000000000110000 )
b00010 *
b11110000111100001111000000000000 ,
b0000000000001101 :
b000000000000000000001101 ;
b00000000000000000000000000001101 ?
b11110000111100001111000000000000 C
b0000000000110100 a
b00001111000000010000000100010011 b
b11110000111100001111000011110000 c
b00000000000000000000000000110100 q
b00000000000000000000000000111000 r
b00000000000000000000000000111000 s
b0010011 t
b00010 u
b10000 v
b000 x
b0000111 y
b11110000111100001111000000000000 z
b00 |
b00000000000000000000000011110000 }
1"!
b00000000000000000000000011110000 #!
b000 $!
b0011110000111100 &!
b0000000000001101 '!
1)!
b00000000000000000000000000110000 ?!
b00010 A!
b11110000111100001111000000000000 B!
b11110000111100001111000011110000 H!
#335
0)!
#339
#340
b00000000000000000000000000110100 )
b11110000111100001111000011110000 ,
b0000000000001110 :
b000000000000000000001110 ;
b00000000000000000000000000001110 ?
b11110000111100001111000011110000 C
b0000000000111000 a
b00000000001000001111011100110011 b
b00000000111100000000000011110000 c
b11110000111100001111000011110000 e
b00000000000000000000000000111000 q
b00000000000000000000000000111100 r
b00000000000000000000000000111100 s
b0110011 t
b00001 u
b00010 v
b01110 w
b111 x
b0000000 y
b00001111111100000000111111110000 z
b11110000111100001111000011110000 }
b0010 !!
0"!
b00000000000000000000000000000010 #!
b0000000000111100 &!
b0000000000001110 '!
1)!
b00000000000000000000000000110100 ?!
b11110000111100001111000011110000 B!
b00111100011100001001011001100011 C!
b00111100011100001001011001100011 D!
b00000000111100000000000011110000 H!
#345
0)!
#349
#350
b00000000000000000000000000111000 )
b01110 *
b00000000111100000000000011110000 ,
b0000000000001111 :
b000000000000000000001111 ;
b00000000000000000000000000001111 ?
b00000000111100000000000011110000 O
b0000000000111100 a
b00000000111100000000001110110111 b
b00000000000000000000000000000000 c
b00000000000000000000000000000000 e
b00000000000000000000000000111100 q
b00000000000000000000000001000000 r
b00000000000000000000000001000000 s
b0110111 t
b00000 u
b01111 v
b00111 w
b000 x
b00000000000000000000000000000000 z
b11 |
b00000000000000000000000000000000 }
b0000 !!
b00000000111100000000000000000000 #!
b011 $!
b0000000000000000 &!
b0000000000001111 '!
1)!
b00000000000000000000000000111000 ?!
b01110 A!
b00000000111100000000000011110000 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000111100000000000000000000 H!
#355
0)!
#359
#360
b00000000000000000000000000111100 )
b00111 *
b00000000111100000000000000000000 ,
b0000000000010000 :
b000000000000000000010000 ;
b00000000000000000000000000010000 ?
b00000000111100000000000000000000 H
b0000000001000000 a
b00001111000000111000001110010011 b
b00000000111100000000000011110000 c
b00000000000000000000000001000000 q
b00000000000000000000000001000100 r
b00000000000000000000000001000100 s
b0010011 t
b00111 u
b10000 v
b0000111 y
b00000000111100000000000000000000 z
b00 |
b00000000000000000000000011110000 }
1"!
b00000000000000000000000011110000 #!
b000 $!
b0000000000111100 &!
b0000000000010000 '!
1)!
b00000000000000000000000000111100 ?!
b00111 A!
b00000000111100000000000000000000 B!
b00111100011100001001011001100011 C!
b00111100011100001001011001100011 D!
b00000000111100000000000011110000 H!
#365
0)!
#369
#370
b00000000000000000000000001000000 )
b00000000111100000000000011110000 ,
b0000000000010001 :
b000000000000000000010001 ;
b00000000000000000000000000010001 ?
b00000000111100000000000011110000 H
b0000000001000100 a
b00000000001100000000000110010011 b
b00000000000000000000000000000011 c
b00000000000000000000000000000010 e
b00000000000000000000000001000100 q
b00000000000000000000000001001000 r
b00000000000000000000000001001000 s
b00000 u
b00011 v
b00011 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000000011 }
b00000000000000000000000000000011 #!
b0000000000000000 &!
b0000000000010001 '!
1)!
b00000000000000000000000001000000 ?!
b00000000111100000000000011110000 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000011 H!
#375
0)!
#379
#380
b00000000000000000000000001000100 )
b00011 *
b00000000000000000000000000000011 ,
b0000000000010010 :
b000000000000000000010010 ;
b00000000000000000000000000010010 ?
b00000000000000000000000000000011 D
b0000000001001000 a
b01000110011101110001101001100011 b
b00000000000000000000000000000000 c
b00000000111100000000000011110000 e
b00000000000000000000000001001000 q
b00000000000000000000000001001100 r
b00000000000000000000000001001100 s
b1100011 t
b01110 u
b00111 v
b10100 w
b001 x
b0100011 y
b00000000111100000000000011110000 z
0{
b00000000111100000000000011110000 }
1~
b1010 !!
0"!
b00000000000000000000010001110100 #!
b010 $!
b0000000000010010 '!
1)!
b00000000000000000000000001000100 ?!
b00011 A!
b00000000000000000000000000000011 B!
b00000000000000000000000000000000 H!
#385
0)!
#389
#390
b00000000000000000000000001001000 )
b10100 *
0+
b00000000000000000000000000000000 ,
b0000000000010011 :
b000000000000000000010011 ;
b00000000000000000000000000010011 ?
b0000000001001100 a
b00000000111111110000000010110111 b
b00000000000000000000000000000000 e
b00000000000000000000000001001100 q
b00000000000000000000000001010000 r
b00000000000000000000000001010000 s
b0110111 t
b11110 u
b01111 v
b00001 w
b000 x
b0000000 y
b00000000000000000000000000000000 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b00000000111111110000000000000000 #!
b011 $!
b0000000000010011 '!
1)!
b00000000000000000000000001001000 ?!
0@!
b10100 A!
b00000000000000000000000000000000 B!
b00000000111111110000000000000000 H!
#395
0)!
#399
#400
b00000000000000000000000001001100 )
b00001 *
1+
b00000000111111110000000000000000 ,
b0000000000010100 :
b000000000000000000010100 ;
b00000000000000000000000000010100 ?
b00000000111111110000000000000000 B
b0000000001010000 a
b00001111111100001000000010010011 b
b00000000111111110000000011111111 c
b00000000000000000000000001010000 q
b00000000000000000000000001010100 r
b00000000000000000000000001010100 s
b0010011 t
b00001 u
b11111 v
b0000111 y
b00000000111111110000000000000000 z
b00 |
b00000000000000000000000011111111 }
1"!
b00000000000000000000000011111111 #!
b000 $!
b0000000000111111 &!
b0000000000010100 '!
1)!
b00000000000000000000000001001100 ?!
1@!
b00001 A!
b00000000111111110000000000000000 B!
b11110000000000001000000010010011 C!
b11110000000000001000000010010011 D!
b00000000111111110000000011111111 H!
#405
0)!
#409
#410
b00000000000000000000000001010000 )
b00000000111111110000000011111111 ,
b0000000000010101 :
b000000000000000000010101 ;
b00000000000000000000000000010101 ?
b00000000111111110000000011111111 B
b0000000001010100 a
b00001111000011110001000100110111 b
b00000000000000000000000000000000 c
b00000000000000000000000001010100 q
b00000000000000000000000001011000 r
b00000000000000000000000001011000 s
b0110111 t
b11110 u
b10000 v
b00010 w
b001 x
b00000000000000000000000000000000 z
b11 |
b00000000000000000000000000000000 }
0"!
b00001111000011110001000000000000 #!
b011 $!
b0000000000000000 &!
b0000000000010101 '!
1)!
b00000000000000000000000001010000 ?!
b00000000111111110000000011111111 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00001111000011110001000000000000 H!
#415
0)!
#419
#420
b00000000000000000000000001010100 )
b00010 *
b00001111000011110001000000000000 ,
b0000000000010110 :
b000000000000000000010110 ;
b00000000000000000000000000010110 ?
b00001111000011110001000000000000 C
b0000000001011000 a
b11110000111100010000000100010011 b
b00001111000011110000111100001111 c
b00000000000000000000000001011000 q
b00000000000000000000000001011100 r
b00000000000000000000000001011100 s
b0010011 t
b00010 u
b01111 v
b000 x
b1111000 y
b00001111000011110001000000000000 z
b00 |
b11111111111111111111111100001111 }
1"!
b11111111111111111111111100001111 #!
b000 $!
b0000001111000011 &!
b0000000000010110 '!
1)!
b00000000000000000000000001010100 ?!
b00010 A!
b00001111000011110001000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b00001111000011110000111100001111 H!
#425
0)!
#429
#430
b00000000000000000000000001011000 )
b00001111000011110000111100001111 ,
b0000000000010111 :
b000000000000000000010111 ;
b00000000000000000000000000010111 ?
b00001111000011110000111100001111 C
b0000000001011100 a
b00000000001000001111011100110011 b
b00000000000011110000000000001111 c
b00001111000011110000111100001111 e
b00000000000000000000000001011100 q
b00000000000000000000000001100000 r
b00000000000000000000000001100000 s
b0110011 t
b00001 u
b00010 v
b01110 w
b111 x
b0000000 y
b00000000111111110000000011111111 z
b00001111000011110000111100001111 }
b0010 !!
0"!
b00000000000000000000000000000010 #!
b0000000000000011 &!
b0000000000010111 '!
1)!
b00000000000000000000000001011000 ?!
b00001111000011110000111100001111 B!
b00001111000011110001000100110111 C!
b00001111000011110001000100110111 D!
b00000000000011110000000000001111 H!
#435
0)!
#439
#440
b00000000000000000000000001011100 )
b01110 *
b00000000000011110000000000001111 ,
b0000000000011000 :
b000000000000000000011000 ;
b00000000000000000000000000011000 ?
b00000000000011110000000000001111 O
b0000000001100000 a
b00000000000011110000001110110111 b
b00000000000000000000000000000000 c
b00000000000000000000000000000000 e
b00000000000000000000000001100000 q
b00000000000000000000000001100100 r
b00000000000000000000000001100100 s
b0110111 t
b11110 u
b00000 v
b00111 w
b000 x
b00000000000000000000000000000000 z
b11 |
b00000000000000000000000000000000 }
b0000 !!
b00000000000011110000000000000000 #!
b011 $!
b0000000000000000 &!
b0000000000011000 '!
1)!
b00000000000000000000000001011100 ?!
b01110 A!
b00000000000011110000000000001111 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000011110000000000000000 H!
#445
0)!
#449
#450
b00000000000000000000000001100000 )
b00111 *
b00000000000011110000000000000000 ,
b0000000000011001 :
b000000000000000000011001 ;
b00000000000000000000000000011001 ?
b00000000000011110000000000000000 H
b0000000001100100 a
b00000000111100111000001110010011 b
b00000000000011110000000000001111 c
b00000000000000000000000001100100 q
b00000000000000000000000001101000 r
b00000000000000000000000001101000 s
b0010011 t
b00111 u
b01111 v
b00000000000011110000000000000000 z
b00 |
b00000000000000000000000000001111 }
1"!
b00000000000000000000000000001111 #!
b000 $!
b0000000000000011 &!
b0000000000011001 '!
1)!
b00000000000000000000000001100000 ?!
b00111 A!
b00000000000011110000000000000000 B!
b00001111000011110001000100110111 C!
b00001111000011110001000100110111 D!
b00000000000011110000000000001111 H!
#455
0)!
#459
#460
b00000000000000000000000001100100 )
b00000000000011110000000000001111 ,
b0000000000011010 :
b000000000000000000011010 ;
b00000000000000000000000000011010 ?
b00000000000011110000000000001111 H
b0000000001101000 a
b00000000010000000000000110010011 b
b00000000000000000000000000000100 c
b00000000000000000000000001101000 q
b00000000000000000000000001101100 r
b00000000000000000000000001101100 s
b00000 u
b00100 v
b00011 w
b00000000000000000000000000000000 z
b00000000000000000000000000000100 }
b00000000000000000000000000000100 #!
b0000000000000001 &!
b0000000000011010 '!
1)!
b00000000000000000000000001100100 ?!
b00000000000011110000000000001111 B!
b11111111000000010000000010110111 C!
b11111111000000010000000010110111 D!
b00000000000000000000000000000100 H!
#465
0)!
#469
#470
b00000000000000000000000001101000 )
b00011 *
b00000000000000000000000000000100 ,
b0000000000011011 :
b000000000000000000011011 ;
b00000000000000000000000000011011 ?
b00000000000000000000000000000100 D
b0000000001101100 a
b01000100011101110001100001100011 b
b00000000000000000000000000000000 c
b00000000000011110000000000001111 e
b00000000000000000000000001101100 q
b00000000000000000000000001110000 r
b00000000000000000000000001110000 s
b1100011 t
b01110 u
b00111 v
b10000 w
b001 x
b0100010 y
b00000000000011110000000000001111 z
0{
b00000000000011110000000000001111 }
1~
b1010 !!
0"!
b00000000000000000000010001010000 #!
b010 $!
b0000000000000000 &!
b0000000000011011 '!
1)!
b00000000000000000000000001101000 ?!
b00011 A!
b00000000000000000000000000000100 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#475
0)!
#479
#480
b00000000000000000000000001101100 )
b10000 *
0+
b00000000000000000000000000000000 ,
b0000000000011100 :
b000000000000000000011100 ;
b00000000000000000000000000011100 ?
b0000000001110000 a
b11110000000011111111000010110111 b
b00000000000000000000000000000000 e
b00000000000000000000000001110000 q
b00000000000000000000000001110100 r
b00000000000000000000000001110100 s
b0110111 t
b11111 u
b00000 v
b00001 w
b111 x
b1111000 y
b00000000000000000000000000000000 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b11110000000011111111000000000000 #!
b011 $!
b0000000000011100 '!
1)!
b00000000000000000000000001101100 ?!
0@!
b10000 A!
b00000000000000000000000000000000 B!
b11110000000011111111000000000000 H!
#485
0)!
#489
#490
b00000000000000000000000001110000 )
b00001 *
1+
b11110000000011111111000000000000 ,
b0000000000011101 :
b000000000000000000011101 ;
b00000000000000000000000000011101 ?
b11110000000011111111000000000000 B
b0000000001110100 a
b00000000111100001000000010010011 b
b11110000000011111111000000001111 c
b00000000000000000000000001110100 q
b00000000000000000000000001111000 r
b00000000000000000000000001111000 s
b0010011 t
b00001 u
b01111 v
b000 x
b0000000 y
b11110000000011111111000000000000 z
b00 |
b00000000000000000000000000001111 }
1"!
b00000000000000000000000000001111 #!
b000 $!
b0011110000000011 &!
b0000000000011101 '!
1)!
b00000000000000000000000001110000 ?!
1@!
b00001 A!
b11110000000011111111000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b11110000000011111111000000001111 H!
#495
0)!
#499
#500
b00000000000000000000000001110100 )
b11110000000011111111000000001111 ,
b0000000000011110 :
b000000000000000000011110 ;
b00000000000000000000000000011110 ?
b11110000000011111111000000001111 B
b0000000001111000 a
b11110000111100001111000100110111 b
b00000000000000000000000001111000 q
b00000000000000000000000001111100 r
b00000000000000000000000001111100 s
b0110111 t
b00010 w
b111 x
b1111000 y
b11110000000011111111000000001111 z
b11 |
b00000000000000000000000000000000 }
0"!
b11110000111100001111000000000000 #!
b011 $!
b0000000000011110 '!
1)!
b00000000000000000000000001110100 ?!
b11110000000011111111000000001111 B!
b11110000111100001111000000000000 H!
#505
0)!
#509
#510
b00000000000000000000000001111000 )
b00010 *
b11110000111100001111000000000000 ,
b0000000000011111 :
b000000000000000000011111 ;
b00000000000000000000000000011111 ?
b11110000111100001111000000000000 C
b0000000001111100 a
b00001111000000010000000100010011 b
b11110000111100001111000011110000 c
b00000000000000000000000001111100 q
b00000000000000000000000010000000 r
b00000000000000000000000010000000 s
b0010011 t
b00010 u
b10000 v
b000 x
b0000111 y
b11110000111100001111000000000000 z
b00 |
b00000000000000000000000011110000 }
1"!
b00000000000000000000000011110000 #!
b000 $!
b0011110000111100 &!
b0000000000011111 '!
1)!
b00000000000000000000000001111000 ?!
b00010 A!
b11110000111100001111000000000000 B!
b11110000111100001111000011110000 H!
#515
0)!
#519
#520
b00000000000000000000000001111100 )
b11110000111100001111000011110000 ,
b0000000000100000 :
b000000000000000000100000 ;
b00000000000000000000000000100000 ?
b11110000111100001111000011110000 C
b0000000010000000 a
b00000000001000001111011100110011 b
b11110000000000001111000000000000 c
b11110000111100001111000011110000 e
b00000000000000000000000010000000 q
b00000000000000000000000010000100 r
b00000000000000000000000010000100 s
b0110011 t
b00001 u
b00010 v
b01110 w
b111 x
b0000000 y
b11110000000011111111000000001111 z
b11110000111100001111000011110000 }
b0010 !!
0"!
b00000000000000000000000000000010 #!
b0011110000000000 &!
b0000000000100000 '!
1)!
b00000000000000000000000001111100 ?!
b11110000111100001111000011110000 B!
b11110000000000001111000000000000 H!
#525
0)!
#529
#530
b00000000000000000000000010000000 )
b01110 *
b11110000000000001111000000000000 ,
b0000000000100001 :
b000000000000000000100001 ;
b00000000000000000000000000100001 ?
b11110000000000001111000000000000 O
b0000000010000100 a
b11110000000000001111001110110111 b
b11110000000011111111000000001111 c
b00000000000000000000000000000000 e
b00000000000000000000000010000100 q
b00000000000000000000000010001000 r
b00000000000000000000000010001000 s
b0110111 t
b00000 v
b00111 w
b1111000 y
b11 |
b00000000000000000000000000000000 }
b0000 !!
b11110000000000001111000000000000 #!
b011 $!
b0011110000000011 &!
b0000000000100001 '!
1)!
b00000000000000000000000010000000 ?!
b01110 A!
b11110000000000001111000000000000 B!
#535
0)!
#539
#540
b00000000000000000000000010000100 )
b00111 *
b0000000000100010 :
b000000000000000000100010 ;
b00000000000000000000000000100010 ?
b11110000000000001111000000000000 H
b0000000010001000 a
b00000000010100000000000110010011 b
b00000000000000000000000000000101 c
b00000000000000000000000010001000 q
b00000000000000000000000010001100 r
b00000000000000000000000010001100 s
b0010011 t
b00000 u
b00101 v
b00011 w
b000 x
b0000000 y
b00000000000000000000000000000000 z
b00 |
b00000000000000000000000000000101 }
1"!
b00000000000000000000000000000101 #!
b000 $!
b0000000000000001 &!
b0000000000100010 '!
1)!
b00000000000000000000000010000100 ?!
b00111 A!
b11111111000000010000000010110111 C!
b11111111000000010000000010110111 D!
b00000000000000000000000000000101 H!
#545
0)!
#549
#550
b00000000000000000000000010001000 )
b00011 *
b00000000000000000000000000000101 ,
b0000000000100011 :
b000000000000000000100011 ;
b00000000000000000000000000100011 ?
b00000000000000000000000000000101 D
b0000000010001100 a
b01000010011101110001100001100011 b
b00000000000000000000000000000000 c
b11110000000000001111000000000000 e
b00000000000000000000000010001100 q
b00000000000000000000000010010000 r
b00000000000000000000000010010000 s
b1100011 t
b01110 u
b00111 v
b10000 w
b001 x
b0100001 y
b11110000000000001111000000000000 z
0{
b11110000000000001111000000000000 }
1~
b1010 !!
0"!
b00000000000000000000010000110000 #!
b010 $!
b0000000000000000 &!
b0000000000100011 '!
1)!
b00000000000000000000000010001000 ?!
b00011 A!
b00000000000000000000000000000101 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#555
0)!
#559
#560
b00000000000000000000000010001100 )
b10000 *
0+
b00000000000000000000000000000000 ,
b0000000000100100 :
b000000000000000000100100 ;
b00000000000000000000000000100100 ?
b0000000010010000 a
b11111111000000010000000010110111 b
b11110000111100001111000011110000 c
b00000000000000000000000000000000 e
b00000000000000000000000010010000 q
b00000000000000000000000010010100 r
b00000000000000000000000010010100 s
b0110111 t
b00010 u
b10000 v
b00001 w
b000 x
b1111111 y
b11110000111100001111000011110000 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b11111111000000010000000000000000 #!
b011 $!
b0011110000111100 &!
b0000000000100100 '!
1)!
b00000000000000000000000010001100 ?!
0@!
b10000 A!
b00000000000000000000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b11111111000000010000000000000000 H!
#565
0)!
#569
#570
b00000000000000000000000010010000 )
b00001 *
1+
b11111111000000010000000000000000 ,
b0000000000100101 :
b000000000000000000100101 ;
b00000000000000000000000000100101 ?
b11111111000000010000000000000000 B
b0000000010010100 a
b11110000000000001000000010010011 b
b11111111000000001111111100000000 c
b00000000000000000000000010010100 q
b00000000000000000000000010011000 r
b00000000000000000000000010011000 s
b0010011 t
b00001 u
b00000 v
b1111000 y
b11111111000000010000000000000000 z
b00 |
b11111111111111111111111100000000 }
1"!
b11111111111111111111111100000000 #!
b000 $!
b0011111111000000 &!
b0000000000100101 '!
1)!
b00000000000000000000000010010000 ?!
1@!
b00001 A!
b11111111000000010000000000000000 B!
b11111111000000001111111100000000 H!
#575
0)!
#579
#580
b00000000000000000000000010010100 )
b11111111000000001111111100000000 ,
b0000000000100110 :
b000000000000000000100110 ;
b00000000000000000000000000100110 ?
b11111111000000001111111100000000 B
b0000000010011000 a
b00001111000011110001000100110111 b
b00000000000000000000000000000000 c
b00000000000000000000000010011000 q
b00000000000000000000000010011100 r
b00000000000000000000000010011100 s
b0110111 t
b11110 u
b10000 v
b00010 w
b001 x
b0000111 y
b00000000000000000000000000000000 z
b11 |
b00000000000000000000000000000000 }
0"!
b00001111000011110001000000000000 #!
b011 $!
b0000000000000000 &!
b0000000000100110 '!
1)!
b00000000000000000000000010010100 ?!
b11111111000000001111111100000000 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00001111000011110001000000000000 H!
#585
0)!
#589
#590
b00000000000000000000000010011000 )
b00010 *
b00001111000011110001000000000000 ,
b0000000000100111 :
b000000000000000000100111 ;
b00000000000000000000000000100111 ?
b00001111000011110001000000000000 C
b0000000010011100 a
b11110000111100010000000100010011 b
b00001111000011110000111100001111 c
b00000000000000000000000010011100 q
b00000000000000000000000010100000 r
b00000000000000000000000010100000 s
b0010011 t
b00010 u
b01111 v
b000 x
b1111000 y
b00001111000011110001000000000000 z
b00 |
b11111111111111111111111100001111 }
1"!
b11111111111111111111111100001111 #!
b000 $!
b0000001111000011 &!
b0000000000100111 '!
1)!
b00000000000000000000000010011000 ?!
b00010 A!
b00001111000011110001000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b00001111000011110000111100001111 H!
#595
0)!
#599
#600
b00000000000000000000000010011100 )
b00001111000011110000111100001111 ,
b0000000000101000 :
b000000000000000000101000 ;
b00000000000000000000000000101000 ?
b00001111000011110000111100001111 C
b0000000010100000 a
b00000000001000001111000010110011 b
b00001111000000000000111100000000 c
b00001111000011110000111100001111 e
b00000000000000000000000010100000 q
b00000000000000000000000010100100 r
b00000000000000000000000010100100 s
b0110011 t
b00001 u
b00010 v
b00001 w
b111 x
b0000000 y
b11111111000000001111111100000000 z
b00001111000011110000111100001111 }
b0010 !!
0"!
b00000000000000000000000000000010 #!
b0000001111000000 &!
b0000000000101000 '!
1)!
b00000000000000000000000010011100 ?!
b00001111000011110000111100001111 B!
b00001111000000000000111100000000 H!
#605
0)!
#609
#610
b00000000000000000000000010100000 )
b00001 *
b00001111000000000000111100000000 ,
b0000000000101001 :
b000000000000000000101001 ;
b00000000000000000000000000101001 ?
b00001111000000000000111100000000 B
b0000000010100100 a
b00001111000000000001001110110111 b
b00000000000000000000000000000000 c
b00000000000000000000000000000000 e
b00000000000000000000000010100100 q
b00000000000000000000000010101000 r
b00000000000000000000000010101000 s
b0110111 t
b00000 u
b10000 v
b00111 w
b001 x
b0000111 y
b00000000000000000000000000000000 z
b11 |
b00000000000000000000000000000000 }
b0000 !!
b00001111000000000001000000000000 #!
b011 $!
b0000000000000000 &!
b0000000000101001 '!
1)!
b00000000000000000000000010100000 ?!
b00001 A!
b00001111000000000000111100000000 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00001111000000000001000000000000 H!
#615
0)!
#619
#620
b00000000000000000000000010100100 )
b00111 *
b00001111000000000001000000000000 ,
b0000000000101010 :
b000000000000000000101010 ;
b00000000000000000000000000101010 ?
b00001111000000000001000000000000 H
b0000000010101000 a
b11110000000000111000001110010011 b
b00001111000000000000111100000000 c
b00000000000000000000000010101000 q
b00000000000000000000000010101100 r
b00000000000000000000000010101100 s
b0010011 t
b00111 u
b00000 v
b000 x
b1111000 y
b00001111000000000001000000000000 z
b00 |
b11111111111111111111111100000000 }
1"!
b11111111111111111111111100000000 #!
b000 $!
b0000001111000000 &!
b0000000000101010 '!
1)!
b00000000000000000000000010100100 ?!
b00111 A!
b00001111000000000001000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b00001111000000000000111100000000 H!
#625
0)!
#629
#630
b00000000000000000000000010101000 )
b00001111000000000000111100000000 ,
b0000000000101011 :
b000000000000000000101011 ;
b00000000000000000000000000101011 ?
b00001111000000000000111100000000 H
b0000000010101100 a
b00000000011000000000000110010011 b
b00000000000000000000000000000110 c
b00000000000000000000000010101100 q
b00000000000000000000000010110000 r
b00000000000000000000000010110000 s
b00000 u
b00110 v
b00011 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000000110 }
b00000000000000000000000000000110 #!
b0000000000000001 &!
b0000000000101011 '!
1)!
b00000000000000000000000010101000 ?!
b00001111000000000000111100000000 B!
b11111111000000010000000010110111 C!
b11111111000000010000000010110111 D!
b00000000000000000000000000000110 H!
#635
0)!
#639
#640
b00000000000000000000000010101100 )
b00011 *
b00000000000000000000000000000110 ,
b0000000000101100 :
b000000000000000000101100 ;
b00000000000000000000000000101100 ?
b00000000000000000000000000000110 D
b0000000010110000 a
b01000000011100001001011001100011 b
b00000000000000000000000000000000 c
b00001111000000000000111100000000 e
b00000000000000000000000010110000 q
b00000000000000000000000010110100 r
b00000000000000000000000010110100 s
b1100011 t
b00001 u
b00111 v
b01100 w
b001 x
b0100000 y
b00001111000000000000111100000000 z
0{
b00001111000000000000111100000000 }
1~
b1010 !!
0"!
b00000000000000000000010000001100 #!
b010 $!
b0000000000000000 &!
b0000000000101100 '!
1)!
b00000000000000000000000010101100 ?!
b00011 A!
b00000000000000000000000000000110 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#645
0)!
#649
#650
b00000000000000000000000010110000 )
b01100 *
0+
b00000000000000000000000000000000 ,
b0000000000101101 :
b000000000000000000101101 ;
b00000000000000000000000000101101 ?
b0000000010110100 a
b00001111111100000001000010110111 b
b00000000000000000000000000000000 e
b00000000000000000000000010110100 q
b00000000000000000000000010111000 r
b00000000000000000000000010111000 s
b0110111 t
b00000 u
b11111 v
b00001 w
b0000111 y
b00000000000000000000000000000000 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b00001111111100000001000000000000 #!
b011 $!
b0000000000101101 '!
1)!
b00000000000000000000000010110000 ?!
0@!
b01100 A!
b00000000000000000000000000000000 B!
b00001111111100000001000000000000 H!
#655
0)!
#659
#660
b00000000000000000000000010110100 )
b00001 *
1+
b00001111111100000001000000000000 ,
b0000000000101110 :
b000000000000000000101110 ;
b00000000000000000000000000101110 ?
b00001111111100000001000000000000 B
b0000000010111000 a
b11111111000000001000000010010011 b
b00001111111100000000111111110000 c
b00000000000000000000000010111000 q
b00000000000000000000000010111100 r
b00000000000000000000000010111100 s
b0010011 t
b00001 u
b10000 v
b000 x
b1111111 y
b00001111111100000001000000000000 z
b00 |
b11111111111111111111111111110000 }
1"!
b11111111111111111111111111110000 #!
b000 $!
b0000001111111100 &!
b0000000000101110 '!
1)!
b00000000000000000000000010110100 ?!
1@!
b00001 A!
b00001111111100000001000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b00001111111100000000111111110000 H!
#665
0)!
#669
#670
b00000000000000000000000010111000 )
b00001111111100000000111111110000 ,
b0000000000101111 :
b000000000000000000101111 ;
b00000000000000000000000000101111 ?
b00001111111100000000111111110000 B
b0000000010111100 a
b11110000111100001111000100110111 b
b00000000000000000000000010111100 q
b00000000000000000000000011000000 r
b00000000000000000000000011000000 s
b0110111 t
b01111 v
b00010 w
b111 x
b1111000 y
b00001111111100000000111111110000 z
b11 |
b00000000000000000000000000000000 }
0"!
b11110000111100001111000000000000 #!
b011 $!
b0000000000101111 '!
1)!
b00000000000000000000000010111000 ?!
b00001111111100000000111111110000 B!
b11110000111100001111000000000000 H!
#675
0)!
#679
#680
b00000000000000000000000010111100 )
b00010 *
b11110000111100001111000000000000 ,
b0000000000110000 :
b000000000000000000110000 ;
b00000000000000000000000000110000 ?
b11110000111100001111000000000000 C
b0000000011000000 a
b00001111000000010000000100010011 b
b11110000111100001111000011110000 c
b00000000000000000000000011000000 q
b00000000000000000000000011000100 r
b00000000000000000000000011000100 s
b0010011 t
b00010 u
b10000 v
b000 x
b0000111 y
b11110000111100001111000000000000 z
b00 |
b00000000000000000000000011110000 }
1"!
b00000000000000000000000011110000 #!
b000 $!
b0011110000111100 &!
b0000000000110000 '!
1)!
b00000000000000000000000010111100 ?!
b00010 A!
b11110000111100001111000000000000 B!
b11110000111100001111000011110000 H!
#685
0)!
#689
#690
b00000000000000000000000011000000 )
b11110000111100001111000011110000 ,
b0000000000110001 :
b000000000000000000110001 ;
b00000000000000000000000000110001 ?
b11110000111100001111000011110000 C
b0000000011000100 a
b00000000001000001111000100110011 b
b00000000111100000000000011110000 c
b11110000111100001111000011110000 e
b00000000000000000000000011000100 q
b00000000000000000000000011001000 r
b00000000000000000000000011001000 s
b0110011 t
b00001 u
b00010 v
b111 x
b0000000 y
b00001111111100000000111111110000 z
b11110000111100001111000011110000 }
b0010 !!
0"!
b00000000000000000000000000000010 #!
b0000000000111100 &!
b0000000000110001 '!
1)!
b00000000000000000000000011000000 ?!
b11110000111100001111000011110000 B!
b00111100011100001001011001100011 C!
b00111100011100001001011001100011 D!
b00000000111100000000000011110000 H!
#695
0)!
#699
#700
b00000000000000000000000011000100 )
b00000000111100000000000011110000 ,
b0000000000110010 :
b000000000000000000110010 ;
b00000000000000000000000000110010 ?
b00000000111100000000000011110000 C
b0000000011001000 a
b00000000111100000000001110110111 b
b00000000000000000000000000000000 c
b00000000000000000000000000000000 e
b00000000000000000000000011001000 q
b00000000000000000000000011001100 r
b00000000000000000000000011001100 s
b0110111 t
b00000 u
b01111 v
b00111 w
b000 x
b00000000000000000000000000000000 z
b11 |
b00000000000000000000000000000000 }
b0000 !!
b00000000111100000000000000000000 #!
b011 $!
b0000000000000000 &!
b0000000000110010 '!
1)!
b00000000000000000000000011000100 ?!
b00000000111100000000000011110000 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000111100000000000000000000 H!
#705
0)!
#709
#710
b00000000000000000000000011001000 )
b00111 *
b00000000111100000000000000000000 ,
b0000000000110011 :
b000000000000000000110011 ;
b00000000000000000000000000110011 ?
b00000000111100000000000000000000 H
b0000000011001100 a
b00001111000000111000001110010011 b
b00000000111100000000000011110000 c
b00000000000000000000000011001100 q
b00000000000000000000000011010000 r
b00000000000000000000000011010000 s
b0010011 t
b00111 u
b10000 v
b0000111 y
b00000000111100000000000000000000 z
b00 |
b00000000000000000000000011110000 }
1"!
b00000000000000000000000011110000 #!
b000 $!
b0000000000111100 &!
b0000000000110011 '!
1)!
b00000000000000000000000011001000 ?!
b00111 A!
b00000000111100000000000000000000 B!
b00111100011100001001011001100011 C!
b00111100011100001001011001100011 D!
b00000000111100000000000011110000 H!
#715
0)!
#719
#720
b00000000000000000000000011001100 )
b00000000111100000000000011110000 ,
b0000000000110100 :
b000000000000000000110100 ;
b00000000000000000000000000110100 ?
b00000000111100000000000011110000 H
b0000000011010000 a
b00000000011100000000000110010011 b
b00000000000000000000000000000111 c
b00000000111100000000000011110000 e
b00000000000000000000000011010000 q
b00000000000000000000000011010100 r
b00000000000000000000000011010100 s
b00000 u
b00111 v
b00011 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000000111 }
b00000000000000000000000000000111 #!
b0000000000000001 &!
b0000000000110100 '!
1)!
b00000000000000000000000011001100 ?!
b00000000111100000000000011110000 B!
b11111111000000010000000010110111 C!
b11111111000000010000000010110111 D!
b00000000000000000000000000000111 H!
#725
0)!
#729
#730
b00000000000000000000000011010000 )
b00011 *
b00000000000000000000000000000111 ,
b0000000000110101 :
b000000000000000000110101 ;
b00000000000000000000000000110101 ?
b00000000000000000000000000000111 D
b0000000011010100 a
b00111110011100010001010001100011 b
b00000000000000000000000000000000 c
b00000000000000000000000011010100 q
b00000000000000000000000011011000 r
b00000000000000000000000011011000 s
b1100011 t
b00010 u
b01000 w
b001 x
b0011111 y
b00000000111100000000000011110000 z
0{
b00000000111100000000000011110000 }
1~
b1010 !!
0"!
b00000000000000000000001111101000 #!
b010 $!
b0000000000000000 &!
b0000000000110101 '!
1)!
b00000000000000000000000011010000 ?!
b00011 A!
b00000000000000000000000000000111 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#735
0)!
#739
#740
b00000000000000000000000011010100 )
b01000 *
0+
b00000000000000000000000000000000 ,
b0000000000110110 :
b000000000000000000110110 ;
b00000000000000000000000000110110 ?
b0000000011011000 a
b11111111000000010000000010110111 b
b00000000111100000000000011110000 c
b00000000000000000000000000000000 e
b00000000000000000000000011011000 q
b00000000000000000000000011011100 r
b00000000000000000000000011011100 s
b0110111 t
b10000 v
b00001 w
b000 x
b1111111 y
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b11111111000000010000000000000000 #!
b011 $!
b0000000000111100 &!
b0000000000110110 '!
1)!
b00000000000000000000000011010100 ?!
0@!
b01000 A!
b00000000000000000000000000000000 B!
b00111100011100001001011001100011 C!
b00111100011100001001011001100011 D!
b11111111000000010000000000000000 H!
#745
0)!
#749
#750
b00000000000000000000000011011000 )
b00001 *
1+
b11111111000000010000000000000000 ,
b0000000000110111 :
b000000000000000000110111 ;
b00000000000000000000000000110111 ?
b11111111000000010000000000000000 B
b0000000011011100 a
b11110000000000001000000010010011 b
b11111111000000001111111100000000 c
b00000000000000000000000011011100 q
b00000000000000000000000011100000 r
b00000000000000000000000011100000 s
b0010011 t
b00001 u
b00000 v
b1111000 y
b11111111000000010000000000000000 z
b00 |
b11111111111111111111111100000000 }
1"!
b11111111111111111111111100000000 #!
b000 $!
b0011111111000000 &!
b0000000000110111 '!
1)!
b00000000000000000000000011011000 ?!
1@!
b00001 A!
b11111111000000010000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b11111111000000001111111100000000 H!
#755
0)!
#759
#760
b00000000000000000000000011011100 )
b11111111000000001111111100000000 ,
b0000000000111000 :
b000000000000000000111000 ;
b00000000000000000000000000111000 ?
b11111111000000001111111100000000 B
b0000000011100000 a
b00000000000100001111000010110011 b
b11111111000000001111111100000000 e
b00000000000000000000000011100000 q
b00000000000000000000000011100100 r
b00000000000000000000000011100100 s
b0110011 t
b00001 v
b111 x
b0000000 y
b11111111000000001111111100000000 z
b11111111000000001111111100000000 }
b0010 !!
0"!
b00000000000000000000000000000001 #!
b0000000000111000 '!
1)!
b00000000000000000000000011011100 ?!
b11111111000000001111111100000000 B!
#765
0)!
#769
#770
b00000000000000000000000011100000 )
b0000000000111001 :
b000000000000000000111001 ;
b00000000000000000000000000111001 ?
b0000000011100100 a
b11111111000000010000001110110111 b
b00000000111100000000000011110000 c
b00000000000000000000000000000000 e
b00000000000000000000000011100100 q
b00000000000000000000000011101000 r
b00000000000000000000000011101000 s
b0110111 t
b00010 u
b10000 v
b00111 w
b000 x
b1111111 y
b00000000111100000000000011110000 z
b11 |
b00000000000000000000000000000000 }
b0000 !!
b11111111000000010000000000000000 #!
b011 $!
b0000000000111100 &!
b0000000000111001 '!
1)!
b00000000000000000000000011100000 ?!
b00111100011100001001011001100011 C!
b00111100011100001001011001100011 D!
b11111111000000010000000000000000 H!
#775
0)!
#779
#780
b00000000000000000000000011100100 )
b00111 *
b11111111000000010000000000000000 ,
b0000000000111010 :
b000000000000000000111010 ;
b00000000000000000000000000111010 ?
b11111111000000010000000000000000 H
b0000000011101000 a
b11110000000000111000001110010011 b
b11111111000000001111111100000000 c
b00000000000000000000000011101000 q
b00000000000000000000000011101100 r
b00000000000000000000000011101100 s
b0010011 t
b00111 u
b00000 v
b1111000 y
b11111111000000010000000000000000 z
b00 |
b11111111111111111111111100000000 }
1"!
b11111111111111111111111100000000 #!
b000 $!
b0011111111000000 &!
b0000000000111010 '!
1)!
b00000000000000000000000011100100 ?!
b00111 A!
b11111111000000010000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b11111111000000001111111100000000 H!
#785
0)!
#789
#790
b00000000000000000000000011101000 )
b11111111000000001111111100000000 ,
b0000000000111011 :
b000000000000000000111011 ;
b00000000000000000000000000111011 ?
b11111111000000001111111100000000 H
b0000000011101100 a
b00000000100000000000000110010011 b
b00000000000000000000000000001000 c
b00000000000000000000000011101100 q
b00000000000000000000000011110000 r
b00000000000000000000000011110000 s
b00000 u
b01000 v
b00011 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000001000 }
b00000000000000000000000000001000 #!
b0000000000000010 &!
b0000000000111011 '!
1)!
b00000000000000000000000011101000 ?!
b11111111000000001111111100000000 B!
b11110000000000001000000010010011 C!
b11110000000000001000000010010011 D!
b00000000000000000000000000001000 H!
#795
0)!
#799
#800
b00000000000000000000000011101100 )
b00011 *
b00000000000000000000000000001000 ,
b0000000000111100 :
b000000000000000000111100 ;
b00000000000000000000000000111100 ?
b00000000000000000000000000001000 D
b0000000011110000 a
b00111100011100001001011001100011 b
b00000000000000000000000000000000 c
b11111111000000001111111100000000 e
b00000000000000000000000011110000 q
b00000000000000000000000011110100 r
b00000000000000000000000011110100 s
b1100011 t
b00001 u
b00111 v
b01100 w
b001 x
b0011110 y
b11111111000000001111111100000000 z
0{
b11111111000000001111111100000000 }
1~
b1010 !!
0"!
b00000000000000000000001111001100 #!
b010 $!
b0000000000000000 &!
b0000000000111100 '!
1)!
b00000000000000000000000011101100 ?!
b00011 A!
b00000000000000000000000000001000 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#805
0)!
#809
#810
b00000000000000000000000011110000 )
b01100 *
0+
b00000000000000000000000000000000 ,
b0000000000111101 :
b000000000000000000111101 ;
b00000000000000000000000000111101 ?
b0000000011110100 a
b00000000000000000000001000010011 b
b00000000000000000000000000000000 e
b00000000000000000000000011110100 q
b00000000000000000000000011111000 r
b00000000000000000000000011111000 s
b0010011 t
b00000 u
b00000 v
b00100 w
b000 x
b0000000 y
b00000000000000000000000000000000 z
1{
b00000000000000000000000000000000 }
0~
b0000 !!
1"!
b00000000000000000000000000000000 #!
b000 $!
b0000000000111101 '!
1)!
b00000000000000000000000011110000 ?!
0@!
b01100 A!
b00000000000000000000000000000000 B!
#815
0)!
#819
#820
b00000000000000000000000011110100 )
b00100 *
1+
b0000000000111110 :
b000000000000000000111110 ;
b00000000000000000000000000111110 ?
b0000000011111000 a
b11111111000000010000000010110111 b
b00000000111100000000000011110000 c
b00000000000000000000000011111000 q
b00000000000000000000000011111100 r
b00000000000000000000000011111100 s
b0110111 t
b00010 u
b10000 v
b00001 w
b1111111 y
b00000000111100000000000011110000 z
b11 |
0"!
b11111111000000010000000000000000 #!
b011 $!
b0000000000111100 &!
b0000000000111110 '!
1)!
b00000000000000000000000011110100 ?!
1@!
b00100 A!
b00111100011100001001011001100011 C!
b00111100011100001001011001100011 D!
b11111111000000010000000000000000 H!
#825
0)!
#829
#830
b00000000000000000000000011111000 )
b00001 *
b11111111000000010000000000000000 ,
b0000000000111111 :
b000000000000000000111111 ;
b00000000000000000000000000111111 ?
b11111111000000010000000000000000 B
b0000000011111100 a
b11110000000000001000000010010011 b
b11111111000000001111111100000000 c
b00000000000000000000000011111100 q
b00000000000000000000000100000000 r
b00000000000000000000000100000000 s
b0010011 t
b00001 u
b00000 v
b1111000 y
b11111111000000010000000000000000 z
b00 |
b11111111111111111111111100000000 }
1"!
b11111111111111111111111100000000 #!
b000 $!
b0011111111000000 &!
b0000000000111111 '!
1)!
b00000000000000000000000011111000 ?!
b00001 A!
b11111111000000010000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b11111111000000001111111100000000 H!
#835
0)!
#839
#840
b00000000000000000000000011111100 )
b11111111000000001111111100000000 ,
b0000000001000000 :
b000000000000000001000000 ;
b00000000000000000000000001000000 ?
b11111111000000001111111100000000 B
b0000000100000000 a
b00001111000011110001000100110111 b
b00000000000000000000000000000000 c
b00000000000000000000000100000000 q
b00000000000000000000000100000100 r
b00000000000000000000000100000100 s
b0110111 t
b11110 u
b10000 v
b00010 w
b001 x
b0000111 y
b00000000000000000000000000000000 z
b11 |
b00000000000000000000000000000000 }
0"!
b00001111000011110001000000000000 #!
b011 $!
b0000000000000000 &!
b0000000001000000 '!
1)!
b00000000000000000000000011111100 ?!
b11111111000000001111111100000000 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00001111000011110001000000000000 H!
#845
0)!
#849
#850
b00000000000000000000000100000000 )
b00010 *
b00001111000011110001000000000000 ,
b0000000001000001 :
b000000000000000001000001 ;
b00000000000000000000000001000001 ?
b00001111000011110001000000000000 C
b0000000100000100 a
b11110000111100010000000100010011 b
b00001111000011110000111100001111 c
b00000000000000000000000100000100 q
b00000000000000000000000100001000 r
b00000000000000000000000100001000 s
b0010011 t
b00010 u
b01111 v
b000 x
b1111000 y
b00001111000011110001000000000000 z
b00 |
b11111111111111111111111100001111 }
1"!
b11111111111111111111111100001111 #!
b000 $!
b0000001111000011 &!
b0000000001000001 '!
1)!
b00000000000000000000000100000000 ?!
b00010 A!
b00001111000011110001000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b00001111000011110000111100001111 H!
#855
0)!
#859
#860
b00000000000000000000000100000100 )
b00001111000011110000111100001111 ,
b0000000001000010 :
b000000000000000001000010 ;
b00000000000000000000000001000010 ?
b00001111000011110000111100001111 C
b0000000100001000 a
b00000000001000001111011100110011 b
b00001111000000000000111100000000 c
b00001111000011110000111100001111 e
b00000000000000000000000100001000 q
b00000000000000000000000100001100 r
b00000000000000000000000100001100 s
b0110011 t
b00001 u
b00010 v
b01110 w
b111 x
b0000000 y
b11111111000000001111111100000000 z
b00001111000011110000111100001111 }
b0010 !!
0"!
b00000000000000000000000000000010 #!
b0000001111000000 &!
b0000000001000010 '!
1)!
b00000000000000000000000100000100 ?!
b00001111000011110000111100001111 B!
b00001111000000000000111100000000 H!
#865
0)!
#869
#870
b00000000000000000000000100001000 )
b01110 *
b00001111000000000000111100000000 ,
b0000000001000011 :
b000000000000000001000011 ;
b00000000000000000000000001000011 ?
b00001111000000000000111100000000 O
b0000000100001100 a
b00000000000001110000001100010011 b
b00000000000000000000000000000000 e
b00000000000000000000000100001100 q
b00000000000000000000000100010000 r
b00000000000000000000000100010000 s
b0010011 t
b01110 u
b00000 v
b00110 w
b000 x
b00001111000000000000111100000000 z
b00000000000000000000000000000000 }
b0000 !!
1"!
b00000000000000000000000000000000 #!
b0000000001000011 '!
1)!
b00000000000000000000000100001000 ?!
b01110 A!
b00001111000000000000111100000000 B!
#875
0)!
#879
#880
b00000000000000000000000100001100 )
b00110 *
b0000000001000100 :
b000000000000000001000100 ;
b00000000000000000000000001000100 ?
b00001111000000000000111100000000 G
b0000000100010000 a
b00000000000100100000001000010011 b
b00000000000000000000000000000001 c
b11111111000000001111111100000000 e
b00000000000000000000000100010000 q
b00000000000000000000000100010100 r
b00000000000000000000000100010100 s
b00100 u
b00001 v
b00100 w
b00000000000000000000000000000000 z
b00000000000000000000000000000001 }
b00000000000000000000000000000001 #!
b0000000000000000 &!
b0000000001000100 '!
1)!
b00000000000000000000000100001100 ?!
b00110 A!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000001 H!
#885
0)!
#889
#890
b00000000000000000000000100010000 )
b00100 *
b00000000000000000000000000000001 ,
b0000000001000101 :
b000000000000000001000101 ;
b00000000000000000000000001000101 ?
b00000000000000000000000000000001 E
b0000000100010100 a
b00000000001000000000001010010011 b
b00000000000000000000000000000010 c
b00001111000011110000111100001111 e
b00000000000000000000000100010100 q
b00000000000000000000000100011000 r
b00000000000000000000000100011000 s
b00000 u
b00010 v
b00101 w
b00000000000000000000000000000010 }
b00000000000000000000000000000010 #!
b0000000001000101 '!
1)!
b00000000000000000000000100010000 ?!
b00100 A!
b00000000000000000000000000000001 B!
b00000000000000000000000000000010 H!
#895
0)!
#899
#900
b00000000000000000000000100010100 )
b00101 *
b00000000000000000000000000000010 ,
b0000000001000110 :
b000000000000000001000110 ;
b00000000000000000000000001000110 ?
b00000000000000000000000000000010 F
b0000000100011000 a
b11111110010100100001000011100011 b
b00000000000000000000000000000000 c
b00000000000000000000000000000010 e
b00000000000000000000000100011000 q
b00000000000000000000000100011100 r
b00000000000000000000000100011100 s
b1100011 t
b00100 u
b00101 v
b00001 w
b001 x
b1111111 y
b00000000000000000000000000000001 z
0{
b1010 !!
0"!
b11111111111111111111111111100000 #!
b010 $!
b0000000001000110 '!
1)!
b00000000000000000000000100010100 ?!
b00101 A!
b00000000000000000000000000000010 B!
b00000000000000000000000000000000 H!
#905
0)!
#909
#910
b00000000000000000000000100011000 )
b00001 *
0+
b00000000000000000000000000000000 ,
b0000000001000111 :
b000000000000000001000111 ;
b00000000000000000000000001000111 ?
b0000000100011100 a
b00001111000000000001001110110111 b
b00000000000000000000000000000000 e
b00000000000000000000000100011100 q
b00000000000000000000000100100000 r
b00000000000000000000000100100000 s
b0110111 t
b00000 u
b10000 v
b00111 w
b0000111 y
b00000000000000000000000000000000 z
1{
b11 |
b00000000000000000000000000000000 }
b0000 !!
b00001111000000000001000000000000 #!
b011 $!
b0000000001000111 '!
1)!
b00000000000000000000000100011000 ?!
0@!
b00001 A!
b00000000000000000000000000000000 B!
b00001111000000000001000000000000 H!
#915
0)!
#919
#920
b00000000000000000000000100011100 )
b00111 *
1+
b00001111000000000001000000000000 ,
b0000000001001000 :
b000000000000000001001000 ;
b00000000000000000000000001001000 ?
b00001111000000000001000000000000 H
b0000000100100000 a
b11110000000000111000001110010011 b
b00001111000000000000111100000000 c
b00000000000000000000000100100000 q
b00000000000000000000000100100100 r
b00000000000000000000000100100100 s
b0010011 t
b00111 u
b00000 v
b000 x
b1111000 y
b00001111000000000001000000000000 z
b00 |
b11111111111111111111111100000000 }
1"!
b11111111111111111111111100000000 #!
b000 $!
b0000001111000000 &!
b0000000001001000 '!
1)!
b00000000000000000000000100011100 ?!
1@!
b00111 A!
b00001111000000000001000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b00001111000000000000111100000000 H!
#925
0)!
