// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Primary model header
//
// This header should be included by all source files instantiating the design.
// The class here is then constructed to instantiate the design.
// See the Verilator manual for examples.

#ifndef VERILATED_VMINIRV_SOC_H_
#define VERILATED_VMINIRV_SOC_H_  // guard

#include "verilated.h"

class VminiRV_SoC__Syms;
class VminiRV_SoC___024root;
class VerilatedVcdC;

// This class is the main interface to the Verilated model
class VminiRV_SoC VL_NOT_FINAL {
  private:
    // Symbol table holding complete model state (owned by this class)
    VminiRV_SoC__Syms* const vlSymsp;

  public:

    // PORTS
    // The application code writes and reads these signals to
    // propagate new values into/out from the Verilated model.
    VL_IN8(&fpga_rst,0,0);
    VL_IN8(&fpga_clk,0,0);
    VL_IN8(&button,4,0);
    VL_OUT8(&dig_en,7,0);
    VL_OUT8(&DN_A0,0,0);
    VL_OUT8(&DN_A1,0,0);
    VL_OUT8(&DN_B0,0,0);
    VL_OUT8(&DN_B1,0,0);
    VL_OUT8(&DN_C0,0,0);
    VL_OUT8(&DN_C1,0,0);
    VL_OUT8(&DN_D0,0,0);
    VL_OUT8(&DN_D1,0,0);
    VL_OUT8(&DN_E0,0,0);
    VL_OUT8(&DN_E1,0,0);
    VL_OUT8(&DN_F0,0,0);
    VL_OUT8(&DN_F1,0,0);
    VL_OUT8(&DN_G0,0,0);
    VL_OUT8(&DN_G1,0,0);
    VL_OUT8(&DN_DP0,0,0);
    VL_OUT8(&DN_DP1,0,0);
    VL_OUT8(&debug_wb_have_inst,0,0);
    VL_OUT8(&debug_wb_ena,0,0);
    VL_OUT8(&debug_wb_reg,4,0);
    VL_IN16(&sw,15,0);
    VL_OUT16(&led,15,0);
    VL_OUT(&debug_wb_pc,31,0);
    VL_OUT(&debug_wb_value,31,0);

    // CELLS
    // Public to allow access to /* verilator public */ items.
    // Otherwise the application code can consider these internals.

    // Root instance pointer to allow access to model internals,
    // including inlined /* verilator public_flat_* */ items.
    VminiRV_SoC___024root* const rootp;

    // CONSTRUCTORS
    /// Construct the model; called by application code
    /// If contextp is null, then the model will use the default global context
    /// If name is "", then makes a wrapper with a
    /// single model invisible with respect to DPI scope names.
    explicit VminiRV_SoC(VerilatedContext* contextp, const char* name = "TOP");
    explicit VminiRV_SoC(const char* name = "TOP");
    /// Destroy the model; called (often implicitly) by application code
    virtual ~VminiRV_SoC();
  private:
    VL_UNCOPYABLE(VminiRV_SoC);  ///< Copying not allowed

  public:
    // API METHODS
    /// Evaluate the model.  Application must call when inputs change.
    void eval() { eval_step(); }
    /// Evaluate when calling multiple units/models per time step.
    void eval_step();
    /// Evaluate at end of a timestep for tracing, when using eval_step().
    /// Application must call after all eval() and before time changes.
    void eval_end_step() {}
    /// Simulation complete, run final blocks.  Application must call on completion.
    void final();
    /// Trace signals in the model; called by application code
    void trace(VerilatedVcdC* tfp, int levels, int options = 0);
    /// Return current simulation context for this model.
    /// Used to get to e.g. simulation time via contextp()->time()
    VerilatedContext* contextp() const;
    /// Retrieve name of this model instance (as passed to constructor).
    const char* name() const;
} VL_ATTR_ALIGNED(VL_CACHE_LINE_BYTES);

#endif  // guard
