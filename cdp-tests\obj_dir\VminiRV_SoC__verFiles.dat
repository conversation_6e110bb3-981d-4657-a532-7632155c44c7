# DESCRIPTION: Verilator output: Timestamp data for --skip-identical.  Delete at will.
C "-cc --exe --build vsrc/ram.v mySoC/ALU.v mySoC/Bridge.v mySoC/Button.v mySoC/Ctrl.v mySoC/defines.vh mySoC/Dig.v mySoC/Led.v mySoC/miniRV_SoC.v mySoC/myCPU.v mySoC/NPC.v mySoC/PC.v mySoC/RF.v mySoC/SEXT.v mySoC/Switch.v mySoC/Timer.v --top-module miniRV_SoC golden_model/emu.c golden_model/stage/EX.c golden_model/stage/ID.c golden_model/stage/IF.c golden_model/stage/MEM.c golden_model/stage/WB.c golden_model/peripheral/onboard.c golden_model/peripheral/result_monitor.c csrc/test.cpp --trace -Wno-lint -Wno-style -Wno-TIMESCALEMOD +define+PATH=meminit.bin -CFLAGS -DPATH=meminit.bin -ImySoC -CFLAGS -I/home/<USER>/2023311402/cdp-tests/golden_model/include"
S  10098880   929514  1655449811   698361585  1655449811   694361551 "/usr/local/bin/verilator_bin"
S      2530 69229358  1751965594   891259489  1751965594   891259489 "mySoC/ALU.v"
S      4028 69229405  1751965596   743308568  1751965596   743308568 "mySoC/Bridge.v"
S      1941 69229406  1751965598   427353196  1751965598   427353196 "mySoC/Button.v"
S      5497 69229407  1751965600   519408638  1751965600   519408638 "mySoC/Ctrl.v"
S      4209 69229409  1751965605   143531177  1751965605   143531177 "mySoC/Dig.v"
S       543 69229648  1751965579   334847200  1751965579   334847200 "mySoC/Led.v"
S       736 69229655  1751965584   442982584  1751965584   442982584 "mySoC/NPC.v"
S       430 69229658  1751965585   983023400  1751965585   983023400 "mySoC/PC.v"
S       738 69229659  1751965587   743070047  1751965587   743070047 "mySoC/RF.v"
S       904 69229662  1751965589   663120931  1751965589   663120931 "mySoC/SEXT.v"
S       492 69229663  1751965591   443168108  1751965591   443168108 "mySoC/Switch.v"
S      1592 69229664  1751965593    59210935  1751965593    59210935 "mySoC/Timer.v"
S      1902 69229408  1751965602   167452311  1751965602   167452311 "mySoC/defines.vh"
S      8208 69229649  1751966750   280269277  1751966750   280269277 "mySoC/miniRV_SoC.v"
S      5463 69229654  1751967234   337647022  1751967234   337647022 "mySoC/myCPU.v"
T      4880 69246378  1751967236   121696011  1751967236   121696011 "obj_dir/VminiRV_SoC.cpp"
T      3374 69246377  1751967236   117695901  1751967236   117695901 "obj_dir/VminiRV_SoC.h"
T      2903 69246387  1751967236   165697219  1751967236   165697219 "obj_dir/VminiRV_SoC.mk"
T      8745 69246375  1751967236   113695792  1751967236   113695792 "obj_dir/VminiRV_SoC__ConstPool_0.cpp"
T      1066 69229666  1751967236   105695572  1751967236   105695572 "obj_dir/VminiRV_SoC__Syms.cpp"
T      1256 69246374  1751967236   109695682  1751967236   109695682 "obj_dir/VminiRV_SoC__Syms.h"
T     21590 69246385  1751967236   157696999  1751967236   157696999 "obj_dir/VminiRV_SoC__Trace__0.cpp"
T     38213 69246384  1751967236   153696890  1751967236   153696890 "obj_dir/VminiRV_SoC__Trace__0__Slow.cpp"
T      5152 69246379  1751967236   125696120  1751967236   125696120 "obj_dir/VminiRV_SoC___024root.h"
T     71589 69246383  1751967236   145696670  1751967236   145696670 "obj_dir/VminiRV_SoC___024root__DepSet_h17f7d384__0.cpp"
T     52537 69246382  1751967236   137696450  1751967236   137696450 "obj_dir/VminiRV_SoC___024root__DepSet_h17f7d384__0__Slow.cpp"
T      8184 69246381  1751967236   133696341  1751967236   133696341 "obj_dir/VminiRV_SoC___024root__DepSet_h7ff9d9a5__0__Slow.cpp"
T       692 69246380  1751967236   129696231  1751967236   129696231 "obj_dir/VminiRV_SoC___024root__Slow.cpp"
T       823 69246388  1751967236   169697329  1751967236   169697329 "obj_dir/VminiRV_SoC__ver.d"
T         0        0  1751967236   169697329  1751967236   169697329 "obj_dir/VminiRV_SoC__verFiles.dat"
T      1871 69246386  1751967236   161697109  1751967236   161697109 "obj_dir/VminiRV_SoC_classes.mk"
S      2557 69229403  1751957880   347467446  1751957880   347467446 "vsrc/ram.v"
