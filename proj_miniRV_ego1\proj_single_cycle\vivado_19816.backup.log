#-----------------------------------------------------------
# Vivado v2018.3 (64-bit)
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
# IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
# Start of session at: Sat Jul  5 10:58:56 2025
# Process ID: 19816
# Current directory: H:/lab2/proj_miniRV_ego1/proj_single_cycle
# Command line: vivado.exe -gui_launcher_event rodinguilauncherevent18232 H:\lab2\proj_miniRV_ego1\proj_single_cycle\proj_single_cycle.xpr
# Log file: H:/lab2/proj_miniRV_ego1/proj_single_cycle/vivado.log
# Journal file: H:/lab2/proj_miniRV_ego1/proj_single_cycle\vivado.jou
#-----------------------------------------------------------
start_gui
open_project H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.xpr
Scanning sources...
Finished scanning sources
INFO: [IP_Flow 19-234] Refreshing IP repositories
INFO: [IP_Flow 19-1704] No user IP repositories specified
INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'E:/Vivado/2018.3/data/ip'.
update_compile_order -fileset sources_1
reset_run synth_1
launch_runs impl_1 -to_step write_bitstream -jobs 32
[Sat Jul  5 10:59:30 2025] Launched synth_1...
Run output will be captured here: H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/synth_1/runme.log
[Sat Jul  5 10:59:30 2025] Launched impl_1...
Run output will be captured here: H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1/runme.log
open_hw
connect_hw_server
INFO: [Labtools 27-2285] Connecting to hw_server url TCP:localhost:3121
INFO: [Labtools 27-2222] Launching hw_server...
INFO: [Labtools 27-2221] Launch Output:

****** Xilinx hw_server v2018.3
  **** Build date : Dec  7 2018-00:40:27
    ** Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.


open_hw_target
INFO: [Labtoolstcl 44-466] Opening hw_target localhost:3121/xilinx_tcf/Xilinx/1234-tulA
current_hw_device [get_hw_devices xc7a35t_0]
refresh_hw_device -update_hw_probes false [lindex [get_hw_devices xc7a35t_0] 0]
INFO: [Labtools 27-1435] Device xc7a35t (JTAG device index = 0) is not programmed (DONE status = 0).
set_property PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property FULL_PROBES.FILE {} [get_hw_devices xc7a35t_0]
set_property PROGRAM.FILE {H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.runs/impl_1/miniRV_SoC.bit} [get_hw_devices xc7a35t_0]
program_hw_devices [get_hw_devices xc7a35t_0]
INFO: [Labtools 27-3164] End of startup status: HIGH
refresh_hw_device [lindex [get_hw_devices xc7a35t_0] 0]
INFO: [Labtools 27-1434] Device xc7a35t (JTAG device index = 0) is programmed with a design that has no supported debug core(s) in it.
exit
INFO: [Common 17-206] Exiting Vivado at Sat Jul  5 11:04:11 2025...
