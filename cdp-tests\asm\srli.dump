
srli:     file format elf32-littleriscv


Disassembly of section .text.init:

00000000 <_start>:
   0:	0040006f          	jal	x0,4 <reset_vector>

00000004 <reset_vector>:
   4:	800000b7          	lui	x1,0x80000
   8:	0000d713          	srli	x14,x1,0x0
   c:	800003b7          	lui	x7,0x80000
  10:	00200193          	addi	x3,x0,2
  14:	28771863          	bne	x14,x7,2a4 <fail>

00000018 <test_3>:
  18:	800000b7          	lui	x1,0x80000
  1c:	0010d713          	srli	x14,x1,0x1
  20:	400003b7          	lui	x7,0x40000
  24:	00300193          	addi	x3,x0,3
  28:	26771e63          	bne	x14,x7,2a4 <fail>

0000002c <test_4>:
  2c:	800000b7          	lui	x1,0x80000
  30:	0070d713          	srli	x14,x1,0x7
  34:	010003b7          	lui	x7,0x1000
  38:	00400193          	addi	x3,x0,4
  3c:	26771463          	bne	x14,x7,2a4 <fail>

00000040 <test_5>:
  40:	800000b7          	lui	x1,0x80000
  44:	00e0d713          	srli	x14,x1,0xe
  48:	000203b7          	lui	x7,0x20
  4c:	00500193          	addi	x3,x0,5
  50:	24771a63          	bne	x14,x7,2a4 <fail>

00000054 <test_6>:
  54:	800000b7          	lui	x1,0x80000
  58:	00108093          	addi	x1,x1,1 # 80000001 <_end+0x7fffe001>
  5c:	01f0d713          	srli	x14,x1,0x1f
  60:	00100393          	addi	x7,x0,1
  64:	00600193          	addi	x3,x0,6
  68:	22771e63          	bne	x14,x7,2a4 <fail>

0000006c <test_7>:
  6c:	fff00093          	addi	x1,x0,-1
  70:	0000d713          	srli	x14,x1,0x0
  74:	fff00393          	addi	x7,x0,-1
  78:	00700193          	addi	x3,x0,7
  7c:	22771463          	bne	x14,x7,2a4 <fail>

00000080 <test_8>:
  80:	fff00093          	addi	x1,x0,-1
  84:	0010d713          	srli	x14,x1,0x1
  88:	800003b7          	lui	x7,0x80000
  8c:	fff38393          	addi	x7,x7,-1 # 7fffffff <_end+0x7fffdfff>
  90:	00800193          	addi	x3,x0,8
  94:	20771863          	bne	x14,x7,2a4 <fail>

00000098 <test_9>:
  98:	fff00093          	addi	x1,x0,-1
  9c:	0070d713          	srli	x14,x1,0x7
  a0:	020003b7          	lui	x7,0x2000
  a4:	fff38393          	addi	x7,x7,-1 # 1ffffff <_end+0x1ffdfff>
  a8:	00900193          	addi	x3,x0,9
  ac:	1e771c63          	bne	x14,x7,2a4 <fail>

000000b0 <test_10>:
  b0:	fff00093          	addi	x1,x0,-1
  b4:	00e0d713          	srli	x14,x1,0xe
  b8:	000403b7          	lui	x7,0x40
  bc:	fff38393          	addi	x7,x7,-1 # 3ffff <_end+0x3dfff>
  c0:	00a00193          	addi	x3,x0,10
  c4:	1e771063          	bne	x14,x7,2a4 <fail>

000000c8 <test_11>:
  c8:	fff00093          	addi	x1,x0,-1
  cc:	01f0d713          	srli	x14,x1,0x1f
  d0:	00100393          	addi	x7,x0,1
  d4:	00b00193          	addi	x3,x0,11
  d8:	1c771663          	bne	x14,x7,2a4 <fail>

000000dc <test_12>:
  dc:	212120b7          	lui	x1,0x21212
  e0:	12108093          	addi	x1,x1,289 # 21212121 <_end+0x21210121>
  e4:	0000d713          	srli	x14,x1,0x0
  e8:	212123b7          	lui	x7,0x21212
  ec:	12138393          	addi	x7,x7,289 # 21212121 <_end+0x21210121>
  f0:	00c00193          	addi	x3,x0,12
  f4:	1a771863          	bne	x14,x7,2a4 <fail>

000000f8 <test_13>:
  f8:	212120b7          	lui	x1,0x21212
  fc:	12108093          	addi	x1,x1,289 # 21212121 <_end+0x21210121>
 100:	0010d713          	srli	x14,x1,0x1
 104:	109093b7          	lui	x7,0x10909
 108:	09038393          	addi	x7,x7,144 # 10909090 <_end+0x10907090>
 10c:	00d00193          	addi	x3,x0,13
 110:	18771a63          	bne	x14,x7,2a4 <fail>

00000114 <test_14>:
 114:	212120b7          	lui	x1,0x21212
 118:	12108093          	addi	x1,x1,289 # 21212121 <_end+0x21210121>
 11c:	0070d713          	srli	x14,x1,0x7
 120:	004243b7          	lui	x7,0x424
 124:	24238393          	addi	x7,x7,578 # 424242 <_end+0x422242>
 128:	00e00193          	addi	x3,x0,14
 12c:	16771c63          	bne	x14,x7,2a4 <fail>

00000130 <test_15>:
 130:	212120b7          	lui	x1,0x21212
 134:	12108093          	addi	x1,x1,289 # 21212121 <_end+0x21210121>
 138:	00e0d713          	srli	x14,x1,0xe
 13c:	000083b7          	lui	x7,0x8
 140:	48438393          	addi	x7,x7,1156 # 8484 <_end+0x6484>
 144:	00f00193          	addi	x3,x0,15
 148:	14771e63          	bne	x14,x7,2a4 <fail>

0000014c <test_16>:
 14c:	212120b7          	lui	x1,0x21212
 150:	12108093          	addi	x1,x1,289 # 21212121 <_end+0x21210121>
 154:	01f0d713          	srli	x14,x1,0x1f
 158:	00000393          	addi	x7,x0,0
 15c:	01000193          	addi	x3,x0,16
 160:	14771263          	bne	x14,x7,2a4 <fail>

00000164 <test_17>:
 164:	800000b7          	lui	x1,0x80000
 168:	0070d093          	srli	x1,x1,0x7
 16c:	010003b7          	lui	x7,0x1000
 170:	01100193          	addi	x3,x0,17
 174:	12709863          	bne	x1,x7,2a4 <fail>

00000178 <test_18>:
 178:	00000213          	addi	x4,x0,0
 17c:	800000b7          	lui	x1,0x80000
 180:	0070d713          	srli	x14,x1,0x7
 184:	00070313          	addi	x6,x14,0
 188:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 18c:	00200293          	addi	x5,x0,2
 190:	fe5216e3          	bne	x4,x5,17c <test_18+0x4>
 194:	010003b7          	lui	x7,0x1000
 198:	01200193          	addi	x3,x0,18
 19c:	10731463          	bne	x6,x7,2a4 <fail>

000001a0 <test_19>:
 1a0:	00000213          	addi	x4,x0,0
 1a4:	800000b7          	lui	x1,0x80000
 1a8:	00e0d713          	srli	x14,x1,0xe
 1ac:	00000013          	addi	x0,x0,0
 1b0:	00070313          	addi	x6,x14,0
 1b4:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 1b8:	00200293          	addi	x5,x0,2
 1bc:	fe5214e3          	bne	x4,x5,1a4 <test_19+0x4>
 1c0:	000203b7          	lui	x7,0x20
 1c4:	01300193          	addi	x3,x0,19
 1c8:	0c731e63          	bne	x6,x7,2a4 <fail>

000001cc <test_20>:
 1cc:	00000213          	addi	x4,x0,0
 1d0:	800000b7          	lui	x1,0x80000
 1d4:	00108093          	addi	x1,x1,1 # 80000001 <_end+0x7fffe001>
 1d8:	01f0d713          	srli	x14,x1,0x1f
 1dc:	00000013          	addi	x0,x0,0
 1e0:	00000013          	addi	x0,x0,0
 1e4:	00070313          	addi	x6,x14,0
 1e8:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 1ec:	00200293          	addi	x5,x0,2
 1f0:	fe5210e3          	bne	x4,x5,1d0 <test_20+0x4>
 1f4:	00100393          	addi	x7,x0,1
 1f8:	01400193          	addi	x3,x0,20
 1fc:	0a731463          	bne	x6,x7,2a4 <fail>

00000200 <test_21>:
 200:	00000213          	addi	x4,x0,0
 204:	800000b7          	lui	x1,0x80000
 208:	0070d713          	srli	x14,x1,0x7
 20c:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 210:	00200293          	addi	x5,x0,2
 214:	fe5218e3          	bne	x4,x5,204 <test_21+0x4>
 218:	010003b7          	lui	x7,0x1000
 21c:	01500193          	addi	x3,x0,21
 220:	08771263          	bne	x14,x7,2a4 <fail>

00000224 <test_22>:
 224:	00000213          	addi	x4,x0,0
 228:	800000b7          	lui	x1,0x80000
 22c:	00000013          	addi	x0,x0,0
 230:	00e0d713          	srli	x14,x1,0xe
 234:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 238:	00200293          	addi	x5,x0,2
 23c:	fe5216e3          	bne	x4,x5,228 <test_22+0x4>
 240:	000203b7          	lui	x7,0x20
 244:	01600193          	addi	x3,x0,22
 248:	04771e63          	bne	x14,x7,2a4 <fail>

0000024c <test_23>:
 24c:	00000213          	addi	x4,x0,0
 250:	800000b7          	lui	x1,0x80000
 254:	00108093          	addi	x1,x1,1 # 80000001 <_end+0x7fffe001>
 258:	00000013          	addi	x0,x0,0
 25c:	00000013          	addi	x0,x0,0
 260:	01f0d713          	srli	x14,x1,0x1f
 264:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 268:	00200293          	addi	x5,x0,2
 26c:	fe5212e3          	bne	x4,x5,250 <test_23+0x4>
 270:	00100393          	addi	x7,x0,1
 274:	01700193          	addi	x3,x0,23
 278:	02771663          	bne	x14,x7,2a4 <fail>

0000027c <test_24>:
 27c:	00405093          	srli	x1,x0,0x4
 280:	00000393          	addi	x7,x0,0
 284:	01800193          	addi	x3,x0,24
 288:	00709e63          	bne	x1,x7,2a4 <fail>

0000028c <test_25>:
 28c:	02100093          	addi	x1,x0,33
 290:	00a0d013          	srli	x0,x1,0xa
 294:	00000393          	addi	x7,x0,0
 298:	01900193          	addi	x3,x0,25
 29c:	00701463          	bne	x0,x7,2a4 <fail>
 2a0:	00301e63          	bne	x0,x3,2bc <pass>

000002a4 <fail>:
 2a4:	00018063          	beq	x3,x0,2a4 <fail>
 2a8:	00119193          	slli	x3,x3,0x1
 2ac:	0011e193          	ori	x3,x3,1
 2b0:	05d00893          	addi	x17,x0,93
 2b4:	00018513          	addi	x10,x3,0
 2b8:	00000073          	ecall

000002bc <pass>:
 2bc:	00100193          	addi	x3,x0,1
 2c0:	05d00893          	addi	x17,x0,93
 2c4:	00000513          	addi	x10,x0,0
 2c8:	00000073          	ecall
 2cc:	c0001073          	unimp
 2d0:	0000                	c.unimp
 2d2:	0000                	c.unimp
 2d4:	0000                	c.unimp
 2d6:	0000                	c.unimp
 2d8:	0000                	c.unimp
 2da:	0000                	c.unimp
 2dc:	0000                	c.unimp
 2de:	0000                	c.unimp
 2e0:	0000                	c.unimp
 2e2:	0000                	c.unimp
 2e4:	0000                	c.unimp
 2e6:	0000                	c.unimp
 2e8:	0000                	c.unimp
 2ea:	0000                	c.unimp
 2ec:	0000                	c.unimp
 2ee:	0000                	c.unimp
 2f0:	0000                	c.unimp
 2f2:	0000                	c.unimp
 2f4:	0000                	c.unimp
 2f6:	0000                	c.unimp
 2f8:	0000                	c.unimp
 2fa:	0000                	c.unimp
 2fc:	0000                	c.unimp
 2fe:	0000                	c.unimp
 300:	0000                	c.unimp
 302:	0000                	c.unimp
