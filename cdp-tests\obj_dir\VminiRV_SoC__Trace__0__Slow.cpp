// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Tracing implementation internals
#include "verilated_vcd_c.h"
#include "VminiRV_SoC__Syms.h"


VL_ATTR_COLD void VminiRV_SoC___024root__trace_init_sub__TOP__0(VminiRV_SoC___024root* vlSelf, VerilatedVcd* tracep) {
    if (false && vlSelf) {}  // Prevent unused
    VminiRV_SoC__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    VL_DEBUG_IF(VL_DBG_MSGF("+    VminiRV_SoC___024root__trace_init_sub__TOP__0\n"); );
    // Init
    const int c = vlSymsp->__Vm_baseCode;
    // Body
    tracep->declBit(c+100,"fpga_rst", false,-1);
    tracep->declBit(c+101,"fpga_clk", false,-1);
    tracep->declBus(c+102,"sw", false,-1, 15,0);
    tracep->declBus(c+103,"button", false,-1, 4,0);
    tracep->declBus(c+104,"dig_en", false,-1, 7,0);
    tracep->declBit(c+105,"DN_A0", false,-1);
    tracep->declBit(c+106,"DN_A1", false,-1);
    tracep->declBit(c+107,"DN_B0", false,-1);
    tracep->declBit(c+108,"DN_B1", false,-1);
    tracep->declBit(c+109,"DN_C0", false,-1);
    tracep->declBit(c+110,"DN_C1", false,-1);
    tracep->declBit(c+111,"DN_D0", false,-1);
    tracep->declBit(c+112,"DN_D1", false,-1);
    tracep->declBit(c+113,"DN_E0", false,-1);
    tracep->declBit(c+114,"DN_E1", false,-1);
    tracep->declBit(c+115,"DN_F0", false,-1);
    tracep->declBit(c+116,"DN_F1", false,-1);
    tracep->declBit(c+117,"DN_G0", false,-1);
    tracep->declBit(c+118,"DN_G1", false,-1);
    tracep->declBit(c+119,"DN_DP0", false,-1);
    tracep->declBit(c+120,"DN_DP1", false,-1);
    tracep->declBus(c+121,"led", false,-1, 15,0);
    tracep->declBit(c+122,"debug_wb_have_inst", false,-1);
    tracep->declBus(c+123,"debug_wb_pc", false,-1, 31,0);
    tracep->declBit(c+124,"debug_wb_ena", false,-1);
    tracep->declBus(c+125,"debug_wb_reg", false,-1, 4,0);
    tracep->declBus(c+126,"debug_wb_value", false,-1, 31,0);
    tracep->pushNamePrefix("miniRV_SoC ");
    tracep->declBit(c+100,"fpga_rst", false,-1);
    tracep->declBit(c+101,"fpga_clk", false,-1);
    tracep->declBus(c+102,"sw", false,-1, 15,0);
    tracep->declBus(c+103,"button", false,-1, 4,0);
    tracep->declBus(c+104,"dig_en", false,-1, 7,0);
    tracep->declBit(c+105,"DN_A0", false,-1);
    tracep->declBit(c+106,"DN_A1", false,-1);
    tracep->declBit(c+107,"DN_B0", false,-1);
    tracep->declBit(c+108,"DN_B1", false,-1);
    tracep->declBit(c+109,"DN_C0", false,-1);
    tracep->declBit(c+110,"DN_C1", false,-1);
    tracep->declBit(c+111,"DN_D0", false,-1);
    tracep->declBit(c+112,"DN_D1", false,-1);
    tracep->declBit(c+113,"DN_E0", false,-1);
    tracep->declBit(c+114,"DN_E1", false,-1);
    tracep->declBit(c+115,"DN_F0", false,-1);
    tracep->declBit(c+116,"DN_F1", false,-1);
    tracep->declBit(c+117,"DN_G0", false,-1);
    tracep->declBit(c+118,"DN_G1", false,-1);
    tracep->declBit(c+119,"DN_DP0", false,-1);
    tracep->declBit(c+120,"DN_DP1", false,-1);
    tracep->declBus(c+121,"led", false,-1, 15,0);
    tracep->declBit(c+122,"debug_wb_have_inst", false,-1);
    tracep->declBus(c+123,"debug_wb_pc", false,-1, 31,0);
    tracep->declBit(c+124,"debug_wb_ena", false,-1);
    tracep->declBus(c+125,"debug_wb_reg", false,-1, 4,0);
    tracep->declBus(c+126,"debug_wb_value", false,-1, 31,0);
    tracep->declBit(c+133,"pll_lock", false,-1);
    tracep->declBit(c+134,"pll_clk", false,-1);
    tracep->declBit(c+101,"cpu_clk", false,-1);
    tracep->declBus(c+63,"inst_addr", false,-1, 15,0);
    tracep->declBus(c+64,"inst", false,-1, 31,0);
    tracep->declBus(c+127,"Bus_rdata", false,-1, 31,0);
    tracep->declBus(c+65,"Bus_addr", false,-1, 31,0);
    tracep->declBit(c+66,"Bus_we", false,-1);
    tracep->declBus(c+67,"Bus_wdata", false,-1, 31,0);
    tracep->declBit(c+101,"clk_bridge2dram", false,-1);
    tracep->declBus(c+65,"addr_bridge2dram", false,-1, 31,0);
    tracep->declBus(c+128,"rdata_dram2bridge", false,-1, 31,0);
    tracep->declBit(c+68,"we_bridge2dram", false,-1);
    tracep->declBus(c+67,"wdata_bridge2dram", false,-1, 31,0);
    tracep->declBit(c+100,"rst_bridge2dig", false,-1);
    tracep->declBit(c+101,"clk_bridge2dig", false,-1);
    tracep->declBus(c+65,"addr_bridge2dig", false,-1, 31,0);
    tracep->declBus(c+67,"wdata_bridge2dig", false,-1, 31,0);
    tracep->declBit(c+69,"we_bridge2dig", false,-1);
    tracep->declBit(c+100,"rst_bridge2tim", false,-1);
    tracep->declBit(c+101,"clk_bridge2tim", false,-1);
    tracep->declBus(c+65,"addr_bridge2tim", false,-1, 31,0);
    tracep->declBus(c+67,"wdata_bridge2tim", false,-1, 31,0);
    tracep->declBus(c+129,"rdata_tim2bridge", false,-1, 31,0);
    tracep->declBit(c+70,"we_bridge2tim", false,-1);
    tracep->declBit(c+100,"rst_bridge2led", false,-1);
    tracep->declBit(c+101,"clk_bridge2led", false,-1);
    tracep->declBus(c+65,"addr_bridge2led", false,-1, 31,0);
    tracep->declBus(c+67,"wdata_bridge2led", false,-1, 31,0);
    tracep->declBit(c+71,"we_bridge2led", false,-1);
    tracep->declBit(c+100,"rst_bridge2sw", false,-1);
    tracep->declBit(c+101,"clk_bridge2sw", false,-1);
    tracep->declBus(c+65,"addr_bridge2sw", false,-1, 31,0);
    tracep->declBus(c+130,"rdata_sw2bridge", false,-1, 31,0);
    tracep->declBit(c+100,"rst_bridge2btn", false,-1);
    tracep->declBit(c+101,"clk_bridge2btn", false,-1);
    tracep->declBus(c+65,"addr_bridge2btn", false,-1, 31,0);
    tracep->declBus(c+131,"rdata_btn2bridge", false,-1, 31,0);
    tracep->pushNamePrefix("Bridge ");
    tracep->declBit(c+100,"rst_from_cpu", false,-1);
    tracep->declBit(c+101,"clk_from_cpu", false,-1);
    tracep->declBus(c+65,"addr_from_cpu", false,-1, 31,0);
    tracep->declBit(c+66,"we_from_cpu", false,-1);
    tracep->declBus(c+67,"wdata_from_cpu", false,-1, 31,0);
    tracep->declBus(c+127,"rdata_to_cpu", false,-1, 31,0);
    tracep->declBit(c+101,"clk_to_dram", false,-1);
    tracep->declBus(c+65,"addr_to_dram", false,-1, 31,0);
    tracep->declBus(c+128,"rdata_from_dram", false,-1, 31,0);
    tracep->declBit(c+68,"we_to_dram", false,-1);
    tracep->declBus(c+67,"wdata_to_dram", false,-1, 31,0);
    tracep->declBit(c+100,"rst_to_dig", false,-1);
    tracep->declBit(c+101,"clk_to_dig", false,-1);
    tracep->declBus(c+65,"addr_to_dig", false,-1, 31,0);
    tracep->declBit(c+69,"we_to_dig", false,-1);
    tracep->declBus(c+67,"wdata_to_dig", false,-1, 31,0);
    tracep->declBit(c+100,"rst_to_led", false,-1);
    tracep->declBit(c+101,"clk_to_led", false,-1);
    tracep->declBus(c+65,"addr_to_led", false,-1, 31,0);
    tracep->declBit(c+71,"we_to_led", false,-1);
    tracep->declBus(c+67,"wdata_to_led", false,-1, 31,0);
    tracep->declBit(c+100,"rst_to_sw", false,-1);
    tracep->declBit(c+101,"clk_to_sw", false,-1);
    tracep->declBus(c+65,"addr_to_sw", false,-1, 31,0);
    tracep->declBus(c+130,"rdata_from_sw", false,-1, 31,0);
    tracep->declBit(c+100,"rst_to_btn", false,-1);
    tracep->declBit(c+101,"clk_to_btn", false,-1);
    tracep->declBus(c+65,"addr_to_btn", false,-1, 31,0);
    tracep->declBus(c+131,"rdata_from_btn", false,-1, 31,0);
    tracep->declBit(c+100,"rst_to_tim", false,-1);
    tracep->declBit(c+101,"clk_to_tim", false,-1);
    tracep->declBus(c+65,"addr_to_tim", false,-1, 31,0);
    tracep->declBit(c+70,"we_to_tim", false,-1);
    tracep->declBus(c+67,"wdata_to_tim", false,-1, 31,0);
    tracep->declBus(c+129,"rdata_from_tim", false,-1, 31,0);
    tracep->declBit(c+72,"access_mem", false,-1);
    tracep->declBit(c+73,"access_dig", false,-1);
    tracep->declBit(c+74,"access_tim", false,-1);
    tracep->declBit(c+75,"access_led", false,-1);
    tracep->declBit(c+76,"access_sw", false,-1);
    tracep->declBit(c+77,"access_btn", false,-1);
    tracep->declBus(c+78,"access_bit", false,-1, 5,0);
    tracep->popNamePrefix(1);
    tracep->pushNamePrefix("Core_cpu ");
    tracep->declBit(c+100,"cpu_rst", false,-1);
    tracep->declBit(c+101,"cpu_clk", false,-1);
    tracep->declBus(c+63,"inst_addr", false,-1, 15,0);
    tracep->declBus(c+64,"inst", false,-1, 31,0);
    tracep->declBus(c+65,"Bus_addr", false,-1, 31,0);
    tracep->declBus(c+127,"Bus_rdata", false,-1, 31,0);
    tracep->declBit(c+66,"Bus_we", false,-1);
    tracep->declBus(c+67,"Bus_wdata", false,-1, 31,0);
    tracep->declBit(c+122,"debug_wb_have_inst", false,-1);
    tracep->declBus(c+123,"debug_wb_pc", false,-1, 31,0);
    tracep->declBit(c+124,"debug_wb_ena", false,-1);
    tracep->declBus(c+125,"debug_wb_reg", false,-1, 4,0);
    tracep->declBus(c+126,"debug_wb_value", false,-1, 31,0);
    tracep->declBus(c+79,"pc_current", false,-1, 31,0);
    tracep->declBus(c+80,"pc_next", false,-1, 31,0);
    tracep->declBus(c+81,"pc_plus4", false,-1, 31,0);
    tracep->declBus(c+7,"prev_pc", false,-1, 31,0);
    tracep->declBus(c+8,"prev_rd", false,-1, 4,0);
    tracep->declBit(c+9,"prev_rf_we", false,-1);
    tracep->declBus(c+10,"prev_rf_wD", false,-1, 31,0);
    tracep->declBit(c+11,"have_prev_inst", false,-1);
    tracep->declBus(c+82,"opcode", false,-1, 6,0);
    tracep->declBus(c+83,"rs1", false,-1, 4,0);
    tracep->declBus(c+84,"rs2", false,-1, 4,0);
    tracep->declBus(c+85,"rd", false,-1, 4,0);
    tracep->declBus(c+86,"funct3", false,-1, 2,0);
    tracep->declBus(c+87,"funct7", false,-1, 6,0);
    tracep->declBus(c+88,"rf_rD1", false,-1, 31,0);
    tracep->declBus(c+67,"rf_rD2", false,-1, 31,0);
    tracep->declBus(c+132,"rf_wD", false,-1, 31,0);
    tracep->declBit(c+89,"rf_we", false,-1);
    tracep->declBus(c+90,"rf_wsel", false,-1, 1,0);
    tracep->declBus(c+88,"alu_A", false,-1, 31,0);
    tracep->declBus(c+91,"alu_B", false,-1, 31,0);
    tracep->declBus(c+65,"alu_C", false,-1, 31,0);
    tracep->declBit(c+92,"alu_f", false,-1);
    tracep->declBus(c+93,"alu_op", false,-1, 3,0);
    tracep->declBit(c+94,"alub_sel", false,-1);
    tracep->declBus(c+95,"sext_ext", false,-1, 31,0);
    tracep->declBus(c+96,"sext_op", false,-1, 2,0);
    tracep->declBus(c+97,"npc_op", false,-1, 1,0);
    tracep->declBus(c+81,"npc_pc4", false,-1, 31,0);
    tracep->declBit(c+66,"ram_we", false,-1);
    tracep->pushNamePrefix("U_ALU ");
    tracep->declBus(c+88,"A", false,-1, 31,0);
    tracep->declBus(c+91,"B", false,-1, 31,0);
    tracep->declBus(c+93,"alu_op", false,-1, 3,0);
    tracep->declBus(c+65,"C", false,-1, 31,0);
    tracep->declBit(c+92,"f", false,-1);
    tracep->popNamePrefix(1);
    tracep->pushNamePrefix("U_Ctrl ");
    tracep->declBus(c+82,"opcode", false,-1, 6,0);
    tracep->declBus(c+86,"funct3", false,-1, 2,0);
    tracep->declBus(c+87,"funct7", false,-1, 6,0);
    tracep->declBit(c+92,"alu_f", false,-1);
    tracep->declBus(c+97,"npc_op", false,-1, 1,0);
    tracep->declBit(c+89,"rf_we", false,-1);
    tracep->declBus(c+90,"rf_wsel", false,-1, 1,0);
    tracep->declBus(c+96,"sext_op", false,-1, 2,0);
    tracep->declBus(c+93,"alu_op", false,-1, 3,0);
    tracep->declBit(c+94,"alub_sel", false,-1);
    tracep->declBit(c+66,"ram_we", false,-1);
    tracep->popNamePrefix(1);
    tracep->pushNamePrefix("U_NPC ");
    tracep->declBus(c+79,"PC", false,-1, 31,0);
    tracep->declBus(c+95,"IMM", false,-1, 31,0);
    tracep->declBus(c+65,"ALU_C", false,-1, 31,0);
    tracep->declBus(c+97,"npc_op", false,-1, 1,0);
    tracep->declBus(c+80,"npc", false,-1, 31,0);
    tracep->declBus(c+81,"pc4", false,-1, 31,0);
    tracep->popNamePrefix(1);
    tracep->pushNamePrefix("U_PC ");
    tracep->declBit(c+100,"rst", false,-1);
    tracep->declBit(c+101,"clk", false,-1);
    tracep->declBus(c+80,"din", false,-1, 31,0);
    tracep->declBus(c+79,"pc", false,-1, 31,0);
    tracep->popNamePrefix(1);
    tracep->pushNamePrefix("U_RF ");
    tracep->declBit(c+101,"clk", false,-1);
    tracep->declBus(c+83,"rR1", false,-1, 4,0);
    tracep->declBus(c+88,"rD1", false,-1, 31,0);
    tracep->declBus(c+84,"rR2", false,-1, 4,0);
    tracep->declBus(c+67,"rD2", false,-1, 31,0);
    tracep->declBit(c+89,"we", false,-1);
    tracep->declBus(c+85,"wR", false,-1, 4,0);
    tracep->declBus(c+132,"wD", false,-1, 31,0);
    for (int i = 0; i < 32; ++i) {
        tracep->declBus(c+31+i*1,"registers", true,(i+0), 31,0);
    }
    tracep->popNamePrefix(1);
    tracep->pushNamePrefix("U_SEXT ");
    tracep->declBus(c+64,"inst", false,-1, 31,0);
    tracep->declBus(c+96,"sext_op", false,-1, 2,0);
    tracep->declBus(c+95,"ext", false,-1, 31,0);
    tracep->popNamePrefix(2);
    tracep->pushNamePrefix("Mem_DRAM ");
    tracep->declBus(c+135,"ADDR_BITS", false,-1, 31,0);
    tracep->declBit(c+101,"clk", false,-1);
    tracep->declBus(c+98,"a", false,-1, 15,0);
    tracep->declBit(c+68,"we", false,-1);
    tracep->declBus(c+67,"d", false,-1, 31,0);
    tracep->declBus(c+128,"spo", false,-1, 31,0);
    tracep->declBus(c+1,"i", false,-1, 31,0);
    tracep->declBus(c+2,"j", false,-1, 31,0);
    tracep->declBus(c+3,"mem_file", false,-1, 31,0);
    tracep->popNamePrefix(1);
    tracep->pushNamePrefix("Mem_IROM ");
    tracep->declBus(c+135,"ADDR_BITS", false,-1, 31,0);
    tracep->declBus(c+99,"a", false,-1, 15,0);
    tracep->declBus(c+64,"spo", false,-1, 31,0);
    tracep->declBus(c+4,"i", false,-1, 31,0);
    tracep->declBus(c+5,"j", false,-1, 31,0);
    tracep->declBus(c+6,"mem_file", false,-1, 31,0);
    tracep->popNamePrefix(1);
    tracep->pushNamePrefix("U_Button ");
    tracep->declBit(c+100,"rst", false,-1);
    tracep->declBit(c+101,"clk", false,-1);
    tracep->declBus(c+65,"addr", false,-1, 31,0);
    tracep->declBus(c+131,"rdata", false,-1, 31,0);
    tracep->declBus(c+103,"button", false,-1, 4,0);
    tracep->declBus(c+12,"button_sync1", false,-1, 4,0);
    tracep->declBus(c+13,"button_sync2", false,-1, 4,0);
    tracep->declBus(c+14,"button_stable", false,-1, 4,0);
    for (int i = 0; i < 5; ++i) {
        tracep->declBus(c+15+i*1,"debounce_cnt", true,(i+0), 19,0);
    }
    tracep->popNamePrefix(1);
    tracep->pushNamePrefix("U_Dig ");
    tracep->declBit(c+100,"rst", false,-1);
    tracep->declBit(c+101,"clk", false,-1);
    tracep->declBus(c+65,"addr", false,-1, 31,0);
    tracep->declBit(c+69,"we", false,-1);
    tracep->declBus(c+67,"wdata", false,-1, 31,0);
    tracep->declBus(c+104,"dig_en", false,-1, 7,0);
    tracep->declBit(c+105,"DN_A0", false,-1);
    tracep->declBit(c+106,"DN_A1", false,-1);
    tracep->declBit(c+107,"DN_B0", false,-1);
    tracep->declBit(c+108,"DN_B1", false,-1);
    tracep->declBit(c+109,"DN_C0", false,-1);
    tracep->declBit(c+110,"DN_C1", false,-1);
    tracep->declBit(c+111,"DN_D0", false,-1);
    tracep->declBit(c+112,"DN_D1", false,-1);
    tracep->declBit(c+113,"DN_E0", false,-1);
    tracep->declBit(c+114,"DN_E1", false,-1);
    tracep->declBit(c+115,"DN_F0", false,-1);
    tracep->declBit(c+116,"DN_F1", false,-1);
    tracep->declBit(c+117,"DN_G0", false,-1);
    tracep->declBit(c+118,"DN_G1", false,-1);
    tracep->declBit(c+119,"DN_DP0", false,-1);
    tracep->declBit(c+120,"DN_DP1", false,-1);
    tracep->declBus(c+20,"display_data", false,-1, 31,0);
    tracep->declBus(c+21,"pending_data", false,-1, 31,0);
    tracep->declBit(c+22,"data_pending", false,-1);
    tracep->declBus(c+23,"scan_cnt", false,-1, 2,0);
    tracep->declBus(c+24,"scan_clk_cnt", false,-1, 15,0);
    tracep->declBus(c+25,"update_limit_cnt", false,-1, 23,0);
    tracep->declBus(c+26,"current_digit", false,-1, 3,0);
    tracep->declBus(c+27,"seg_code", false,-1, 6,0);
    tracep->popNamePrefix(1);
    tracep->pushNamePrefix("U_Led ");
    tracep->declBit(c+100,"rst", false,-1);
    tracep->declBit(c+101,"clk", false,-1);
    tracep->declBus(c+65,"addr", false,-1, 31,0);
    tracep->declBit(c+71,"we", false,-1);
    tracep->declBus(c+67,"wdata", false,-1, 31,0);
    tracep->declBus(c+121,"led", false,-1, 15,0);
    tracep->popNamePrefix(1);
    tracep->pushNamePrefix("U_Switch ");
    tracep->declBit(c+100,"rst", false,-1);
    tracep->declBit(c+101,"clk", false,-1);
    tracep->declBus(c+65,"addr", false,-1, 31,0);
    tracep->declBus(c+130,"rdata", false,-1, 31,0);
    tracep->declBus(c+102,"sw", false,-1, 15,0);
    tracep->popNamePrefix(1);
    tracep->pushNamePrefix("U_Timer ");
    tracep->declBit(c+100,"rst", false,-1);
    tracep->declBit(c+101,"clk", false,-1);
    tracep->declBus(c+65,"addr", false,-1, 31,0);
    tracep->declBit(c+70,"we", false,-1);
    tracep->declBus(c+67,"wdata", false,-1, 31,0);
    tracep->declBus(c+129,"rdata", false,-1, 31,0);
    tracep->declBus(c+28,"counter0", false,-1, 31,0);
    tracep->declBus(c+29,"counter1", false,-1, 31,0);
    tracep->declBus(c+30,"threshold", false,-1, 31,0);
    tracep->popNamePrefix(2);
}

VL_ATTR_COLD void VminiRV_SoC___024root__trace_init_top(VminiRV_SoC___024root* vlSelf, VerilatedVcd* tracep) {
    if (false && vlSelf) {}  // Prevent unused
    VminiRV_SoC__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    VL_DEBUG_IF(VL_DBG_MSGF("+    VminiRV_SoC___024root__trace_init_top\n"); );
    // Body
    VminiRV_SoC___024root__trace_init_sub__TOP__0(vlSelf, tracep);
}

VL_ATTR_COLD void VminiRV_SoC___024root__trace_full_top_0(void* voidSelf, VerilatedVcd* tracep);
void VminiRV_SoC___024root__trace_chg_top_0(void* voidSelf, VerilatedVcd* tracep);
void VminiRV_SoC___024root__trace_cleanup(void* voidSelf, VerilatedVcd* /*unused*/);

VL_ATTR_COLD void VminiRV_SoC___024root__trace_register(VminiRV_SoC___024root* vlSelf, VerilatedVcd* tracep) {
    if (false && vlSelf) {}  // Prevent unused
    VminiRV_SoC__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    VL_DEBUG_IF(VL_DBG_MSGF("+    VminiRV_SoC___024root__trace_register\n"); );
    // Body
    tracep->addFullCb(&VminiRV_SoC___024root__trace_full_top_0, vlSelf);
    tracep->addChgCb(&VminiRV_SoC___024root__trace_chg_top_0, vlSelf);
    tracep->addCleanupCb(&VminiRV_SoC___024root__trace_cleanup, vlSelf);
}

VL_ATTR_COLD void VminiRV_SoC___024root__trace_full_sub_0(VminiRV_SoC___024root* vlSelf, VerilatedVcd* tracep);

VL_ATTR_COLD void VminiRV_SoC___024root__trace_full_top_0(void* voidSelf, VerilatedVcd* tracep) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VminiRV_SoC___024root__trace_full_top_0\n"); );
    // Init
    VminiRV_SoC___024root* const __restrict vlSelf VL_ATTR_UNUSED = static_cast<VminiRV_SoC___024root*>(voidSelf);
    VminiRV_SoC__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    // Body
    VminiRV_SoC___024root__trace_full_sub_0((&vlSymsp->TOP), tracep);
}

VL_ATTR_COLD void VminiRV_SoC___024root__trace_full_sub_0(VminiRV_SoC___024root* vlSelf, VerilatedVcd* tracep) {
    if (false && vlSelf) {}  // Prevent unused
    VminiRV_SoC__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    VL_DEBUG_IF(VL_DBG_MSGF("+    VminiRV_SoC___024root__trace_full_sub_0\n"); );
    // Init
    uint32_t* const oldp VL_ATTR_UNUSED = tracep->oldp(vlSymsp->__Vm_baseCode);
    // Body
    tracep->fullIData(oldp+1,(vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__i),32);
    tracep->fullIData(oldp+2,(vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__j),32);
    tracep->fullIData(oldp+3,(vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__mem_file),32);
    tracep->fullIData(oldp+4,(vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__i),32);
    tracep->fullIData(oldp+5,(vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__j),32);
    tracep->fullIData(oldp+6,(vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__mem_file),32);
    tracep->fullIData(oldp+7,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_pc),32);
    tracep->fullCData(oldp+8,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_rd),5);
    tracep->fullBit(oldp+9,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_rf_we));
    tracep->fullIData(oldp+10,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_rf_wD),32);
    tracep->fullBit(oldp+11,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__have_prev_inst));
    tracep->fullCData(oldp+12,(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_sync1),5);
    tracep->fullCData(oldp+13,(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_sync2),5);
    tracep->fullCData(oldp+14,(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_stable),5);
    tracep->fullIData(oldp+15,(vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[0]),20);
    tracep->fullIData(oldp+16,(vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[1]),20);
    tracep->fullIData(oldp+17,(vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[2]),20);
    tracep->fullIData(oldp+18,(vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[3]),20);
    tracep->fullIData(oldp+19,(vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[4]),20);
    tracep->fullIData(oldp+20,(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data),32);
    tracep->fullIData(oldp+21,(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__pending_data),32);
    tracep->fullBit(oldp+22,(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__data_pending));
    tracep->fullCData(oldp+23,(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt),3);
    tracep->fullSData(oldp+24,(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_clk_cnt),16);
    tracep->fullIData(oldp+25,(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__update_limit_cnt),24);
    tracep->fullCData(oldp+26,((0xfU & ((0U == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                         ? vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data
                                         : ((1U == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                             ? (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                >> 4U)
                                             : ((2U 
                                                 == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                                 ? 
                                                (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                 >> 8U)
                                                 : 
                                                ((3U 
                                                  == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                                  ? 
                                                 (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                  >> 0xcU)
                                                  : 
                                                 ((4U 
                                                   == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                                   ? 
                                                  (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                   >> 0x10U)
                                                   : 
                                                  ((5U 
                                                    == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                                    ? 
                                                   (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                    >> 0x14U)
                                                    : 
                                                   ((6U 
                                                     == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                                     ? 
                                                    (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                     >> 0x18U)
                                                     : 
                                                    (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                     >> 0x1cU)))))))))),4);
    tracep->fullCData(oldp+27,(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__seg_code),7);
    tracep->fullIData(oldp+28,(vlSelf->miniRV_SoC__DOT__U_Timer__DOT__counter0),32);
    tracep->fullIData(oldp+29,(vlSelf->miniRV_SoC__DOT__U_Timer__DOT__counter1),32);
    tracep->fullIData(oldp+30,(vlSelf->miniRV_SoC__DOT__U_Timer__DOT__threshold),32);
    tracep->fullIData(oldp+31,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[0]),32);
    tracep->fullIData(oldp+32,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[1]),32);
    tracep->fullIData(oldp+33,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[2]),32);
    tracep->fullIData(oldp+34,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[3]),32);
    tracep->fullIData(oldp+35,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[4]),32);
    tracep->fullIData(oldp+36,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[5]),32);
    tracep->fullIData(oldp+37,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[6]),32);
    tracep->fullIData(oldp+38,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[7]),32);
    tracep->fullIData(oldp+39,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[8]),32);
    tracep->fullIData(oldp+40,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[9]),32);
    tracep->fullIData(oldp+41,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[10]),32);
    tracep->fullIData(oldp+42,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[11]),32);
    tracep->fullIData(oldp+43,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[12]),32);
    tracep->fullIData(oldp+44,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[13]),32);
    tracep->fullIData(oldp+45,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[14]),32);
    tracep->fullIData(oldp+46,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[15]),32);
    tracep->fullIData(oldp+47,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[16]),32);
    tracep->fullIData(oldp+48,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[17]),32);
    tracep->fullIData(oldp+49,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[18]),32);
    tracep->fullIData(oldp+50,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[19]),32);
    tracep->fullIData(oldp+51,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[20]),32);
    tracep->fullIData(oldp+52,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[21]),32);
    tracep->fullIData(oldp+53,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[22]),32);
    tracep->fullIData(oldp+54,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[23]),32);
    tracep->fullIData(oldp+55,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[24]),32);
    tracep->fullIData(oldp+56,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[25]),32);
    tracep->fullIData(oldp+57,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[26]),32);
    tracep->fullIData(oldp+58,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[27]),32);
    tracep->fullIData(oldp+59,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[28]),32);
    tracep->fullIData(oldp+60,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[29]),32);
    tracep->fullIData(oldp+61,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[30]),32);
    tracep->fullIData(oldp+62,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[31]),32);
    tracep->fullSData(oldp+63,((0xffffU & vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current)),16);
    tracep->fullIData(oldp+64,(vlSelf->miniRV_SoC__DOT__inst),32);
    tracep->fullIData(oldp+65,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C),32);
    tracep->fullBit(oldp+66,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__ram_we));
    tracep->fullIData(oldp+67,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD2),32);
    tracep->fullBit(oldp+68,(((IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__ram_we) 
                              & (0xfffffU != (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C 
                                              >> 0xcU)))));
    tracep->fullBit(oldp+69,(vlSelf->miniRV_SoC__DOT__we_bridge2dig));
    tracep->fullBit(oldp+70,(vlSelf->miniRV_SoC__DOT__we_bridge2tim));
    tracep->fullBit(oldp+71,(((IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__ram_we) 
                              & (0xfffff060U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C))));
    tracep->fullBit(oldp+72,((0xfffffU != (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C 
                                           >> 0xcU))));
    tracep->fullBit(oldp+73,((0xfffff000U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)));
    tracep->fullBit(oldp+74,(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_tim));
    tracep->fullBit(oldp+75,((0xfffff060U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)));
    tracep->fullBit(oldp+76,((0xfffff070U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)));
    tracep->fullBit(oldp+77,((0xfffff078U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)));
    tracep->fullCData(oldp+78,(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit),6);
    tracep->fullIData(oldp+79,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current),32);
    tracep->fullIData(oldp+80,(((0U == (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op))
                                 ? ((IData)(4U) + vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current)
                                 : ((1U == (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op))
                                     ? (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current 
                                        + vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_ext)
                                     : ((2U == (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op))
                                         ? (0xfffffffeU 
                                            & vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                         : ((IData)(4U) 
                                            + vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current))))),32);
    tracep->fullIData(oldp+81,(((IData)(4U) + vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current)),32);
    tracep->fullCData(oldp+82,((0x7fU & vlSelf->miniRV_SoC__DOT__inst)),7);
    tracep->fullCData(oldp+83,((0x1fU & (vlSelf->miniRV_SoC__DOT__inst 
                                         >> 0xfU))),5);
    tracep->fullCData(oldp+84,((0x1fU & (vlSelf->miniRV_SoC__DOT__inst 
                                         >> 0x14U))),5);
    tracep->fullCData(oldp+85,((0x1fU & (vlSelf->miniRV_SoC__DOT__inst 
                                         >> 7U))),5);
    tracep->fullCData(oldp+86,((7U & (vlSelf->miniRV_SoC__DOT__inst 
                                      >> 0xcU))),3);
    tracep->fullCData(oldp+87,((vlSelf->miniRV_SoC__DOT__inst 
                                >> 0x19U)),7);
    tracep->fullIData(oldp+88,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1),32);
    tracep->fullBit(oldp+89,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_we));
    tracep->fullCData(oldp+90,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel),2);
    tracep->fullIData(oldp+91,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B),32);
    tracep->fullBit(oldp+92,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_f));
    tracep->fullCData(oldp+93,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op),4);
    tracep->fullBit(oldp+94,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alub_sel));
    tracep->fullIData(oldp+95,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_ext),32);
    tracep->fullCData(oldp+96,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op),3);
    tracep->fullCData(oldp+97,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op),2);
    tracep->fullSData(oldp+98,((0x3fffU & (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C 
                                           >> 2U))),16);
    tracep->fullSData(oldp+99,((0x3fffU & (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current 
                                           >> 2U))),16);
    tracep->fullBit(oldp+100,(vlSelf->fpga_rst));
    tracep->fullBit(oldp+101,(vlSelf->fpga_clk));
    tracep->fullSData(oldp+102,(vlSelf->sw),16);
    tracep->fullCData(oldp+103,(vlSelf->button),5);
    tracep->fullCData(oldp+104,(vlSelf->dig_en),8);
    tracep->fullBit(oldp+105,(vlSelf->DN_A0));
    tracep->fullBit(oldp+106,(vlSelf->DN_A1));
    tracep->fullBit(oldp+107,(vlSelf->DN_B0));
    tracep->fullBit(oldp+108,(vlSelf->DN_B1));
    tracep->fullBit(oldp+109,(vlSelf->DN_C0));
    tracep->fullBit(oldp+110,(vlSelf->DN_C1));
    tracep->fullBit(oldp+111,(vlSelf->DN_D0));
    tracep->fullBit(oldp+112,(vlSelf->DN_D1));
    tracep->fullBit(oldp+113,(vlSelf->DN_E0));
    tracep->fullBit(oldp+114,(vlSelf->DN_E1));
    tracep->fullBit(oldp+115,(vlSelf->DN_F0));
    tracep->fullBit(oldp+116,(vlSelf->DN_F1));
    tracep->fullBit(oldp+117,(vlSelf->DN_G0));
    tracep->fullBit(oldp+118,(vlSelf->DN_G1));
    tracep->fullBit(oldp+119,(vlSelf->DN_DP0));
    tracep->fullBit(oldp+120,(vlSelf->DN_DP1));
    tracep->fullSData(oldp+121,(vlSelf->led),16);
    tracep->fullBit(oldp+122,(vlSelf->debug_wb_have_inst));
    tracep->fullIData(oldp+123,(vlSelf->debug_wb_pc),32);
    tracep->fullBit(oldp+124,(vlSelf->debug_wb_ena));
    tracep->fullCData(oldp+125,(vlSelf->debug_wb_reg),5);
    tracep->fullIData(oldp+126,(vlSelf->debug_wb_value),32);
    tracep->fullIData(oldp+127,(((0x20U & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                  ? vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__mem
                                 [(0x3fffU & (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C 
                                              >> 2U))]
                                  : ((0x10U & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                      ? 0xffffffffU
                                      : ((8U & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                          ? ((4U & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                              ? 0xffffffffU
                                              : ((2U 
                                                  & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                                  ? 0xffffffffU
                                                  : 
                                                 ((1U 
                                                   & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                                   ? 0xffffffffU
                                                   : 
                                                  ((0xfffff020U 
                                                    == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                                    ? vlSelf->miniRV_SoC__DOT__U_Timer__DOT__counter0
                                                    : 
                                                   ((0xfffff024U 
                                                     == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                                     ? vlSelf->miniRV_SoC__DOT__U_Timer__DOT__threshold
                                                     : 0U)))))
                                          : ((4U & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                              ? 0xffffffffU
                                              : ((2U 
                                                  & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                                  ? 
                                                 ((1U 
                                                   & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                                   ? 0xffffffffU
                                                   : 
                                                  ((0xfffff070U 
                                                    == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                                    ? (IData)(vlSelf->sw)
                                                    : 0U))
                                                  : 
                                                 ((1U 
                                                   & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                                   ? 
                                                  ((0xfffff078U 
                                                    == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                                    ? (IData)(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_stable)
                                                    : 0U)
                                                   : 0xffffffffU))))))),32);
    tracep->fullIData(oldp+128,(vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__mem
                                [(0x3fffU & (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C 
                                             >> 2U))]),32);
    tracep->fullIData(oldp+129,(((0xfffff020U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                  ? vlSelf->miniRV_SoC__DOT__U_Timer__DOT__counter0
                                  : ((0xfffff024U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                      ? vlSelf->miniRV_SoC__DOT__U_Timer__DOT__threshold
                                      : 0U))),32);
    tracep->fullIData(oldp+130,(((0xfffff070U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                  ? (IData)(vlSelf->sw)
                                  : 0U)),32);
    tracep->fullIData(oldp+131,(((0xfffff078U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                  ? (IData)(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_stable)
                                  : 0U)),32);
    tracep->fullIData(oldp+132,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wD),32);
    tracep->fullBit(oldp+133,(vlSelf->miniRV_SoC__DOT__pll_lock));
    tracep->fullBit(oldp+134,(vlSelf->miniRV_SoC__DOT__pll_clk));
    tracep->fullIData(oldp+135,(0x10U),32);
}
