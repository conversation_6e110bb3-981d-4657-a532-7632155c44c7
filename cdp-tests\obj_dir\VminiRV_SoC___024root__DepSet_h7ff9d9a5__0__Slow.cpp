// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design implementation internals
// See VminiRV_SoC.h for the primary calling header

#include "verilated.h"

#include "VminiRV_SoC__Syms.h"
#include "VminiRV_SoC___024root.h"

VL_ATTR_COLD void VminiRV_SoC___024root___initial__TOP__0(VminiRV_SoC___024root* vlSelf) {
    if (false && vlSelf) {}  // Prevent unused
    VminiRV_SoC__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    VL_DEBUG_IF(VL_DBG_MSGF("+    VminiRV_SoC___024root___initial__TOP__0\n"); );
    // Init
    VlWide<3>/*95:0*/ __Vtemp_hd22861a6__0;
    VlWide<3>/*95:0*/ __Vtemp_hd22861a6__1;
    // Body
    vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__i = 0U;
    while (VL_GTS_III(32, 0x100000U, vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__i)) {
        vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__j = vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__i;
        while (VL_LTS_III(32, vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__j, 
                          ((IData)(0x400U) + vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__i))) {
            vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__mem[(0xfffffU 
                                                         & vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__j)] = 0U;
            vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__j 
                = ((IData)(1U) + vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__j);
        }
        vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__i = 
            ((IData)(0x400U) + vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__i);
    }
    __Vtemp_hd22861a6__0[0U] = 0x2e62696eU;
    __Vtemp_hd22861a6__0[1U] = 0x696e6974U;
    __Vtemp_hd22861a6__0[2U] = 0x6d656dU;
    vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__mem_file = VL_FOPEN_NN(
                                                                   VL_CVT_PACK_STR_NW(3, __Vtemp_hd22861a6__0)
                                                                   , 
                                                                   std::string("r"));
    if (VL_UNLIKELY((0U == vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__mem_file))) {
        VL_WRITEF("[ERROR] Open file meminit.bin failed, please check whether file exists!\n\n[%0t] %%Error: ram.v:66: Assertion failed in %NminiRV_SoC.Mem_DRAM\n",
                  64,VL_TIME_UNITED_Q(1000),-9,vlSymsp->name());
        VL_STOP_MT("vsrc/ram.v", 66, "");
    }
    VL_WRITEF("[INFO] Data RAM initialized with meminit.bin\n");
    (void)VL_FREAD_I(32,0,1048576, &(vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__mem_rd)
                     , vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__mem_file
                     , 0, 1048576);vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__i = 0U;
    while (VL_GTS_III(32, 0x100000U, vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__i)) {
        vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__j = vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__i;
        while (VL_LTS_III(32, vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__j, 
                          ((IData)(0x400U) + vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__i))) {
            vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__mem[(0xfffffU 
                                                         & vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__j)] 
                = ((vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__mem_rd
                    [(0xfffffU & vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__j)] 
                    << 0x18U) | ((0xff0000U & (vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__mem_rd
                                               [(0xfffffU 
                                                 & vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__j)] 
                                               << 8U)) 
                                 | ((0xff00U & (vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__mem_rd
                                                [(0xfffffU 
                                                  & vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__j)] 
                                                >> 8U)) 
                                    | (vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__mem_rd
                                       [(0xfffffU & vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__j)] 
                                       >> 0x18U))));
            vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__j 
                = ((IData)(1U) + vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__j);
        }
        vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__i = 
            ((IData)(0x400U) + vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__i);
    }
    vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__i = 0U;
    while (VL_GTS_III(32, 0x100000U, vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__i)) {
        vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__j = vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__i;
        while (VL_LTS_III(32, vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__j, 
                          ((IData)(0x400U) + vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__i))) {
            vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__mem[(0xfffffU 
                                                         & vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__j)] = 0U;
            vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__j 
                = ((IData)(1U) + vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__j);
        }
        vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__i = 
            ((IData)(0x400U) + vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__i);
    }
    __Vtemp_hd22861a6__1[0U] = 0x2e62696eU;
    __Vtemp_hd22861a6__1[1U] = 0x696e6974U;
    __Vtemp_hd22861a6__1[2U] = 0x6d656dU;
    vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__mem_file = VL_FOPEN_NN(
                                                                   VL_CVT_PACK_STR_NW(3, __Vtemp_hd22861a6__1)
                                                                   , 
                                                                   std::string("r"));
    if (VL_UNLIKELY((0U == vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__mem_file))) {
        VL_WRITEF("[ERROR] Open file meminit.bin failed, please check whether file exists!\n\n[%0t] %%Error: ram.v:26: Assertion failed in %NminiRV_SoC.Mem_IROM\n",
                  64,VL_TIME_UNITED_Q(1000),-9,vlSymsp->name());
        VL_STOP_MT("vsrc/ram.v", 26, "");
    }
    VL_WRITEF("[INFO] Instruction ROM initialized with meminit.bin\n");
    (void)VL_FREAD_I(32,0,1048576, &(vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__mem_rd)
                     , vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__mem_file
                     , 0, 1048576);vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__i = 0U;
    while (VL_GTS_III(32, 0x100000U, vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__i)) {
        vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__j = vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__i;
        while (VL_LTS_III(32, vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__j, 
                          ((IData)(0x400U) + vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__i))) {
            vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__mem[(0xfffffU 
                                                         & vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__j)] 
                = ((vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__mem_rd
                    [(0xfffffU & vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__j)] 
                    << 0x18U) | ((0xff0000U & (vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__mem_rd
                                               [(0xfffffU 
                                                 & vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__j)] 
                                               << 8U)) 
                                 | ((0xff00U & (vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__mem_rd
                                                [(0xfffffU 
                                                  & vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__j)] 
                                                >> 8U)) 
                                    | (vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__mem_rd
                                       [(0xfffffU & vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__j)] 
                                       >> 0x18U))));
            vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__j 
                = ((IData)(1U) + vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__j);
        }
        vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__i = 
            ((IData)(0x400U) + vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__i);
    }
}
