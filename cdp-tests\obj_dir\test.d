test.o: ../csrc/test.cpp ../csrc/dut.h \
 /usr/local/share/verilator/include/verilated_vcd_c.h \
 /usr/local/share/verilator/include/verilated.h \
 /usr/local/share/verilator/include/verilatedos.h \
 /usr/local/share/verilator/include/verilated_config.h \
 /usr/local/share/verilator/include/verilated_types.h \
 /usr/local/share/verilator/include/verilated_funcs.h \
 /usr/local/share/verilator/include/verilated_trace.h \
 /usr/local/share/verilator/include/verilated_trace_defs.h \
 /home/<USER>/2023311402/cdp-tests/golden_model/include/cpu.h \
 /home/<USER>/2023311402/cdp-tests/golden_model/include/debug.h \
 /home/<USER>/2023311402/cdp-tests/golden_model/include/riscv32_instdef.h \
 /usr/local/share/verilator/include/verilated.h VminiRV_SoC.h
