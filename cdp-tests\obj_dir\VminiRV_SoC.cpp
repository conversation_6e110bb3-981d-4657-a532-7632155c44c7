// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Model implementation (design independent parts)

#include "VminiRV_SoC.h"
#include "VminiRV_SoC__Syms.h"
#include "verilated_vcd_c.h"

//============================================================
// Constructors

VminiRV_SoC::VminiRV_SoC(VerilatedContext* _vcontextp__, const char* _vcname__)
    : vlSymsp{new VminiRV_SoC__Syms(_vcontextp__, _vcname__, this)}
    , fpga_rst{vlSymsp->TOP.fpga_rst}
    , fpga_clk{vlSymsp->TOP.fpga_clk}
    , button{vlSymsp->TOP.button}
    , dig_en{vlSymsp->TOP.dig_en}
    , DN_A0{vlSymsp->TOP.DN_A0}
    , DN_A1{vlSymsp->TOP.DN_A1}
    , DN_B0{vlSymsp->TOP.DN_B0}
    , DN_B1{vlSymsp->TOP.DN_B1}
    , DN_C0{vlSymsp->TOP.DN_C0}
    , DN_C1{vlSymsp->TOP.DN_C1}
    , DN_D0{vlSymsp->TOP.DN_D0}
    , DN_D1{vlSymsp->TOP.DN_D1}
    , DN_E0{vlSymsp->TOP.DN_E0}
    , DN_E1{vlSymsp->TOP.DN_E1}
    , DN_F0{vlSymsp->TOP.DN_F0}
    , DN_F1{vlSymsp->TOP.DN_F1}
    , DN_G0{vlSymsp->TOP.DN_G0}
    , DN_G1{vlSymsp->TOP.DN_G1}
    , DN_DP0{vlSymsp->TOP.DN_DP0}
    , DN_DP1{vlSymsp->TOP.DN_DP1}
    , debug_wb_have_inst{vlSymsp->TOP.debug_wb_have_inst}
    , debug_wb_ena{vlSymsp->TOP.debug_wb_ena}
    , debug_wb_reg{vlSymsp->TOP.debug_wb_reg}
    , sw{vlSymsp->TOP.sw}
    , led{vlSymsp->TOP.led}
    , debug_wb_pc{vlSymsp->TOP.debug_wb_pc}
    , debug_wb_value{vlSymsp->TOP.debug_wb_value}
    , rootp{&(vlSymsp->TOP)}
{
}

VminiRV_SoC::VminiRV_SoC(const char* _vcname__)
    : VminiRV_SoC(nullptr, _vcname__)
{
}

//============================================================
// Destructor

VminiRV_SoC::~VminiRV_SoC() {
    delete vlSymsp;
}

//============================================================
// Evaluation loop

void VminiRV_SoC___024root___eval_initial(VminiRV_SoC___024root* vlSelf);
void VminiRV_SoC___024root___eval_settle(VminiRV_SoC___024root* vlSelf);
void VminiRV_SoC___024root___eval(VminiRV_SoC___024root* vlSelf);
#ifdef VL_DEBUG
void VminiRV_SoC___024root___eval_debug_assertions(VminiRV_SoC___024root* vlSelf);
#endif  // VL_DEBUG
void VminiRV_SoC___024root___final(VminiRV_SoC___024root* vlSelf);

static void _eval_initial_loop(VminiRV_SoC__Syms* __restrict vlSymsp) {
    vlSymsp->__Vm_didInit = true;
    VminiRV_SoC___024root___eval_initial(&(vlSymsp->TOP));
    // Evaluate till stable
    vlSymsp->__Vm_activity = true;
    do {
        VL_DEBUG_IF(VL_DBG_MSGF("+ Initial loop\n"););
        VminiRV_SoC___024root___eval_settle(&(vlSymsp->TOP));
        VminiRV_SoC___024root___eval(&(vlSymsp->TOP));
    } while (0);
}

void VminiRV_SoC::eval_step() {
    VL_DEBUG_IF(VL_DBG_MSGF("+++++TOP Evaluate VminiRV_SoC::eval_step\n"); );
#ifdef VL_DEBUG
    // Debug assertions
    VminiRV_SoC___024root___eval_debug_assertions(&(vlSymsp->TOP));
#endif  // VL_DEBUG
    // Initialize
    if (VL_UNLIKELY(!vlSymsp->__Vm_didInit)) _eval_initial_loop(vlSymsp);
    // Evaluate till stable
    vlSymsp->__Vm_activity = true;
    do {
        VL_DEBUG_IF(VL_DBG_MSGF("+ Clock loop\n"););
        VminiRV_SoC___024root___eval(&(vlSymsp->TOP));
    } while (0);
    // Evaluate cleanup
}

//============================================================
// Utilities

VerilatedContext* VminiRV_SoC::contextp() const {
    return vlSymsp->_vm_contextp__;
}

const char* VminiRV_SoC::name() const {
    return vlSymsp->name();
}

//============================================================
// Invoke final blocks

VL_ATTR_COLD void VminiRV_SoC::final() {
    VminiRV_SoC___024root___final(&(vlSymsp->TOP));
}

//============================================================
// Trace configuration

void VminiRV_SoC___024root__trace_init_top(VminiRV_SoC___024root* vlSelf, VerilatedVcd* tracep);

VL_ATTR_COLD static void trace_init(void* voidSelf, VerilatedVcd* tracep, uint32_t code) {
    // Callback from tracep->open()
    VminiRV_SoC___024root* const __restrict vlSelf VL_ATTR_UNUSED = static_cast<VminiRV_SoC___024root*>(voidSelf);
    VminiRV_SoC__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    if (!vlSymsp->_vm_contextp__->calcUnusedSigs()) {
        VL_FATAL_MT(__FILE__, __LINE__, __FILE__,
            "Turning on wave traces requires Verilated::traceEverOn(true) call before time 0.");
    }
    vlSymsp->__Vm_baseCode = code;
    tracep->scopeEscape(' ');
    tracep->pushNamePrefix(std::string{vlSymsp->name()} + ' ');
    VminiRV_SoC___024root__trace_init_top(vlSelf, tracep);
    tracep->popNamePrefix();
    tracep->scopeEscape('.');
}

VL_ATTR_COLD void VminiRV_SoC___024root__trace_register(VminiRV_SoC___024root* vlSelf, VerilatedVcd* tracep);

VL_ATTR_COLD void VminiRV_SoC::trace(VerilatedVcdC* tfp, int levels, int options) {
    if (false && levels && options) {}  // Prevent unused
    tfp->spTrace()->addInitCb(&trace_init, &(vlSymsp->TOP));
    VminiRV_SoC___024root__trace_register(&(vlSymsp->TOP), tfp->spTrace());
}
