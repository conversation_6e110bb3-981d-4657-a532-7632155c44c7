`timescale 1ns / 1ps

`include "defines.vh"

module SEXT (
    input  wire [31:0]  inst,      
    input  wire [2:0]   sext_op,   
    output reg  [31:0]  ext         
);

    // 根据sext_op选择立即数类型并进行符号扩展
    always @(*) begin
        case (sext_op)
            `EXT_I: ext = {{20{inst[31]}}, inst[31:20]};              
            `EXT_S: ext = {{20{inst[31]}}, inst[31:25], inst[11:7]};       
            `EXT_B: ext = {{19{inst[31]}}, inst[31], inst[7], inst[30:25], inst[11:8], 1'b0}; 
            `EXT_U: ext = {inst[31:12], 12'b0};                            
            `EXT_J: ext = {{11{inst[31]}}, inst[31], inst[19:12], inst[20], inst[30:21], 1'b0}; 
            `EXT_shift: ext = {27'b0, inst[24:20]};                         
            default: ext = {{20{inst[31]}}, inst[31:20]};                 
        endcase
    end

endmodule