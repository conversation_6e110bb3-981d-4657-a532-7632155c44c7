// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Symbol table implementation internals

#include "VminiRV_SoC__Syms.h"
#include "VminiRV_SoC.h"
#include "VminiRV_SoC___024root.h"

// FUNCTIONS
VminiRV_SoC__Syms::~VminiRV_SoC__Syms()
{
}

VminiRV_SoC__Syms::VminiRV_SoC__Syms(VerilatedContext* contextp, const char* namep,VminiRV_SoC* modelp)
    : VerilatedSyms{contextp}
    // Setup internal state of the Syms class
    , __Vm_modelp{modelp}
    // Setup module instances
    , TOP{this, namep}
{
    // Configure time unit / time precision
    _vm_contextp__->timeunit(-9);
    _vm_contextp__->timeprecision(-12);
    // Setup each module's pointers to their submodules
    // Setup each module's pointer back to symbol table (for public functions)
    TOP.__Vconfigure(true);
    // Setup scopes
    __Vscope_miniRV_SoC__Mem_DRAM.configure(this, name(), "miniRV_SoC.Mem_DRAM", "Mem_DRAM", -9, VerilatedScope::SCOPE_OTHER);
    __Vscope_miniRV_SoC__Mem_IROM.configure(this, name(), "miniRV_SoC.Mem_IROM", "Mem_IROM", -9, VerilatedScope::SCOPE_OTHER);
}
