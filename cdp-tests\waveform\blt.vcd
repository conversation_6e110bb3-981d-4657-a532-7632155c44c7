$version Generated by VerilatedVcd $end
$date Tue Jul  8 10:26:39 2025 $end
$timescale 1ps $end

 $scope module TOP $end
  $var wire  1 -! DN_A0 $end
  $var wire  1 .! DN_A1 $end
  $var wire  1 /! DN_B0 $end
  $var wire  1 0! DN_B1 $end
  $var wire  1 1! DN_C0 $end
  $var wire  1 2! DN_C1 $end
  $var wire  1 3! DN_D0 $end
  $var wire  1 4! DN_D1 $end
  $var wire  1 ;! DN_DP0 $end
  $var wire  1 <! DN_DP1 $end
  $var wire  1 5! DN_E0 $end
  $var wire  1 6! DN_E1 $end
  $var wire  1 7! DN_F0 $end
  $var wire  1 8! DN_F1 $end
  $var wire  1 9! DN_G0 $end
  $var wire  1 :! DN_G1 $end
  $var wire  5 +! button [4:0] $end
  $var wire  1 @! debug_wb_ena $end
  $var wire  1 >! debug_wb_have_inst $end
  $var wire 32 ?! debug_wb_pc [31:0] $end
  $var wire  5 A! debug_wb_reg [4:0] $end
  $var wire 32 B! debug_wb_value [31:0] $end
  $var wire  8 ,! dig_en [7:0] $end
  $var wire  1 )! fpga_clk $end
  $var wire  1 (! fpga_rst $end
  $var wire 16 =! led [15:0] $end
  $var wire 16 *! sw [15:0] $end
  $scope module miniRV_SoC $end
   $var wire 32 c Bus_addr [31:0] $end
   $var wire 32 C! Bus_rdata [31:0] $end
   $var wire 32 e Bus_wdata [31:0] $end
   $var wire  1 d Bus_we $end
   $var wire  1 -! DN_A0 $end
   $var wire  1 .! DN_A1 $end
   $var wire  1 /! DN_B0 $end
   $var wire  1 0! DN_B1 $end
   $var wire  1 1! DN_C0 $end
   $var wire  1 2! DN_C1 $end
   $var wire  1 3! DN_D0 $end
   $var wire  1 4! DN_D1 $end
   $var wire  1 ;! DN_DP0 $end
   $var wire  1 <! DN_DP1 $end
   $var wire  1 5! DN_E0 $end
   $var wire  1 6! DN_E1 $end
   $var wire  1 7! DN_F0 $end
   $var wire  1 8! DN_F1 $end
   $var wire  1 9! DN_G0 $end
   $var wire  1 :! DN_G1 $end
   $var wire 32 c addr_bridge2btn [31:0] $end
   $var wire 32 c addr_bridge2dig [31:0] $end
   $var wire 32 c addr_bridge2dram [31:0] $end
   $var wire 32 c addr_bridge2led [31:0] $end
   $var wire 32 c addr_bridge2sw [31:0] $end
   $var wire 32 c addr_bridge2tim [31:0] $end
   $var wire  5 +! button [4:0] $end
   $var wire  1 )! clk_bridge2btn $end
   $var wire  1 )! clk_bridge2dig $end
   $var wire  1 )! clk_bridge2dram $end
   $var wire  1 )! clk_bridge2led $end
   $var wire  1 )! clk_bridge2sw $end
   $var wire  1 )! clk_bridge2tim $end
   $var wire  1 )! cpu_clk $end
   $var wire  1 @! debug_wb_ena $end
   $var wire  1 >! debug_wb_have_inst $end
   $var wire 32 ?! debug_wb_pc [31:0] $end
   $var wire  5 A! debug_wb_reg [4:0] $end
   $var wire 32 B! debug_wb_value [31:0] $end
   $var wire  8 ,! dig_en [7:0] $end
   $var wire  1 )! fpga_clk $end
   $var wire  1 (! fpga_rst $end
   $var wire 32 b inst [31:0] $end
   $var wire 16 a inst_addr [15:0] $end
   $var wire 16 =! led [15:0] $end
   $var wire  1 J! pll_clk $end
   $var wire  1 I! pll_lock $end
   $var wire 32 G! rdata_btn2bridge [31:0] $end
   $var wire 32 D! rdata_dram2bridge [31:0] $end
   $var wire 32 F! rdata_sw2bridge [31:0] $end
   $var wire 32 E! rdata_tim2bridge [31:0] $end
   $var wire  1 (! rst_bridge2btn $end
   $var wire  1 (! rst_bridge2dig $end
   $var wire  1 (! rst_bridge2led $end
   $var wire  1 (! rst_bridge2sw $end
   $var wire  1 (! rst_bridge2tim $end
   $var wire 16 *! sw [15:0] $end
   $var wire 32 e wdata_bridge2dig [31:0] $end
   $var wire 32 e wdata_bridge2dram [31:0] $end
   $var wire 32 e wdata_bridge2led [31:0] $end
   $var wire 32 e wdata_bridge2tim [31:0] $end
   $var wire  1 g we_bridge2dig $end
   $var wire  1 f we_bridge2dram $end
   $var wire  1 i we_bridge2led $end
   $var wire  1 h we_bridge2tim $end
   $scope module Bridge $end
    $var wire  6 p access_bit [5:0] $end
    $var wire  1 o access_btn $end
    $var wire  1 k access_dig $end
    $var wire  1 m access_led $end
    $var wire  1 j access_mem $end
    $var wire  1 n access_sw $end
    $var wire  1 l access_tim $end
    $var wire 32 c addr_from_cpu [31:0] $end
    $var wire 32 c addr_to_btn [31:0] $end
    $var wire 32 c addr_to_dig [31:0] $end
    $var wire 32 c addr_to_dram [31:0] $end
    $var wire 32 c addr_to_led [31:0] $end
    $var wire 32 c addr_to_sw [31:0] $end
    $var wire 32 c addr_to_tim [31:0] $end
    $var wire  1 )! clk_from_cpu $end
    $var wire  1 )! clk_to_btn $end
    $var wire  1 )! clk_to_dig $end
    $var wire  1 )! clk_to_dram $end
    $var wire  1 )! clk_to_led $end
    $var wire  1 )! clk_to_sw $end
    $var wire  1 )! clk_to_tim $end
    $var wire 32 G! rdata_from_btn [31:0] $end
    $var wire 32 D! rdata_from_dram [31:0] $end
    $var wire 32 F! rdata_from_sw [31:0] $end
    $var wire 32 E! rdata_from_tim [31:0] $end
    $var wire 32 C! rdata_to_cpu [31:0] $end
    $var wire  1 (! rst_from_cpu $end
    $var wire  1 (! rst_to_btn $end
    $var wire  1 (! rst_to_dig $end
    $var wire  1 (! rst_to_led $end
    $var wire  1 (! rst_to_sw $end
    $var wire  1 (! rst_to_tim $end
    $var wire 32 e wdata_from_cpu [31:0] $end
    $var wire 32 e wdata_to_dig [31:0] $end
    $var wire 32 e wdata_to_dram [31:0] $end
    $var wire 32 e wdata_to_led [31:0] $end
    $var wire 32 e wdata_to_tim [31:0] $end
    $var wire  1 d we_from_cpu $end
    $var wire  1 g we_to_dig $end
    $var wire  1 f we_to_dram $end
    $var wire  1 i we_to_led $end
    $var wire  1 h we_to_tim $end
   $upscope $end
   $scope module Core_cpu $end
    $var wire 32 c Bus_addr [31:0] $end
    $var wire 32 C! Bus_rdata [31:0] $end
    $var wire 32 e Bus_wdata [31:0] $end
    $var wire  1 d Bus_we $end
    $var wire 32 z alu_A [31:0] $end
    $var wire 32 } alu_B [31:0] $end
    $var wire 32 c alu_C [31:0] $end
    $var wire  1 ~ alu_f $end
    $var wire  4 !! alu_op [3:0] $end
    $var wire  1 "! alub_sel $end
    $var wire  1 )! cpu_clk $end
    $var wire  1 (! cpu_rst $end
    $var wire  1 @! debug_wb_ena $end
    $var wire  1 >! debug_wb_have_inst $end
    $var wire 32 ?! debug_wb_pc [31:0] $end
    $var wire  5 A! debug_wb_reg [4:0] $end
    $var wire 32 B! debug_wb_value [31:0] $end
    $var wire  3 x funct3 [2:0] $end
    $var wire  7 y funct7 [6:0] $end
    $var wire  1 - have_prev_inst $end
    $var wire 32 b inst [31:0] $end
    $var wire 16 a inst_addr [15:0] $end
    $var wire  2 %! npc_op [1:0] $end
    $var wire 32 s npc_pc4 [31:0] $end
    $var wire  7 t opcode [6:0] $end
    $var wire 32 q pc_current [31:0] $end
    $var wire 32 r pc_next [31:0] $end
    $var wire 32 s pc_plus4 [31:0] $end
    $var wire 32 ) prev_pc [31:0] $end
    $var wire  5 * prev_rd [4:0] $end
    $var wire 32 , prev_rf_wD [31:0] $end
    $var wire  1 + prev_rf_we $end
    $var wire  1 d ram_we $end
    $var wire  5 w rd [4:0] $end
    $var wire 32 z rf_rD1 [31:0] $end
    $var wire 32 e rf_rD2 [31:0] $end
    $var wire 32 H! rf_wD [31:0] $end
    $var wire  1 { rf_we $end
    $var wire  2 | rf_wsel [1:0] $end
    $var wire  5 u rs1 [4:0] $end
    $var wire  5 v rs2 [4:0] $end
    $var wire 32 #! sext_ext [31:0] $end
    $var wire  3 $! sext_op [2:0] $end
    $scope module U_ALU $end
     $var wire 32 z A [31:0] $end
     $var wire 32 } B [31:0] $end
     $var wire 32 c C [31:0] $end
     $var wire  4 !! alu_op [3:0] $end
     $var wire  1 ~ f $end
    $upscope $end
    $scope module U_Ctrl $end
     $var wire  1 ~ alu_f $end
     $var wire  4 !! alu_op [3:0] $end
     $var wire  1 "! alub_sel $end
     $var wire  3 x funct3 [2:0] $end
     $var wire  7 y funct7 [6:0] $end
     $var wire  2 %! npc_op [1:0] $end
     $var wire  7 t opcode [6:0] $end
     $var wire  1 d ram_we $end
     $var wire  1 { rf_we $end
     $var wire  2 | rf_wsel [1:0] $end
     $var wire  3 $! sext_op [2:0] $end
    $upscope $end
    $scope module U_NPC $end
     $var wire 32 c ALU_C [31:0] $end
     $var wire 32 #! IMM [31:0] $end
     $var wire 32 q PC [31:0] $end
     $var wire 32 r npc [31:0] $end
     $var wire  2 %! npc_op [1:0] $end
     $var wire 32 s pc4 [31:0] $end
    $upscope $end
    $scope module U_PC $end
     $var wire  1 )! clk $end
     $var wire 32 r din [31:0] $end
     $var wire 32 q pc [31:0] $end
     $var wire  1 (! rst $end
    $upscope $end
    $scope module U_RF $end
     $var wire  1 )! clk $end
     $var wire 32 z rD1 [31:0] $end
     $var wire 32 e rD2 [31:0] $end
     $var wire  5 u rR1 [4:0] $end
     $var wire  5 v rR2 [4:0] $end
     $var wire 32 A registers[0] [31:0] $end
     $var wire 32 K registers[10] [31:0] $end
     $var wire 32 L registers[11] [31:0] $end
     $var wire 32 M registers[12] [31:0] $end
     $var wire 32 N registers[13] [31:0] $end
     $var wire 32 O registers[14] [31:0] $end
     $var wire 32 P registers[15] [31:0] $end
     $var wire 32 Q registers[16] [31:0] $end
     $var wire 32 R registers[17] [31:0] $end
     $var wire 32 S registers[18] [31:0] $end
     $var wire 32 T registers[19] [31:0] $end
     $var wire 32 B registers[1] [31:0] $end
     $var wire 32 U registers[20] [31:0] $end
     $var wire 32 V registers[21] [31:0] $end
     $var wire 32 W registers[22] [31:0] $end
     $var wire 32 X registers[23] [31:0] $end
     $var wire 32 Y registers[24] [31:0] $end
     $var wire 32 Z registers[25] [31:0] $end
     $var wire 32 [ registers[26] [31:0] $end
     $var wire 32 \ registers[27] [31:0] $end
     $var wire 32 ] registers[28] [31:0] $end
     $var wire 32 ^ registers[29] [31:0] $end
     $var wire 32 C registers[2] [31:0] $end
     $var wire 32 _ registers[30] [31:0] $end
     $var wire 32 ` registers[31] [31:0] $end
     $var wire 32 D registers[3] [31:0] $end
     $var wire 32 E registers[4] [31:0] $end
     $var wire 32 F registers[5] [31:0] $end
     $var wire 32 G registers[6] [31:0] $end
     $var wire 32 H registers[7] [31:0] $end
     $var wire 32 I registers[8] [31:0] $end
     $var wire 32 J registers[9] [31:0] $end
     $var wire 32 H! wD [31:0] $end
     $var wire  5 w wR [4:0] $end
     $var wire  1 { we $end
    $upscope $end
    $scope module U_SEXT $end
     $var wire 32 #! ext [31:0] $end
     $var wire 32 b inst [31:0] $end
     $var wire  3 $! sext_op [2:0] $end
    $upscope $end
   $upscope $end
   $scope module Mem_DRAM $end
    $var wire 32 K! ADDR_BITS [31:0] $end
    $var wire 16 &! a [15:0] $end
    $var wire  1 )! clk $end
    $var wire 32 e d [31:0] $end
    $var wire 32 # i [31:0] $end
    $var wire 32 $ j [31:0] $end
    $var wire 32 % mem_file [31:0] $end
    $var wire 32 D! spo [31:0] $end
    $var wire  1 f we $end
   $upscope $end
   $scope module Mem_IROM $end
    $var wire 32 K! ADDR_BITS [31:0] $end
    $var wire 16 '! a [15:0] $end
    $var wire 32 & i [31:0] $end
    $var wire 32 ' j [31:0] $end
    $var wire 32 ( mem_file [31:0] $end
    $var wire 32 b spo [31:0] $end
   $upscope $end
   $scope module U_Button $end
    $var wire 32 c addr [31:0] $end
    $var wire  5 +! button [4:0] $end
    $var wire  5 0 button_stable [4:0] $end
    $var wire  5 . button_sync1 [4:0] $end
    $var wire  5 / button_sync2 [4:0] $end
    $var wire  1 )! clk $end
    $var wire 20 1 debounce_cnt[0] [19:0] $end
    $var wire 20 2 debounce_cnt[1] [19:0] $end
    $var wire 20 3 debounce_cnt[2] [19:0] $end
    $var wire 20 4 debounce_cnt[3] [19:0] $end
    $var wire 20 5 debounce_cnt[4] [19:0] $end
    $var wire 32 G! rdata [31:0] $end
    $var wire  1 (! rst $end
   $upscope $end
   $scope module U_Dig $end
    $var wire  1 -! DN_A0 $end
    $var wire  1 .! DN_A1 $end
    $var wire  1 /! DN_B0 $end
    $var wire  1 0! DN_B1 $end
    $var wire  1 1! DN_C0 $end
    $var wire  1 2! DN_C1 $end
    $var wire  1 3! DN_D0 $end
    $var wire  1 4! DN_D1 $end
    $var wire  1 ;! DN_DP0 $end
    $var wire  1 <! DN_DP1 $end
    $var wire  1 5! DN_E0 $end
    $var wire  1 6! DN_E1 $end
    $var wire  1 7! DN_F0 $end
    $var wire  1 8! DN_F1 $end
    $var wire  1 9! DN_G0 $end
    $var wire  1 :! DN_G1 $end
    $var wire 32 c addr [31:0] $end
    $var wire  1 )! clk $end
    $var wire  4 < current_digit [3:0] $end
    $var wire  1 8 data_pending $end
    $var wire  8 ,! dig_en [7:0] $end
    $var wire 32 6 display_data [31:0] $end
    $var wire 32 7 pending_data [31:0] $end
    $var wire  1 (! rst $end
    $var wire 16 : scan_clk_cnt [15:0] $end
    $var wire  3 9 scan_cnt [2:0] $end
    $var wire  7 = seg_code [6:0] $end
    $var wire 24 ; update_limit_cnt [23:0] $end
    $var wire 32 e wdata [31:0] $end
    $var wire  1 g we $end
   $upscope $end
   $scope module U_Led $end
    $var wire 32 c addr [31:0] $end
    $var wire  1 )! clk $end
    $var wire 16 =! led [15:0] $end
    $var wire  1 (! rst $end
    $var wire 32 e wdata [31:0] $end
    $var wire  1 i we $end
   $upscope $end
   $scope module U_Switch $end
    $var wire 32 c addr [31:0] $end
    $var wire  1 )! clk $end
    $var wire 32 F! rdata [31:0] $end
    $var wire  1 (! rst $end
    $var wire 16 *! sw [15:0] $end
   $upscope $end
   $scope module U_Timer $end
    $var wire 32 c addr [31:0] $end
    $var wire  1 )! clk $end
    $var wire 32 > counter0 [31:0] $end
    $var wire 32 ? counter1 [31:0] $end
    $var wire 32 E! rdata [31:0] $end
    $var wire  1 (! rst $end
    $var wire 32 @ threshold [31:0] $end
    $var wire 32 e wdata [31:0] $end
    $var wire  1 h we $end
   $upscope $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#9
b00000000000100000000000000000000 #
b00000000000100000000000000000000 $
b10000000000000000000000000101100 %
b00000000000100000000000000000000 &
b00000000000100000000000000000000 '
b10000000000000000000000000101011 (
b00000000000000000000000000000000 )
b00000 *
0+
b00000000000000000000000000000000 ,
0-
b00000 .
b00000 /
b00000 0
b00000000000000000000 1
b00000000000000000000 2
b00000000000000000000 3
b00000000000000000000 4
b00000000000000000000 5
b00000000000000000000000000000000 6
b00000000000000000000000000000000 7
08
b000 9
b0000000000000000 :
b000000000000000000000000 ;
b0000 <
b0111111 =
b00000000000000000000000000000000 >
b00000000000000000000000000000000 ?
b00000000000000000000000000000000 @
b00000000000000000000000000000000 A
b00000000000000000000000000000000 B
b00000000000000000000000000000000 C
b00000000000000000000000000000000 D
b00000000000000000000000000000000 E
b00000000000000000000000000000000 F
b00000000000000000000000000000000 G
b00000000000000000000000000000000 H
b00000000000000000000000000000000 I
b00000000000000000000000000000000 J
b00000000000000000000000000000000 K
b00000000000000000000000000000000 L
b00000000000000000000000000000000 M
b00000000000000000000000000000000 N
b00000000000000000000000000000000 O
b00000000000000000000000000000000 P
b00000000000000000000000000000000 Q
b00000000000000000000000000000000 R
b00000000000000000000000000000000 S
b00000000000000000000000000000000 T
b00000000000000000000000000000000 U
b00000000000000000000000000000000 V
b00000000000000000000000000000000 W
b00000000000000000000000000000000 X
b00000000000000000000000000000000 Y
b00000000000000000000000000000000 Z
b00000000000000000000000000000000 [
b00000000000000000000000000000000 \
b00000000000000000000000000000000 ]
b00000000000000000000000000000000 ^
b00000000000000000000000000000000 _
b00000000000000000000000000000000 `
b0000000000000000 a
b00000000010000000000000001101111 b
b00000000000000000000000000000000 c
0d
b00000000000000000000000000000000 e
0f
0g
0h
0i
1j
0k
0l
0m
0n
0o
b100000 p
b00000000000000000000000000000000 q
b00000000000000000000000000000100 r
b00000000000000000000000000000100 s
b1101111 t
b00000 u
b00100 v
b00000 w
b000 x
b0000000 y
b00000000000000000000000000000000 z
1{
b10 |
b00000000000000000000000000000000 }
0~
b0000 !!
0"!
b00000000000000000000000000000100 #!
b100 $!
b01 %!
b0000000000000000 &!
b0000000000000000 '!
1(!
0)!
b0000000000000000 *!
b00000 +!
b00000001 ,!
1-!
1.!
1/!
10!
11!
12!
13!
14!
15!
16!
17!
18!
09!
0:!
0;!
0<!
b0000000000000000 =!
0>!
b00000000000000000000000000000000 ?!
0@!
b00000 A!
b00000000000000000000000000000000 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 E!
b00000000000000000000000000000000 F!
b00000000000000000000000000000000 G!
b00000000000000000000000000000100 H!
0I!
0J!
b00000000000000000000000000010000 K!
#10
b00000001011111010111100001000000 @
1)!
#15
0)!
#19
#20
1)!
#25
0)!
#29
#30
1)!
#35
0)!
#39
#40
1)!
#45
0)!
#49
#50
1)!
#55
0)!
#59
#60
1)!
#65
0)!
#69
#70
1)!
#75
0)!
#79
#80
1)!
#85
0)!
#89
#90
1)!
#95
0)!
#99
#100
1)!
#105
0)!
#109
#110
1)!
#115
0)!
#119
#120
1)!
#125
0)!
#129
#130
1)!
#135
0)!
#139
#140
1)!
#145
0)!
#149
#150
1)!
#155
0)!
#159
#160
1)!
#165
0)!
#169
#170
1)!
#175
0)!
#179
#180
1)!
#185
0)!
#189
#190
1)!
#195
0)!
#199
#200
1)!
#205
0)!
#209
0(!
#210
1+
b00000000000000000000000000000100 ,
1-
b0000000000000001 :
b000000000000000000000001 ;
b00000000000000000000000000000001 ?
b0000000000000100 a
b00000000001000000000000110010011 b
b00000000000000000000000000000010 c
b00000000000000000000000000000100 q
b00000000000000000000000000001000 r
b00000000000000000000000000001000 s
b0010011 t
b00010 v
b00011 w
b00 |
b00000000000000000000000000000010 }
1"!
b00000000000000000000000000000010 #!
b000 $!
b00 %!
b0000000000000001 '!
1)!
1>!
1@!
b00000000000000000000000000000100 B!
b00000000000000000000000000000010 H!
#215
0)!
#219
#220
b00000000000000000000000000000100 )
b00011 *
b00000000000000000000000000000010 ,
b0000000000000010 :
b000000000000000000000010 ;
b00000000000000000000000000000010 ?
b00000000000000000000000000000010 D
b0000000000001000 a
b00000000000000000000000010010011 b
b00000000000000000000000000000000 c
b00000000000000000000000000001000 q
b00000000000000000000000000001100 r
b00000000000000000000000000001100 s
b00000 v
b00001 w
b00000000000000000000000000000000 }
b00000000000000000000000000000000 #!
b0000000000000010 '!
1)!
b00000000000000000000000000000100 ?!
b00011 A!
b00000000000000000000000000000010 B!
b00000000000000000000000000000000 H!
#225
0)!
#229
#230
b00000000000000000000000000001000 )
b00001 *
b00000000000000000000000000000000 ,
b0000000000000011 :
b000000000000000000000011 ;
b00000000000000000000000000000011 ?
b0000000000001100 a
b00000000000100000000000100010011 b
b00000000000000000000000000000001 c
b00000000000000000000000000001100 q
b00000000000000000000000000010000 r
b00000000000000000000000000010000 s
b00001 v
b00010 w
b00000000000000000000000000000001 }
b00000000000000000000000000000001 #!
b0000000000000011 '!
1)!
b00000000000000000000000000001000 ?!
b00001 A!
b00000000000000000000000000000000 B!
b00000000000000000000000000000001 H!
#235
0)!
#239
#240
b00000000000000000000000000001100 )
b00010 *
b00000000000000000000000000000001 ,
b0000000000000100 :
b000000000000000000000100 ;
b00000000000000000000000000000100 ?
b00000000000000000000000000000001 C
b0000000000010000 a
b00000000001000001100011001100011 b
b00000000000000000000000000000000 c
b00000000000000000000000000000001 e
b00000000000000000000000000010000 q
b00000000000000000000000000011100 r
b00000000000000000000000000010100 s
b1100011 t
b00001 u
b00010 v
b01100 w
b100 x
0{
1~
b1011 !!
0"!
b00000000000000000000000000001100 #!
b010 $!
b01 %!
b0000000000000100 '!
1)!
b00000000000000000000000000001100 ?!
b00010 A!
b00000000000000000000000000000001 B!
b00000000000000000000000000000000 H!
#245
0)!
#249
#250
b00000000000000000000000000010000 )
b01100 *
0+
b00000000000000000000000000000000 ,
b0000000000000101 :
b000000000000000000000101 ;
b00000000000000000000000000000101 ?
b0000000000011100 a
b11111110001000001100111011100011 b
b00000000000000000000000000011100 q
b00000000000000000000000000011000 r
b00000000000000000000000000100000 s
b11101 w
b1111111 y
b11111111111111111111111111111100 #!
b0000000000000111 '!
1)!
b00000000000000000000000000010000 ?!
0@!
b01100 A!
b00000000000000000000000000000000 B!
#255
0)!
#259
#260
b00000000000000000000000000011100 )
b11101 *
b0000000000000110 :
b000000000000000000000110 ;
b00000000000000000000000000000110 ?
b0000000000011000 a
b00000000001100000001011001100011 b
b00000000000000000000000000000010 e
b00000000000000000000000000011000 q
b00000000000000000000000000011100 r
b00000000000000000000000000011100 s
b00000 u
b00011 v
b01100 w
b001 x
b0000000 y
b00000000000000000000000000000010 }
0~
b1010 !!
b00000000000000000000000000001100 #!
b00 %!
b0000000000000110 '!
1)!
b00000000000000000000000000011100 ?!
b11101 A!
#265
0)!
#269
#270
b00000000000000000000000000011000 )
b01100 *
b0000000000000111 :
b000000000000000000000111 ;
b00000000000000000000000000000111 ?
b0000000000011100 a
b11111110001000001100111011100011 b
b00000000000000000000000000000001 e
b00000000000000000000000000011100 q
b00000000000000000000000000011000 r
b00000000000000000000000000100000 s
b00001 u
b00010 v
b11101 w
b100 x
b1111111 y
b00000000000000000000000000000001 }
1~
b1011 !!
b11111111111111111111111111111100 #!
b01 %!
b0000000000000111 '!
1)!
b00000000000000000000000000011000 ?!
b01100 A!
#275
0)!
#279
#280
b00000000000000000000000000011100 )
b11101 *
b0000000000001000 :
b000000000000000000001000 ;
b00000000000000000000000000001000 ?
b0000000000011000 a
b00000000001100000001011001100011 b
b00000000000000000000000000000010 e
b00000000000000000000000000011000 q
b00000000000000000000000000011100 r
b00000000000000000000000000011100 s
b00000 u
b00011 v
b01100 w
b001 x
b0000000 y
b00000000000000000000000000000010 }
0~
b1010 !!
b00000000000000000000000000001100 #!
b00 %!
b0000000000000110 '!
1)!
b00000000000000000000000000011100 ?!
b11101 A!
#285
0)!
