`timescale 1ns / 1ps

`include "defines.vh"

module Dig (
    input  wire         rst,        
    input  wire         clk,        
    input  wire [31:0]  addr,       
    input  wire         we,         
    input  wire [31:0]  wdata,      

    // 数码管硬件接口
    output reg  [ 7:0]  dig_en,   
    output reg          DN_A0, DN_A1,  
    output reg          DN_B0, DN_B1, 
    output reg          DN_C0, DN_C1, 
    output reg          DN_D0, DN_D1,  
    output reg          DN_E0, DN_E1,  
    output reg          DN_F0, DN_F1, 
    output reg          DN_G0, DN_G1, 
    output reg          DN_DP0, DN_DP1
);

    // 数据寄存器，存储要显示的32位数据
    reg [31:0] display_data;
    reg [31:0] pending_data;  // 待更新的数据
    reg data_pending;         // 是否有待更新的数据

    // 扫描计数器，动态扫描数码管
    reg [2:0] scan_cnt;
    reg [15:0] scan_clk_cnt;

    // 数据更新限制计数器 (限制更新频率为2Hz)
    reg [23:0] update_limit_cnt;

    // 当前扫描位的4位数据
    wire [3:0] current_digit = (scan_cnt == 3'd0) ? display_data[3:0]   :
                               (scan_cnt == 3'd1) ? display_data[7:4]   :
                               (scan_cnt == 3'd2) ? display_data[11:8]  :
                               (scan_cnt == 3'd3) ? display_data[15:12] :
                               (scan_cnt == 3'd4) ? display_data[19:16] :
                               (scan_cnt == 3'd5) ? display_data[23:20] :
                               (scan_cnt == 3'd6) ? display_data[27:24] :
                                                     display_data[31:28];

    // 7段译码器
    reg [6:0] seg_code;
    always @(*) begin
        case (current_digit)
            4'h0: seg_code = 7'h3f;  
            4'h1: seg_code = 7'h06;  
            4'h2: seg_code = 7'h5b;  
            4'h3: seg_code = 7'h4f; 
            4'h4: seg_code = 7'h66; 
            4'h5: seg_code = 7'h6d;  
            4'h6: seg_code = 7'h7d;  
            4'h7: seg_code = 7'h07;  
            4'h8: seg_code = 7'h7f;  
            4'h9: seg_code = 7'h6f;  
            4'hA: seg_code = 7'h77;  
            4'hB: seg_code = 7'h7c;  
            4'hC: seg_code = 7'h39;  
            4'hD: seg_code = 7'h5e;  
            4'hE: seg_code = 7'h79;  
            4'hF: seg_code = 7'h71; 
            default: seg_code = 7'h00; 
        endcase
    end

    // 写数据寄存器 - 带频率限制
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            display_data <= 32'h0;
            pending_data <= 32'h0;
            data_pending <= 1'b0;
            update_limit_cnt <= 24'h0;
        end else begin
            // CPU写入新数据时，先存储到pending_data
            if (we && addr == `PERI_ADDR_DIG) begin
                pending_data <= wdata;
                data_pending <= 1'b1;
            end

            // 限制更新频率：每0.5秒更新一次 (25MHz/12500000 = 2Hz)
            if (update_limit_cnt >= 24'd12500000) begin
                update_limit_cnt <= 24'h0;
                if (data_pending) begin
                    display_data <= pending_data;
                    data_pending <= 1'b0;
                end
            end else begin
                update_limit_cnt <= update_limit_cnt + 1'b1;
            end
        end
    end

    // 扫描时钟分频 (1KHz扫描频率)
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            scan_clk_cnt <= 16'h0;
            scan_cnt <= 3'h0;
        end else begin
            if (scan_clk_cnt >= 16'd25000) begin  // 25MHz/25000 = 1KHz
                scan_clk_cnt <= 16'h0;
                scan_cnt <= scan_cnt + 1'b1;
            end else begin
                scan_clk_cnt <= scan_clk_cnt + 1'b1;
            end
        end
    end

    // 位选信号生成
    always @(*) begin
        dig_en = 8'h00;
        dig_en[scan_cnt] = 1'b1;
    end

    // 段选信号输出
    always @(*) begin
        {DN_G0, DN_F0, DN_E0, DN_D0, DN_C0, DN_B0, DN_A0} = seg_code;
        {DN_G1, DN_F1, DN_E1, DN_D1, DN_C1, DN_B1, DN_A1} = seg_code;
        DN_DP0 = 1'b0;  // 小数点不亮
        DN_DP1 = 1'b0;  // 小数点不亮
    end

endmodule
