
auipc:     file format elf32-littleriscv


Disassembly of section .text.init:

00000000 <_start>:
   0:	0040006f          	jal	x0,4 <reset_vector>

00000004 <reset_vector>:
   4:	00000013          	addi	x0,x0,0
   8:	00002517          	auipc	x10,0x2
   c:	71c50513          	addi	x10,x10,1820 # 2724 <_end+0x724>
  10:	004005ef          	jal	x11,14 <reset_vector+0x10>
  14:	40b50533          	sub	x10,x10,x11
  18:	000023b7          	lui	x7,0x2
  1c:	71038393          	addi	x7,x7,1808 # 2710 <_end+0x710>
  20:	00200193          	addi	x3,x0,2
  24:	02751463          	bne	x10,x7,4c <fail>

00000028 <test_3>:
  28:	ffffe517          	auipc	x10,0xffffe
  2c:	8fc50513          	addi	x10,x10,-1796 # ffffd924 <_end+0xffffb924>
  30:	004005ef          	jal	x11,34 <test_3+0xc>
  34:	40b50533          	sub	x10,x10,x11
  38:	ffffe3b7          	lui	x7,0xffffe
  3c:	8f038393          	addi	x7,x7,-1808 # ffffd8f0 <_end+0xffffb8f0>
  40:	00300193          	addi	x3,x0,3
  44:	00751463          	bne	x10,x7,4c <fail>
  48:	00301e63          	bne	x0,x3,64 <pass>

0000004c <fail>:
  4c:	00018063          	beq	x3,x0,4c <fail>
  50:	00119193          	slli	x3,x3,0x1
  54:	0011e193          	ori	x3,x3,1
  58:	05d00893          	addi	x17,x0,93
  5c:	00018513          	addi	x10,x3,0
  60:	00000073          	ecall

00000064 <pass>:
  64:	00100193          	addi	x3,x0,1
  68:	05d00893          	addi	x17,x0,93
  6c:	00000513          	addi	x10,x0,0
  70:	00000073          	ecall
  74:	c0001073          	unimp
  78:	0000                	c.unimp
  7a:	0000                	c.unimp
  7c:	0000                	c.unimp
  7e:	0000                	c.unimp
