
bne:     file format elf32-littleriscv


Disassembly of section .text.init:

00000000 <_start>:
   0:	0040006f          	jal	x0,4 <reset_vector>

00000004 <reset_vector>:
   4:	00200193          	addi	x3,x0,2
   8:	00000093          	addi	x1,x0,0
   c:	00100113          	addi	x2,x0,1
  10:	00209663          	bne	x1,x2,1c <reset_vector+0x18>
  14:	2a301a63          	bne	x0,x3,2c8 <fail>
  18:	00301663          	bne	x0,x3,24 <test_3>
  1c:	fe209ee3          	bne	x1,x2,18 <reset_vector+0x14>
  20:	2a301463          	bne	x0,x3,2c8 <fail>

00000024 <test_3>:
  24:	00300193          	addi	x3,x0,3
  28:	00100093          	addi	x1,x0,1
  2c:	00000113          	addi	x2,x0,0
  30:	00209663          	bne	x1,x2,3c <test_3+0x18>
  34:	28301a63          	bne	x0,x3,2c8 <fail>
  38:	00301663          	bne	x0,x3,44 <test_4>
  3c:	fe209ee3          	bne	x1,x2,38 <test_3+0x14>
  40:	28301463          	bne	x0,x3,2c8 <fail>

00000044 <test_4>:
  44:	00400193          	addi	x3,x0,4
  48:	fff00093          	addi	x1,x0,-1
  4c:	00100113          	addi	x2,x0,1
  50:	00209663          	bne	x1,x2,5c <test_4+0x18>
  54:	26301a63          	bne	x0,x3,2c8 <fail>
  58:	00301663          	bne	x0,x3,64 <test_5>
  5c:	fe209ee3          	bne	x1,x2,58 <test_4+0x14>
  60:	26301463          	bne	x0,x3,2c8 <fail>

00000064 <test_5>:
  64:	00500193          	addi	x3,x0,5
  68:	00100093          	addi	x1,x0,1
  6c:	fff00113          	addi	x2,x0,-1
  70:	00209663          	bne	x1,x2,7c <test_5+0x18>
  74:	24301a63          	bne	x0,x3,2c8 <fail>
  78:	00301663          	bne	x0,x3,84 <test_6>
  7c:	fe209ee3          	bne	x1,x2,78 <test_5+0x14>
  80:	24301463          	bne	x0,x3,2c8 <fail>

00000084 <test_6>:
  84:	00600193          	addi	x3,x0,6
  88:	00000093          	addi	x1,x0,0
  8c:	00000113          	addi	x2,x0,0
  90:	00209463          	bne	x1,x2,98 <test_6+0x14>
  94:	00301463          	bne	x0,x3,9c <test_6+0x18>
  98:	22301863          	bne	x0,x3,2c8 <fail>
  9c:	fe209ee3          	bne	x1,x2,98 <test_6+0x14>

000000a0 <test_7>:
  a0:	00700193          	addi	x3,x0,7
  a4:	00100093          	addi	x1,x0,1
  a8:	00100113          	addi	x2,x0,1
  ac:	00209463          	bne	x1,x2,b4 <test_7+0x14>
  b0:	00301463          	bne	x0,x3,b8 <test_7+0x18>
  b4:	20301a63          	bne	x0,x3,2c8 <fail>
  b8:	fe209ee3          	bne	x1,x2,b4 <test_7+0x14>

000000bc <test_8>:
  bc:	00800193          	addi	x3,x0,8
  c0:	fff00093          	addi	x1,x0,-1
  c4:	fff00113          	addi	x2,x0,-1
  c8:	00209463          	bne	x1,x2,d0 <test_8+0x14>
  cc:	00301463          	bne	x0,x3,d4 <test_8+0x18>
  d0:	1e301c63          	bne	x0,x3,2c8 <fail>
  d4:	fe209ee3          	bne	x1,x2,d0 <test_8+0x14>

000000d8 <test_9>:
  d8:	00900193          	addi	x3,x0,9
  dc:	00000213          	addi	x4,x0,0
  e0:	00000093          	addi	x1,x0,0
  e4:	00000113          	addi	x2,x0,0
  e8:	1e209063          	bne	x1,x2,2c8 <fail>
  ec:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
  f0:	00200293          	addi	x5,x0,2
  f4:	fe5216e3          	bne	x4,x5,e0 <test_9+0x8>

000000f8 <test_10>:
  f8:	00a00193          	addi	x3,x0,10
  fc:	00000213          	addi	x4,x0,0
 100:	00000093          	addi	x1,x0,0
 104:	00000113          	addi	x2,x0,0
 108:	00000013          	addi	x0,x0,0
 10c:	1a209e63          	bne	x1,x2,2c8 <fail>
 110:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 114:	00200293          	addi	x5,x0,2
 118:	fe5214e3          	bne	x4,x5,100 <test_10+0x8>

0000011c <test_11>:
 11c:	00b00193          	addi	x3,x0,11
 120:	00000213          	addi	x4,x0,0
 124:	00000093          	addi	x1,x0,0
 128:	00000113          	addi	x2,x0,0
 12c:	00000013          	addi	x0,x0,0
 130:	00000013          	addi	x0,x0,0
 134:	18209a63          	bne	x1,x2,2c8 <fail>
 138:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 13c:	00200293          	addi	x5,x0,2
 140:	fe5212e3          	bne	x4,x5,124 <test_11+0x8>

00000144 <test_12>:
 144:	00c00193          	addi	x3,x0,12
 148:	00000213          	addi	x4,x0,0
 14c:	00000093          	addi	x1,x0,0
 150:	00000013          	addi	x0,x0,0
 154:	00000113          	addi	x2,x0,0
 158:	16209863          	bne	x1,x2,2c8 <fail>
 15c:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 160:	00200293          	addi	x5,x0,2
 164:	fe5214e3          	bne	x4,x5,14c <test_12+0x8>

00000168 <test_13>:
 168:	00d00193          	addi	x3,x0,13
 16c:	00000213          	addi	x4,x0,0
 170:	00000093          	addi	x1,x0,0
 174:	00000013          	addi	x0,x0,0
 178:	00000113          	addi	x2,x0,0
 17c:	00000013          	addi	x0,x0,0
 180:	14209463          	bne	x1,x2,2c8 <fail>
 184:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 188:	00200293          	addi	x5,x0,2
 18c:	fe5212e3          	bne	x4,x5,170 <test_13+0x8>

00000190 <test_14>:
 190:	00e00193          	addi	x3,x0,14
 194:	00000213          	addi	x4,x0,0
 198:	00000093          	addi	x1,x0,0
 19c:	00000013          	addi	x0,x0,0
 1a0:	00000013          	addi	x0,x0,0
 1a4:	00000113          	addi	x2,x0,0
 1a8:	12209063          	bne	x1,x2,2c8 <fail>
 1ac:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 1b0:	00200293          	addi	x5,x0,2
 1b4:	fe5212e3          	bne	x4,x5,198 <test_14+0x8>

000001b8 <test_15>:
 1b8:	00f00193          	addi	x3,x0,15
 1bc:	00000213          	addi	x4,x0,0
 1c0:	00000093          	addi	x1,x0,0
 1c4:	00000113          	addi	x2,x0,0
 1c8:	10209063          	bne	x1,x2,2c8 <fail>
 1cc:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 1d0:	00200293          	addi	x5,x0,2
 1d4:	fe5216e3          	bne	x4,x5,1c0 <test_15+0x8>

000001d8 <test_16>:
 1d8:	01000193          	addi	x3,x0,16
 1dc:	00000213          	addi	x4,x0,0
 1e0:	00000093          	addi	x1,x0,0
 1e4:	00000113          	addi	x2,x0,0
 1e8:	00000013          	addi	x0,x0,0
 1ec:	0c209e63          	bne	x1,x2,2c8 <fail>
 1f0:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 1f4:	00200293          	addi	x5,x0,2
 1f8:	fe5214e3          	bne	x4,x5,1e0 <test_16+0x8>

000001fc <test_17>:
 1fc:	01100193          	addi	x3,x0,17
 200:	00000213          	addi	x4,x0,0
 204:	00000093          	addi	x1,x0,0
 208:	00000113          	addi	x2,x0,0
 20c:	00000013          	addi	x0,x0,0
 210:	00000013          	addi	x0,x0,0
 214:	0a209a63          	bne	x1,x2,2c8 <fail>
 218:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 21c:	00200293          	addi	x5,x0,2
 220:	fe5212e3          	bne	x4,x5,204 <test_17+0x8>

00000224 <test_18>:
 224:	01200193          	addi	x3,x0,18
 228:	00000213          	addi	x4,x0,0
 22c:	00000093          	addi	x1,x0,0
 230:	00000013          	addi	x0,x0,0
 234:	00000113          	addi	x2,x0,0
 238:	08209863          	bne	x1,x2,2c8 <fail>
 23c:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 240:	00200293          	addi	x5,x0,2
 244:	fe5214e3          	bne	x4,x5,22c <test_18+0x8>

00000248 <test_19>:
 248:	01300193          	addi	x3,x0,19
 24c:	00000213          	addi	x4,x0,0
 250:	00000093          	addi	x1,x0,0
 254:	00000013          	addi	x0,x0,0
 258:	00000113          	addi	x2,x0,0
 25c:	00000013          	addi	x0,x0,0
 260:	06209463          	bne	x1,x2,2c8 <fail>
 264:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 268:	00200293          	addi	x5,x0,2
 26c:	fe5212e3          	bne	x4,x5,250 <test_19+0x8>

00000270 <test_20>:
 270:	01400193          	addi	x3,x0,20
 274:	00000213          	addi	x4,x0,0
 278:	00000093          	addi	x1,x0,0
 27c:	00000013          	addi	x0,x0,0
 280:	00000013          	addi	x0,x0,0
 284:	00000113          	addi	x2,x0,0
 288:	04209063          	bne	x1,x2,2c8 <fail>
 28c:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 290:	00200293          	addi	x5,x0,2
 294:	fe5212e3          	bne	x4,x5,278 <test_20+0x8>

00000298 <test_21>:
 298:	00100093          	addi	x1,x0,1
 29c:	00009a63          	bne	x1,x0,2b0 <test_21+0x18>
 2a0:	00108093          	addi	x1,x1,1
 2a4:	00108093          	addi	x1,x1,1
 2a8:	00108093          	addi	x1,x1,1
 2ac:	00108093          	addi	x1,x1,1
 2b0:	00108093          	addi	x1,x1,1
 2b4:	00108093          	addi	x1,x1,1
 2b8:	00300393          	addi	x7,x0,3
 2bc:	01500193          	addi	x3,x0,21
 2c0:	00709463          	bne	x1,x7,2c8 <fail>
 2c4:	00301e63          	bne	x0,x3,2e0 <pass>

000002c8 <fail>:
 2c8:	00018063          	beq	x3,x0,2c8 <fail>
 2cc:	00119193          	slli	x3,x3,0x1
 2d0:	0011e193          	ori	x3,x3,1
 2d4:	05d00893          	addi	x17,x0,93
 2d8:	00018513          	addi	x10,x3,0
 2dc:	00000073          	ecall

000002e0 <pass>:
 2e0:	00100193          	addi	x3,x0,1
 2e4:	05d00893          	addi	x17,x0,93
 2e8:	00000513          	addi	x10,x0,0
 2ec:	00000073          	ecall
 2f0:	c0001073          	unimp
 2f4:	0000                	c.unimp
 2f6:	0000                	c.unimp
 2f8:	0000                	c.unimp
 2fa:	0000                	c.unimp
 2fc:	0000                	c.unimp
 2fe:	0000                	c.unimp
 300:	0000                	c.unimp
 302:	0000                	c.unimp
