`timescale 1ns / 1ps

`include "defines.vh"

module Dig (
    input  wire         rst,        
    input  wire         clk,        
    input  wire [31:0]  addr,       
    input  wire         we,         
    input  wire [31:0]  wdata,      

    // 数码管硬件接口
    output reg  [ 7:0]  dig_en,   
    output reg          DN_A0, DN_A1,  
    output reg          DN_B0, DN_B1, 
    output reg          DN_C0, DN_C1, 
    output reg          DN_D0, DN_D1,  
    output reg          DN_E0, DN_E1,  
    output reg          DN_F0, DN_F1, 
    output reg          DN_G0, DN_G1, 
    output reg          DN_DP0, DN_DP1
);

    // 数据寄存器，存储要显示的32位数据
    reg [31:0] display_data;

    // 扫描计数器，动态扫描数码管
    reg [2:0] scan_cnt;
    reg [15:0] scan_clk_cnt;

    // 当前扫描位的4位数据
    wire [3:0] current_digit = (scan_cnt == 3'd0) ? display_data[3:0]   :
                               (scan_cnt == 3'd1) ? display_data[7:4]   :
                               (scan_cnt == 3'd2) ? display_data[11:8]  :
                               (scan_cnt == 3'd3) ? display_data[15:12] :
                               (scan_cnt == 3'd4) ? display_data[19:16] :
                               (scan_cnt == 3'd5) ? display_data[23:20] :
                               (scan_cnt == 3'd6) ? display_data[27:24] :
                                                     display_data[31:28];

    // 7段译码器 - 将4位16进制数转换为7段显示码
    reg [6:0] seg_code;
    always @(*) begin
        case (current_digit)
            4'h0: seg_code = 8'h3f; 
            4'h1: seg_code = 8'h06; 
            4'h2: seg_code = 8'h5b; 
            4'h3: seg_code = 8'h4f; 
            4'h4: seg_code = 8'h66; 
            4'h5: seg_code = 8'h6d; 
            4'h6: seg_code = 8'h7d; 
            4'h7: seg_code = 8'h07; 
            4'h8: seg_code = 8'h7f; 
            4'h9: seg_code = 8'h6f; 
            4'hA: seg_code = 8'h77; 
            4'hB: seg_code = 8'h7c;  
            4'hC: seg_code = 8'h39; 
            4'hD: seg_code = 8'h5e; 
            4'hE: seg_code = 8'h79; 
            4'hF: seg_code = 8'h71;
            default: seg_code = 8'h00;  
        endcase
    end

    // 写数据寄存器
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            display_data <= 32'h0;
        end else if (we && addr == `PERI_ADDR_DIG) begin
            display_data <= wdata;
        end
    end

    // 扫描时钟分频 (1KHz扫描频率)
    always @(posedge clk or posedge rst) begin
        if (rst) begin
            scan_clk_cnt <= 16'h0;
            scan_cnt <= 3'h0;
        end else begin
            if (scan_clk_cnt >= 16'd25000) begin  // 25MHz/25000 = 1KHz
                scan_clk_cnt <= 16'h0;
                scan_cnt <= scan_cnt + 1'b1;
            end else begin
                scan_clk_cnt <= scan_clk_cnt + 1'b1;
            end
        end
    end

    // 位选信号生成
    always @(*) begin
        dig_en = 8'h00;
        dig_en[scan_cnt] = 1'b1;
    end

    // 段选信号输出
    always @(*) begin
        {DN_DP0, DN_G0, DN_F0, DN_E0, DN_D0, DN_C0, DN_B0, DN_A0} = seg_code;
        {DN_DP1, DN_G1, DN_F1, DN_E1, DN_D1, DN_C1, DN_B1, DN_A1} = seg_code;
    end

endmodule
