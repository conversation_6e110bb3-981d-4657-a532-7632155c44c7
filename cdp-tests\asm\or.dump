
or:     file format elf32-littleriscv


Disassembly of section .text.init:

00000000 <_start>:
   0:	0040006f          	jal	x0,4 <reset_vector>

00000004 <reset_vector>:
   4:	ff0100b7          	lui	x1,0xff010
   8:	f0008093          	addi	x1,x1,-256 # ff00ff00 <_end+0xff00df00>
   c:	0f0f1137          	lui	x2,0xf0f1
  10:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
  14:	0020e733          	or	x14,x1,x2
  18:	ff1003b7          	lui	x7,0xff100
  1c:	f0f38393          	addi	x7,x7,-241 # ff0fff0f <_end+0xff0fdf0f>
  20:	00200193          	addi	x3,x0,2
  24:	4a771263          	bne	x14,x7,4c8 <fail>

00000028 <test_3>:
  28:	0ff010b7          	lui	x1,0xff01
  2c:	ff008093          	addi	x1,x1,-16 # ff00ff0 <_end+0xfefeff0>
  30:	f0f0f137          	lui	x2,0xf0f0f
  34:	0f010113          	addi	x2,x2,240 # f0f0f0f0 <_end+0xf0f0d0f0>
  38:	0020e733          	or	x14,x1,x2
  3c:	fff103b7          	lui	x7,0xfff10
  40:	ff038393          	addi	x7,x7,-16 # fff0fff0 <_end+0xfff0dff0>
  44:	00300193          	addi	x3,x0,3
  48:	48771063          	bne	x14,x7,4c8 <fail>

0000004c <test_4>:
  4c:	00ff00b7          	lui	x1,0xff0
  50:	0ff08093          	addi	x1,x1,255 # ff00ff <_end+0xfee0ff>
  54:	0f0f1137          	lui	x2,0xf0f1
  58:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
  5c:	0020e733          	or	x14,x1,x2
  60:	0fff13b7          	lui	x7,0xfff1
  64:	fff38393          	addi	x7,x7,-1 # fff0fff <_end+0xffeefff>
  68:	00400193          	addi	x3,x0,4
  6c:	44771e63          	bne	x14,x7,4c8 <fail>

00000070 <test_5>:
  70:	f00ff0b7          	lui	x1,0xf00ff
  74:	00f08093          	addi	x1,x1,15 # f00ff00f <_end+0xf00fd00f>
  78:	f0f0f137          	lui	x2,0xf0f0f
  7c:	0f010113          	addi	x2,x2,240 # f0f0f0f0 <_end+0xf0f0d0f0>
  80:	0020e733          	or	x14,x1,x2
  84:	f0fff3b7          	lui	x7,0xf0fff
  88:	0ff38393          	addi	x7,x7,255 # f0fff0ff <_end+0xf0ffd0ff>
  8c:	00500193          	addi	x3,x0,5
  90:	42771c63          	bne	x14,x7,4c8 <fail>

00000094 <test_6>:
  94:	ff0100b7          	lui	x1,0xff010
  98:	f0008093          	addi	x1,x1,-256 # ff00ff00 <_end+0xff00df00>
  9c:	0f0f1137          	lui	x2,0xf0f1
  a0:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
  a4:	0020e0b3          	or	x1,x1,x2
  a8:	ff1003b7          	lui	x7,0xff100
  ac:	f0f38393          	addi	x7,x7,-241 # ff0fff0f <_end+0xff0fdf0f>
  b0:	00600193          	addi	x3,x0,6
  b4:	40709a63          	bne	x1,x7,4c8 <fail>

000000b8 <test_7>:
  b8:	ff0100b7          	lui	x1,0xff010
  bc:	f0008093          	addi	x1,x1,-256 # ff00ff00 <_end+0xff00df00>
  c0:	0f0f1137          	lui	x2,0xf0f1
  c4:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
  c8:	0020e133          	or	x2,x1,x2
  cc:	ff1003b7          	lui	x7,0xff100
  d0:	f0f38393          	addi	x7,x7,-241 # ff0fff0f <_end+0xff0fdf0f>
  d4:	00700193          	addi	x3,x0,7
  d8:	3e711863          	bne	x2,x7,4c8 <fail>

000000dc <test_8>:
  dc:	ff0100b7          	lui	x1,0xff010
  e0:	f0008093          	addi	x1,x1,-256 # ff00ff00 <_end+0xff00df00>
  e4:	0010e0b3          	or	x1,x1,x1
  e8:	ff0103b7          	lui	x7,0xff010
  ec:	f0038393          	addi	x7,x7,-256 # ff00ff00 <_end+0xff00df00>
  f0:	00800193          	addi	x3,x0,8
  f4:	3c709a63          	bne	x1,x7,4c8 <fail>

000000f8 <test_9>:
  f8:	00000213          	addi	x4,x0,0
  fc:	ff0100b7          	lui	x1,0xff010
 100:	f0008093          	addi	x1,x1,-256 # ff00ff00 <_end+0xff00df00>
 104:	0f0f1137          	lui	x2,0xf0f1
 108:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
 10c:	0020e733          	or	x14,x1,x2
 110:	00070313          	addi	x6,x14,0
 114:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 118:	00200293          	addi	x5,x0,2
 11c:	fe5210e3          	bne	x4,x5,fc <test_9+0x4>
 120:	ff1003b7          	lui	x7,0xff100
 124:	f0f38393          	addi	x7,x7,-241 # ff0fff0f <_end+0xff0fdf0f>
 128:	00900193          	addi	x3,x0,9
 12c:	38731e63          	bne	x6,x7,4c8 <fail>

00000130 <test_10>:
 130:	00000213          	addi	x4,x0,0
 134:	0ff010b7          	lui	x1,0xff01
 138:	ff008093          	addi	x1,x1,-16 # ff00ff0 <_end+0xfefeff0>
 13c:	f0f0f137          	lui	x2,0xf0f0f
 140:	0f010113          	addi	x2,x2,240 # f0f0f0f0 <_end+0xf0f0d0f0>
 144:	0020e733          	or	x14,x1,x2
 148:	00000013          	addi	x0,x0,0
 14c:	00070313          	addi	x6,x14,0
 150:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 154:	00200293          	addi	x5,x0,2
 158:	fc521ee3          	bne	x4,x5,134 <test_10+0x4>
 15c:	fff103b7          	lui	x7,0xfff10
 160:	ff038393          	addi	x7,x7,-16 # fff0fff0 <_end+0xfff0dff0>
 164:	00a00193          	addi	x3,x0,10
 168:	36731063          	bne	x6,x7,4c8 <fail>

0000016c <test_11>:
 16c:	00000213          	addi	x4,x0,0
 170:	00ff00b7          	lui	x1,0xff0
 174:	0ff08093          	addi	x1,x1,255 # ff00ff <_end+0xfee0ff>
 178:	0f0f1137          	lui	x2,0xf0f1
 17c:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
 180:	0020e733          	or	x14,x1,x2
 184:	00000013          	addi	x0,x0,0
 188:	00000013          	addi	x0,x0,0
 18c:	00070313          	addi	x6,x14,0
 190:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 194:	00200293          	addi	x5,x0,2
 198:	fc521ce3          	bne	x4,x5,170 <test_11+0x4>
 19c:	0fff13b7          	lui	x7,0xfff1
 1a0:	fff38393          	addi	x7,x7,-1 # fff0fff <_end+0xffeefff>
 1a4:	00b00193          	addi	x3,x0,11
 1a8:	32731063          	bne	x6,x7,4c8 <fail>

000001ac <test_12>:
 1ac:	00000213          	addi	x4,x0,0
 1b0:	ff0100b7          	lui	x1,0xff010
 1b4:	f0008093          	addi	x1,x1,-256 # ff00ff00 <_end+0xff00df00>
 1b8:	0f0f1137          	lui	x2,0xf0f1
 1bc:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
 1c0:	0020e733          	or	x14,x1,x2
 1c4:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 1c8:	00200293          	addi	x5,x0,2
 1cc:	fe5212e3          	bne	x4,x5,1b0 <test_12+0x4>
 1d0:	ff1003b7          	lui	x7,0xff100
 1d4:	f0f38393          	addi	x7,x7,-241 # ff0fff0f <_end+0xff0fdf0f>
 1d8:	00c00193          	addi	x3,x0,12
 1dc:	2e771663          	bne	x14,x7,4c8 <fail>

000001e0 <test_13>:
 1e0:	00000213          	addi	x4,x0,0
 1e4:	0ff010b7          	lui	x1,0xff01
 1e8:	ff008093          	addi	x1,x1,-16 # ff00ff0 <_end+0xfefeff0>
 1ec:	f0f0f137          	lui	x2,0xf0f0f
 1f0:	0f010113          	addi	x2,x2,240 # f0f0f0f0 <_end+0xf0f0d0f0>
 1f4:	00000013          	addi	x0,x0,0
 1f8:	0020e733          	or	x14,x1,x2
 1fc:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 200:	00200293          	addi	x5,x0,2
 204:	fe5210e3          	bne	x4,x5,1e4 <test_13+0x4>
 208:	fff103b7          	lui	x7,0xfff10
 20c:	ff038393          	addi	x7,x7,-16 # fff0fff0 <_end+0xfff0dff0>
 210:	00d00193          	addi	x3,x0,13
 214:	2a771a63          	bne	x14,x7,4c8 <fail>

00000218 <test_14>:
 218:	00000213          	addi	x4,x0,0
 21c:	00ff00b7          	lui	x1,0xff0
 220:	0ff08093          	addi	x1,x1,255 # ff00ff <_end+0xfee0ff>
 224:	0f0f1137          	lui	x2,0xf0f1
 228:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
 22c:	00000013          	addi	x0,x0,0
 230:	00000013          	addi	x0,x0,0
 234:	0020e733          	or	x14,x1,x2
 238:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 23c:	00200293          	addi	x5,x0,2
 240:	fc521ee3          	bne	x4,x5,21c <test_14+0x4>
 244:	0fff13b7          	lui	x7,0xfff1
 248:	fff38393          	addi	x7,x7,-1 # fff0fff <_end+0xffeefff>
 24c:	00e00193          	addi	x3,x0,14
 250:	26771c63          	bne	x14,x7,4c8 <fail>

00000254 <test_15>:
 254:	00000213          	addi	x4,x0,0
 258:	ff0100b7          	lui	x1,0xff010
 25c:	f0008093          	addi	x1,x1,-256 # ff00ff00 <_end+0xff00df00>
 260:	00000013          	addi	x0,x0,0
 264:	0f0f1137          	lui	x2,0xf0f1
 268:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
 26c:	0020e733          	or	x14,x1,x2
 270:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 274:	00200293          	addi	x5,x0,2
 278:	fe5210e3          	bne	x4,x5,258 <test_15+0x4>
 27c:	ff1003b7          	lui	x7,0xff100
 280:	f0f38393          	addi	x7,x7,-241 # ff0fff0f <_end+0xff0fdf0f>
 284:	00f00193          	addi	x3,x0,15
 288:	24771063          	bne	x14,x7,4c8 <fail>

0000028c <test_16>:
 28c:	00000213          	addi	x4,x0,0
 290:	0ff010b7          	lui	x1,0xff01
 294:	ff008093          	addi	x1,x1,-16 # ff00ff0 <_end+0xfefeff0>
 298:	00000013          	addi	x0,x0,0
 29c:	f0f0f137          	lui	x2,0xf0f0f
 2a0:	0f010113          	addi	x2,x2,240 # f0f0f0f0 <_end+0xf0f0d0f0>
 2a4:	00000013          	addi	x0,x0,0
 2a8:	0020e733          	or	x14,x1,x2
 2ac:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 2b0:	00200293          	addi	x5,x0,2
 2b4:	fc521ee3          	bne	x4,x5,290 <test_16+0x4>
 2b8:	fff103b7          	lui	x7,0xfff10
 2bc:	ff038393          	addi	x7,x7,-16 # fff0fff0 <_end+0xfff0dff0>
 2c0:	01000193          	addi	x3,x0,16
 2c4:	20771263          	bne	x14,x7,4c8 <fail>

000002c8 <test_17>:
 2c8:	00000213          	addi	x4,x0,0
 2cc:	00ff00b7          	lui	x1,0xff0
 2d0:	0ff08093          	addi	x1,x1,255 # ff00ff <_end+0xfee0ff>
 2d4:	00000013          	addi	x0,x0,0
 2d8:	00000013          	addi	x0,x0,0
 2dc:	0f0f1137          	lui	x2,0xf0f1
 2e0:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
 2e4:	0020e733          	or	x14,x1,x2
 2e8:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 2ec:	00200293          	addi	x5,x0,2
 2f0:	fc521ee3          	bne	x4,x5,2cc <test_17+0x4>
 2f4:	0fff13b7          	lui	x7,0xfff1
 2f8:	fff38393          	addi	x7,x7,-1 # fff0fff <_end+0xffeefff>
 2fc:	01100193          	addi	x3,x0,17
 300:	1c771463          	bne	x14,x7,4c8 <fail>

00000304 <test_18>:
 304:	00000213          	addi	x4,x0,0
 308:	0f0f1137          	lui	x2,0xf0f1
 30c:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
 310:	ff0100b7          	lui	x1,0xff010
 314:	f0008093          	addi	x1,x1,-256 # ff00ff00 <_end+0xff00df00>
 318:	0020e733          	or	x14,x1,x2
 31c:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 320:	00200293          	addi	x5,x0,2
 324:	fe5212e3          	bne	x4,x5,308 <test_18+0x4>
 328:	ff1003b7          	lui	x7,0xff100
 32c:	f0f38393          	addi	x7,x7,-241 # ff0fff0f <_end+0xff0fdf0f>
 330:	01200193          	addi	x3,x0,18
 334:	18771a63          	bne	x14,x7,4c8 <fail>

00000338 <test_19>:
 338:	00000213          	addi	x4,x0,0
 33c:	f0f0f137          	lui	x2,0xf0f0f
 340:	0f010113          	addi	x2,x2,240 # f0f0f0f0 <_end+0xf0f0d0f0>
 344:	0ff010b7          	lui	x1,0xff01
 348:	ff008093          	addi	x1,x1,-16 # ff00ff0 <_end+0xfefeff0>
 34c:	00000013          	addi	x0,x0,0
 350:	0020e733          	or	x14,x1,x2
 354:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 358:	00200293          	addi	x5,x0,2
 35c:	fe5210e3          	bne	x4,x5,33c <test_19+0x4>
 360:	fff103b7          	lui	x7,0xfff10
 364:	ff038393          	addi	x7,x7,-16 # fff0fff0 <_end+0xfff0dff0>
 368:	01300193          	addi	x3,x0,19
 36c:	14771e63          	bne	x14,x7,4c8 <fail>

00000370 <test_20>:
 370:	00000213          	addi	x4,x0,0
 374:	0f0f1137          	lui	x2,0xf0f1
 378:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
 37c:	00ff00b7          	lui	x1,0xff0
 380:	0ff08093          	addi	x1,x1,255 # ff00ff <_end+0xfee0ff>
 384:	00000013          	addi	x0,x0,0
 388:	00000013          	addi	x0,x0,0
 38c:	0020e733          	or	x14,x1,x2
 390:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 394:	00200293          	addi	x5,x0,2
 398:	fc521ee3          	bne	x4,x5,374 <test_20+0x4>
 39c:	0fff13b7          	lui	x7,0xfff1
 3a0:	fff38393          	addi	x7,x7,-1 # fff0fff <_end+0xffeefff>
 3a4:	01400193          	addi	x3,x0,20
 3a8:	12771063          	bne	x14,x7,4c8 <fail>

000003ac <test_21>:
 3ac:	00000213          	addi	x4,x0,0
 3b0:	0f0f1137          	lui	x2,0xf0f1
 3b4:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
 3b8:	00000013          	addi	x0,x0,0
 3bc:	ff0100b7          	lui	x1,0xff010
 3c0:	f0008093          	addi	x1,x1,-256 # ff00ff00 <_end+0xff00df00>
 3c4:	0020e733          	or	x14,x1,x2
 3c8:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 3cc:	00200293          	addi	x5,x0,2
 3d0:	fe5210e3          	bne	x4,x5,3b0 <test_21+0x4>
 3d4:	ff1003b7          	lui	x7,0xff100
 3d8:	f0f38393          	addi	x7,x7,-241 # ff0fff0f <_end+0xff0fdf0f>
 3dc:	01500193          	addi	x3,x0,21
 3e0:	0e771463          	bne	x14,x7,4c8 <fail>

000003e4 <test_22>:
 3e4:	00000213          	addi	x4,x0,0
 3e8:	f0f0f137          	lui	x2,0xf0f0f
 3ec:	0f010113          	addi	x2,x2,240 # f0f0f0f0 <_end+0xf0f0d0f0>
 3f0:	00000013          	addi	x0,x0,0
 3f4:	0ff010b7          	lui	x1,0xff01
 3f8:	ff008093          	addi	x1,x1,-16 # ff00ff0 <_end+0xfefeff0>
 3fc:	00000013          	addi	x0,x0,0
 400:	0020e733          	or	x14,x1,x2
 404:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 408:	00200293          	addi	x5,x0,2
 40c:	fc521ee3          	bne	x4,x5,3e8 <test_22+0x4>
 410:	fff103b7          	lui	x7,0xfff10
 414:	ff038393          	addi	x7,x7,-16 # fff0fff0 <_end+0xfff0dff0>
 418:	01600193          	addi	x3,x0,22
 41c:	0a771663          	bne	x14,x7,4c8 <fail>

00000420 <test_23>:
 420:	00000213          	addi	x4,x0,0
 424:	0f0f1137          	lui	x2,0xf0f1
 428:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
 42c:	00000013          	addi	x0,x0,0
 430:	00000013          	addi	x0,x0,0
 434:	00ff00b7          	lui	x1,0xff0
 438:	0ff08093          	addi	x1,x1,255 # ff00ff <_end+0xfee0ff>
 43c:	0020e733          	or	x14,x1,x2
 440:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 444:	00200293          	addi	x5,x0,2
 448:	fc521ee3          	bne	x4,x5,424 <test_23+0x4>
 44c:	0fff13b7          	lui	x7,0xfff1
 450:	fff38393          	addi	x7,x7,-1 # fff0fff <_end+0xffeefff>
 454:	01700193          	addi	x3,x0,23
 458:	06771863          	bne	x14,x7,4c8 <fail>

0000045c <test_24>:
 45c:	ff0100b7          	lui	x1,0xff010
 460:	f0008093          	addi	x1,x1,-256 # ff00ff00 <_end+0xff00df00>
 464:	00106133          	or	x2,x0,x1
 468:	ff0103b7          	lui	x7,0xff010
 46c:	f0038393          	addi	x7,x7,-256 # ff00ff00 <_end+0xff00df00>
 470:	01800193          	addi	x3,x0,24
 474:	04711a63          	bne	x2,x7,4c8 <fail>

00000478 <test_25>:
 478:	00ff00b7          	lui	x1,0xff0
 47c:	0ff08093          	addi	x1,x1,255 # ff00ff <_end+0xfee0ff>
 480:	0000e133          	or	x2,x1,x0
 484:	00ff03b7          	lui	x7,0xff0
 488:	0ff38393          	addi	x7,x7,255 # ff00ff <_end+0xfee0ff>
 48c:	01900193          	addi	x3,x0,25
 490:	02711c63          	bne	x2,x7,4c8 <fail>

00000494 <test_26>:
 494:	000060b3          	or	x1,x0,x0
 498:	00000393          	addi	x7,x0,0
 49c:	01a00193          	addi	x3,x0,26
 4a0:	02709463          	bne	x1,x7,4c8 <fail>

000004a4 <test_27>:
 4a4:	111110b7          	lui	x1,0x11111
 4a8:	11108093          	addi	x1,x1,273 # 11111111 <_end+0x1110f111>
 4ac:	22222137          	lui	x2,0x22222
 4b0:	22210113          	addi	x2,x2,546 # 22222222 <_end+0x22220222>
 4b4:	0020e033          	or	x0,x1,x2
 4b8:	00000393          	addi	x7,x0,0
 4bc:	01b00193          	addi	x3,x0,27
 4c0:	00701463          	bne	x0,x7,4c8 <fail>
 4c4:	00301e63          	bne	x0,x3,4e0 <pass>

000004c8 <fail>:
 4c8:	00018063          	beq	x3,x0,4c8 <fail>
 4cc:	00119193          	slli	x3,x3,0x1
 4d0:	0011e193          	ori	x3,x3,1
 4d4:	05d00893          	addi	x17,x0,93
 4d8:	00018513          	addi	x10,x3,0
 4dc:	00000073          	ecall

000004e0 <pass>:
 4e0:	00100193          	addi	x3,x0,1
 4e4:	05d00893          	addi	x17,x0,93
 4e8:	00000513          	addi	x10,x0,0
 4ec:	00000073          	ecall
 4f0:	c0001073          	unimp
 4f4:	0000                	c.unimp
 4f6:	0000                	c.unimp
 4f8:	0000                	c.unimp
 4fa:	0000                	c.unimp
 4fc:	0000                	c.unimp
 4fe:	0000                	c.unimp
 500:	0000                	c.unimp
 502:	0000                	c.unimp
