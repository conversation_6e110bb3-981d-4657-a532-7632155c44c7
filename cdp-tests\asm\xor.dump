
xor:     file format elf32-littleriscv


Disassembly of section .text.init:

00000000 <_start>:
   0:	0040006f          	jal	x0,4 <reset_vector>

00000004 <reset_vector>:
   4:	ff0100b7          	lui	x1,0xff010
   8:	f0008093          	addi	x1,x1,-256 # ff00ff00 <_end+0xff00df00>
   c:	0f0f1137          	lui	x2,0xf0f1
  10:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
  14:	0020c733          	xor	x14,x1,x2
  18:	f00ff3b7          	lui	x7,0xf00ff
  1c:	00f38393          	addi	x7,x7,15 # f00ff00f <_end+0xf00fd00f>
  20:	00200193          	addi	x3,x0,2
  24:	4a771063          	bne	x14,x7,4c4 <fail>

00000028 <test_3>:
  28:	0ff010b7          	lui	x1,0xff01
  2c:	ff008093          	addi	x1,x1,-16 # ff00ff0 <_end+0xfefeff0>
  30:	f0f0f137          	lui	x2,0xf0f0f
  34:	0f010113          	addi	x2,x2,240 # f0f0f0f0 <_end+0xf0f0d0f0>
  38:	0020c733          	xor	x14,x1,x2
  3c:	ff0103b7          	lui	x7,0xff010
  40:	f0038393          	addi	x7,x7,-256 # ff00ff00 <_end+0xff00df00>
  44:	00300193          	addi	x3,x0,3
  48:	46771e63          	bne	x14,x7,4c4 <fail>

0000004c <test_4>:
  4c:	00ff00b7          	lui	x1,0xff0
  50:	0ff08093          	addi	x1,x1,255 # ff00ff <_end+0xfee0ff>
  54:	0f0f1137          	lui	x2,0xf0f1
  58:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
  5c:	0020c733          	xor	x14,x1,x2
  60:	0ff013b7          	lui	x7,0xff01
  64:	ff038393          	addi	x7,x7,-16 # ff00ff0 <_end+0xfefeff0>
  68:	00400193          	addi	x3,x0,4
  6c:	44771c63          	bne	x14,x7,4c4 <fail>

00000070 <test_5>:
  70:	f00ff0b7          	lui	x1,0xf00ff
  74:	00f08093          	addi	x1,x1,15 # f00ff00f <_end+0xf00fd00f>
  78:	f0f0f137          	lui	x2,0xf0f0f
  7c:	0f010113          	addi	x2,x2,240 # f0f0f0f0 <_end+0xf0f0d0f0>
  80:	0020c733          	xor	x14,x1,x2
  84:	00ff03b7          	lui	x7,0xff0
  88:	0ff38393          	addi	x7,x7,255 # ff00ff <_end+0xfee0ff>
  8c:	00500193          	addi	x3,x0,5
  90:	42771a63          	bne	x14,x7,4c4 <fail>

00000094 <test_6>:
  94:	ff0100b7          	lui	x1,0xff010
  98:	f0008093          	addi	x1,x1,-256 # ff00ff00 <_end+0xff00df00>
  9c:	0f0f1137          	lui	x2,0xf0f1
  a0:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
  a4:	0020c0b3          	xor	x1,x1,x2
  a8:	f00ff3b7          	lui	x7,0xf00ff
  ac:	00f38393          	addi	x7,x7,15 # f00ff00f <_end+0xf00fd00f>
  b0:	00600193          	addi	x3,x0,6
  b4:	40709863          	bne	x1,x7,4c4 <fail>

000000b8 <test_7>:
  b8:	ff0100b7          	lui	x1,0xff010
  bc:	f0008093          	addi	x1,x1,-256 # ff00ff00 <_end+0xff00df00>
  c0:	0f0f1137          	lui	x2,0xf0f1
  c4:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
  c8:	0020c133          	xor	x2,x1,x2
  cc:	f00ff3b7          	lui	x7,0xf00ff
  d0:	00f38393          	addi	x7,x7,15 # f00ff00f <_end+0xf00fd00f>
  d4:	00700193          	addi	x3,x0,7
  d8:	3e711663          	bne	x2,x7,4c4 <fail>

000000dc <test_8>:
  dc:	ff0100b7          	lui	x1,0xff010
  e0:	f0008093          	addi	x1,x1,-256 # ff00ff00 <_end+0xff00df00>
  e4:	0010c0b3          	xor	x1,x1,x1
  e8:	00000393          	addi	x7,x0,0
  ec:	00800193          	addi	x3,x0,8
  f0:	3c709a63          	bne	x1,x7,4c4 <fail>

000000f4 <test_9>:
  f4:	00000213          	addi	x4,x0,0
  f8:	ff0100b7          	lui	x1,0xff010
  fc:	f0008093          	addi	x1,x1,-256 # ff00ff00 <_end+0xff00df00>
 100:	0f0f1137          	lui	x2,0xf0f1
 104:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
 108:	0020c733          	xor	x14,x1,x2
 10c:	00070313          	addi	x6,x14,0
 110:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 114:	00200293          	addi	x5,x0,2
 118:	fe5210e3          	bne	x4,x5,f8 <test_9+0x4>
 11c:	f00ff3b7          	lui	x7,0xf00ff
 120:	00f38393          	addi	x7,x7,15 # f00ff00f <_end+0xf00fd00f>
 124:	00900193          	addi	x3,x0,9
 128:	38731e63          	bne	x6,x7,4c4 <fail>

0000012c <test_10>:
 12c:	00000213          	addi	x4,x0,0
 130:	0ff010b7          	lui	x1,0xff01
 134:	ff008093          	addi	x1,x1,-16 # ff00ff0 <_end+0xfefeff0>
 138:	f0f0f137          	lui	x2,0xf0f0f
 13c:	0f010113          	addi	x2,x2,240 # f0f0f0f0 <_end+0xf0f0d0f0>
 140:	0020c733          	xor	x14,x1,x2
 144:	00000013          	addi	x0,x0,0
 148:	00070313          	addi	x6,x14,0
 14c:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 150:	00200293          	addi	x5,x0,2
 154:	fc521ee3          	bne	x4,x5,130 <test_10+0x4>
 158:	ff0103b7          	lui	x7,0xff010
 15c:	f0038393          	addi	x7,x7,-256 # ff00ff00 <_end+0xff00df00>
 160:	00a00193          	addi	x3,x0,10
 164:	36731063          	bne	x6,x7,4c4 <fail>

00000168 <test_11>:
 168:	00000213          	addi	x4,x0,0
 16c:	00ff00b7          	lui	x1,0xff0
 170:	0ff08093          	addi	x1,x1,255 # ff00ff <_end+0xfee0ff>
 174:	0f0f1137          	lui	x2,0xf0f1
 178:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
 17c:	0020c733          	xor	x14,x1,x2
 180:	00000013          	addi	x0,x0,0
 184:	00000013          	addi	x0,x0,0
 188:	00070313          	addi	x6,x14,0
 18c:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 190:	00200293          	addi	x5,x0,2
 194:	fc521ce3          	bne	x4,x5,16c <test_11+0x4>
 198:	0ff013b7          	lui	x7,0xff01
 19c:	ff038393          	addi	x7,x7,-16 # ff00ff0 <_end+0xfefeff0>
 1a0:	00b00193          	addi	x3,x0,11
 1a4:	32731063          	bne	x6,x7,4c4 <fail>

000001a8 <test_12>:
 1a8:	00000213          	addi	x4,x0,0
 1ac:	ff0100b7          	lui	x1,0xff010
 1b0:	f0008093          	addi	x1,x1,-256 # ff00ff00 <_end+0xff00df00>
 1b4:	0f0f1137          	lui	x2,0xf0f1
 1b8:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
 1bc:	0020c733          	xor	x14,x1,x2
 1c0:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 1c4:	00200293          	addi	x5,x0,2
 1c8:	fe5212e3          	bne	x4,x5,1ac <test_12+0x4>
 1cc:	f00ff3b7          	lui	x7,0xf00ff
 1d0:	00f38393          	addi	x7,x7,15 # f00ff00f <_end+0xf00fd00f>
 1d4:	00c00193          	addi	x3,x0,12
 1d8:	2e771663          	bne	x14,x7,4c4 <fail>

000001dc <test_13>:
 1dc:	00000213          	addi	x4,x0,0
 1e0:	0ff010b7          	lui	x1,0xff01
 1e4:	ff008093          	addi	x1,x1,-16 # ff00ff0 <_end+0xfefeff0>
 1e8:	f0f0f137          	lui	x2,0xf0f0f
 1ec:	0f010113          	addi	x2,x2,240 # f0f0f0f0 <_end+0xf0f0d0f0>
 1f0:	00000013          	addi	x0,x0,0
 1f4:	0020c733          	xor	x14,x1,x2
 1f8:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 1fc:	00200293          	addi	x5,x0,2
 200:	fe5210e3          	bne	x4,x5,1e0 <test_13+0x4>
 204:	ff0103b7          	lui	x7,0xff010
 208:	f0038393          	addi	x7,x7,-256 # ff00ff00 <_end+0xff00df00>
 20c:	00d00193          	addi	x3,x0,13
 210:	2a771a63          	bne	x14,x7,4c4 <fail>

00000214 <test_14>:
 214:	00000213          	addi	x4,x0,0
 218:	00ff00b7          	lui	x1,0xff0
 21c:	0ff08093          	addi	x1,x1,255 # ff00ff <_end+0xfee0ff>
 220:	0f0f1137          	lui	x2,0xf0f1
 224:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
 228:	00000013          	addi	x0,x0,0
 22c:	00000013          	addi	x0,x0,0
 230:	0020c733          	xor	x14,x1,x2
 234:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 238:	00200293          	addi	x5,x0,2
 23c:	fc521ee3          	bne	x4,x5,218 <test_14+0x4>
 240:	0ff013b7          	lui	x7,0xff01
 244:	ff038393          	addi	x7,x7,-16 # ff00ff0 <_end+0xfefeff0>
 248:	00e00193          	addi	x3,x0,14
 24c:	26771c63          	bne	x14,x7,4c4 <fail>

00000250 <test_15>:
 250:	00000213          	addi	x4,x0,0
 254:	ff0100b7          	lui	x1,0xff010
 258:	f0008093          	addi	x1,x1,-256 # ff00ff00 <_end+0xff00df00>
 25c:	00000013          	addi	x0,x0,0
 260:	0f0f1137          	lui	x2,0xf0f1
 264:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
 268:	0020c733          	xor	x14,x1,x2
 26c:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 270:	00200293          	addi	x5,x0,2
 274:	fe5210e3          	bne	x4,x5,254 <test_15+0x4>
 278:	f00ff3b7          	lui	x7,0xf00ff
 27c:	00f38393          	addi	x7,x7,15 # f00ff00f <_end+0xf00fd00f>
 280:	00f00193          	addi	x3,x0,15
 284:	24771063          	bne	x14,x7,4c4 <fail>

00000288 <test_16>:
 288:	00000213          	addi	x4,x0,0
 28c:	0ff010b7          	lui	x1,0xff01
 290:	ff008093          	addi	x1,x1,-16 # ff00ff0 <_end+0xfefeff0>
 294:	00000013          	addi	x0,x0,0
 298:	f0f0f137          	lui	x2,0xf0f0f
 29c:	0f010113          	addi	x2,x2,240 # f0f0f0f0 <_end+0xf0f0d0f0>
 2a0:	00000013          	addi	x0,x0,0
 2a4:	0020c733          	xor	x14,x1,x2
 2a8:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 2ac:	00200293          	addi	x5,x0,2
 2b0:	fc521ee3          	bne	x4,x5,28c <test_16+0x4>
 2b4:	ff0103b7          	lui	x7,0xff010
 2b8:	f0038393          	addi	x7,x7,-256 # ff00ff00 <_end+0xff00df00>
 2bc:	01000193          	addi	x3,x0,16
 2c0:	20771263          	bne	x14,x7,4c4 <fail>

000002c4 <test_17>:
 2c4:	00000213          	addi	x4,x0,0
 2c8:	00ff00b7          	lui	x1,0xff0
 2cc:	0ff08093          	addi	x1,x1,255 # ff00ff <_end+0xfee0ff>
 2d0:	00000013          	addi	x0,x0,0
 2d4:	00000013          	addi	x0,x0,0
 2d8:	0f0f1137          	lui	x2,0xf0f1
 2dc:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
 2e0:	0020c733          	xor	x14,x1,x2
 2e4:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 2e8:	00200293          	addi	x5,x0,2
 2ec:	fc521ee3          	bne	x4,x5,2c8 <test_17+0x4>
 2f0:	0ff013b7          	lui	x7,0xff01
 2f4:	ff038393          	addi	x7,x7,-16 # ff00ff0 <_end+0xfefeff0>
 2f8:	01100193          	addi	x3,x0,17
 2fc:	1c771463          	bne	x14,x7,4c4 <fail>

00000300 <test_18>:
 300:	00000213          	addi	x4,x0,0
 304:	0f0f1137          	lui	x2,0xf0f1
 308:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
 30c:	ff0100b7          	lui	x1,0xff010
 310:	f0008093          	addi	x1,x1,-256 # ff00ff00 <_end+0xff00df00>
 314:	0020c733          	xor	x14,x1,x2
 318:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 31c:	00200293          	addi	x5,x0,2
 320:	fe5212e3          	bne	x4,x5,304 <test_18+0x4>
 324:	f00ff3b7          	lui	x7,0xf00ff
 328:	00f38393          	addi	x7,x7,15 # f00ff00f <_end+0xf00fd00f>
 32c:	01200193          	addi	x3,x0,18
 330:	18771a63          	bne	x14,x7,4c4 <fail>

00000334 <test_19>:
 334:	00000213          	addi	x4,x0,0
 338:	f0f0f137          	lui	x2,0xf0f0f
 33c:	0f010113          	addi	x2,x2,240 # f0f0f0f0 <_end+0xf0f0d0f0>
 340:	0ff010b7          	lui	x1,0xff01
 344:	ff008093          	addi	x1,x1,-16 # ff00ff0 <_end+0xfefeff0>
 348:	00000013          	addi	x0,x0,0
 34c:	0020c733          	xor	x14,x1,x2
 350:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 354:	00200293          	addi	x5,x0,2
 358:	fe5210e3          	bne	x4,x5,338 <test_19+0x4>
 35c:	ff0103b7          	lui	x7,0xff010
 360:	f0038393          	addi	x7,x7,-256 # ff00ff00 <_end+0xff00df00>
 364:	01300193          	addi	x3,x0,19
 368:	14771e63          	bne	x14,x7,4c4 <fail>

0000036c <test_20>:
 36c:	00000213          	addi	x4,x0,0
 370:	0f0f1137          	lui	x2,0xf0f1
 374:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
 378:	00ff00b7          	lui	x1,0xff0
 37c:	0ff08093          	addi	x1,x1,255 # ff00ff <_end+0xfee0ff>
 380:	00000013          	addi	x0,x0,0
 384:	00000013          	addi	x0,x0,0
 388:	0020c733          	xor	x14,x1,x2
 38c:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 390:	00200293          	addi	x5,x0,2
 394:	fc521ee3          	bne	x4,x5,370 <test_20+0x4>
 398:	0ff013b7          	lui	x7,0xff01
 39c:	ff038393          	addi	x7,x7,-16 # ff00ff0 <_end+0xfefeff0>
 3a0:	01400193          	addi	x3,x0,20
 3a4:	12771063          	bne	x14,x7,4c4 <fail>

000003a8 <test_21>:
 3a8:	00000213          	addi	x4,x0,0
 3ac:	0f0f1137          	lui	x2,0xf0f1
 3b0:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
 3b4:	00000013          	addi	x0,x0,0
 3b8:	ff0100b7          	lui	x1,0xff010
 3bc:	f0008093          	addi	x1,x1,-256 # ff00ff00 <_end+0xff00df00>
 3c0:	0020c733          	xor	x14,x1,x2
 3c4:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 3c8:	00200293          	addi	x5,x0,2
 3cc:	fe5210e3          	bne	x4,x5,3ac <test_21+0x4>
 3d0:	f00ff3b7          	lui	x7,0xf00ff
 3d4:	00f38393          	addi	x7,x7,15 # f00ff00f <_end+0xf00fd00f>
 3d8:	01500193          	addi	x3,x0,21
 3dc:	0e771463          	bne	x14,x7,4c4 <fail>

000003e0 <test_22>:
 3e0:	00000213          	addi	x4,x0,0
 3e4:	f0f0f137          	lui	x2,0xf0f0f
 3e8:	0f010113          	addi	x2,x2,240 # f0f0f0f0 <_end+0xf0f0d0f0>
 3ec:	00000013          	addi	x0,x0,0
 3f0:	0ff010b7          	lui	x1,0xff01
 3f4:	ff008093          	addi	x1,x1,-16 # ff00ff0 <_end+0xfefeff0>
 3f8:	00000013          	addi	x0,x0,0
 3fc:	0020c733          	xor	x14,x1,x2
 400:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 404:	00200293          	addi	x5,x0,2
 408:	fc521ee3          	bne	x4,x5,3e4 <test_22+0x4>
 40c:	ff0103b7          	lui	x7,0xff010
 410:	f0038393          	addi	x7,x7,-256 # ff00ff00 <_end+0xff00df00>
 414:	01600193          	addi	x3,x0,22
 418:	0a771663          	bne	x14,x7,4c4 <fail>

0000041c <test_23>:
 41c:	00000213          	addi	x4,x0,0
 420:	0f0f1137          	lui	x2,0xf0f1
 424:	f0f10113          	addi	x2,x2,-241 # f0f0f0f <_end+0xf0eef0f>
 428:	00000013          	addi	x0,x0,0
 42c:	00000013          	addi	x0,x0,0
 430:	00ff00b7          	lui	x1,0xff0
 434:	0ff08093          	addi	x1,x1,255 # ff00ff <_end+0xfee0ff>
 438:	0020c733          	xor	x14,x1,x2
 43c:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 440:	00200293          	addi	x5,x0,2
 444:	fc521ee3          	bne	x4,x5,420 <test_23+0x4>
 448:	0ff013b7          	lui	x7,0xff01
 44c:	ff038393          	addi	x7,x7,-16 # ff00ff0 <_end+0xfefeff0>
 450:	01700193          	addi	x3,x0,23
 454:	06771863          	bne	x14,x7,4c4 <fail>

00000458 <test_24>:
 458:	ff0100b7          	lui	x1,0xff010
 45c:	f0008093          	addi	x1,x1,-256 # ff00ff00 <_end+0xff00df00>
 460:	00104133          	xor	x2,x0,x1
 464:	ff0103b7          	lui	x7,0xff010
 468:	f0038393          	addi	x7,x7,-256 # ff00ff00 <_end+0xff00df00>
 46c:	01800193          	addi	x3,x0,24
 470:	04711a63          	bne	x2,x7,4c4 <fail>

00000474 <test_25>:
 474:	00ff00b7          	lui	x1,0xff0
 478:	0ff08093          	addi	x1,x1,255 # ff00ff <_end+0xfee0ff>
 47c:	0000c133          	xor	x2,x1,x0
 480:	00ff03b7          	lui	x7,0xff0
 484:	0ff38393          	addi	x7,x7,255 # ff00ff <_end+0xfee0ff>
 488:	01900193          	addi	x3,x0,25
 48c:	02711c63          	bne	x2,x7,4c4 <fail>

00000490 <test_26>:
 490:	000040b3          	xor	x1,x0,x0
 494:	00000393          	addi	x7,x0,0
 498:	01a00193          	addi	x3,x0,26
 49c:	02709463          	bne	x1,x7,4c4 <fail>

000004a0 <test_27>:
 4a0:	111110b7          	lui	x1,0x11111
 4a4:	11108093          	addi	x1,x1,273 # 11111111 <_end+0x1110f111>
 4a8:	22222137          	lui	x2,0x22222
 4ac:	22210113          	addi	x2,x2,546 # 22222222 <_end+0x22220222>
 4b0:	0020c033          	xor	x0,x1,x2
 4b4:	00000393          	addi	x7,x0,0
 4b8:	01b00193          	addi	x3,x0,27
 4bc:	00701463          	bne	x0,x7,4c4 <fail>
 4c0:	00301e63          	bne	x0,x3,4dc <pass>

000004c4 <fail>:
 4c4:	00018063          	beq	x3,x0,4c4 <fail>
 4c8:	00119193          	slli	x3,x3,0x1
 4cc:	0011e193          	ori	x3,x3,1
 4d0:	05d00893          	addi	x17,x0,93
 4d4:	00018513          	addi	x10,x3,0
 4d8:	00000073          	ecall

000004dc <pass>:
 4dc:	00100193          	addi	x3,x0,1
 4e0:	05d00893          	addi	x17,x0,93
 4e4:	00000513          	addi	x10,x0,0
 4e8:	00000073          	ecall
 4ec:	c0001073          	unimp
 4f0:	0000                	c.unimp
 4f2:	0000                	c.unimp
 4f4:	0000                	c.unimp
 4f6:	0000                	c.unimp
 4f8:	0000                	c.unimp
 4fa:	0000                	c.unimp
 4fc:	0000                	c.unimp
 4fe:	0000                	c.unimp
 500:	0000                	c.unimp
 502:	0000                	c.unimp
