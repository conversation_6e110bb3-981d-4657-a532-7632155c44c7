
andi:     file format elf32-littleriscv


Disassembly of section .text.init:

00000000 <_start>:
   0:	0040006f          	jal	x0,4 <reset_vector>

00000004 <reset_vector>:
   4:	ff0100b7          	lui	x1,0xff010
   8:	f0008093          	addi	x1,x1,-256 # ff00ff00 <_end+0xff00df00>
   c:	f0f0f713          	andi	x14,x1,-241
  10:	ff0103b7          	lui	x7,0xff010
  14:	f0038393          	addi	x7,x7,-256 # ff00ff00 <_end+0xff00df00>
  18:	00200193          	addi	x3,x0,2
  1c:	1a771463          	bne	x14,x7,1c4 <fail>

00000020 <test_3>:
  20:	0ff010b7          	lui	x1,0xff01
  24:	ff008093          	addi	x1,x1,-16 # ff00ff0 <_end+0xfefeff0>
  28:	0f00f713          	andi	x14,x1,240
  2c:	0f000393          	addi	x7,x0,240
  30:	00300193          	addi	x3,x0,3
  34:	18771863          	bne	x14,x7,1c4 <fail>

00000038 <test_4>:
  38:	00ff00b7          	lui	x1,0xff0
  3c:	0ff08093          	addi	x1,x1,255 # ff00ff <_end+0xfee0ff>
  40:	70f0f713          	andi	x14,x1,1807
  44:	00f00393          	addi	x7,x0,15
  48:	00400193          	addi	x3,x0,4
  4c:	16771c63          	bne	x14,x7,1c4 <fail>

00000050 <test_5>:
  50:	f00ff0b7          	lui	x1,0xf00ff
  54:	00f08093          	addi	x1,x1,15 # f00ff00f <_end+0xf00fd00f>
  58:	0f00f713          	andi	x14,x1,240
  5c:	00000393          	addi	x7,x0,0
  60:	00500193          	addi	x3,x0,5
  64:	16771063          	bne	x14,x7,1c4 <fail>

00000068 <test_6>:
  68:	ff0100b7          	lui	x1,0xff010
  6c:	f0008093          	addi	x1,x1,-256 # ff00ff00 <_end+0xff00df00>
  70:	0f00f093          	andi	x1,x1,240
  74:	00000393          	addi	x7,x0,0
  78:	00600193          	addi	x3,x0,6
  7c:	14709463          	bne	x1,x7,1c4 <fail>

00000080 <test_7>:
  80:	00000213          	addi	x4,x0,0
  84:	0ff010b7          	lui	x1,0xff01
  88:	ff008093          	addi	x1,x1,-16 # ff00ff0 <_end+0xfefeff0>
  8c:	70f0f713          	andi	x14,x1,1807
  90:	00070313          	addi	x6,x14,0
  94:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
  98:	00200293          	addi	x5,x0,2
  9c:	fe5214e3          	bne	x4,x5,84 <test_7+0x4>
  a0:	70000393          	addi	x7,x0,1792
  a4:	00700193          	addi	x3,x0,7
  a8:	10731e63          	bne	x6,x7,1c4 <fail>

000000ac <test_8>:
  ac:	00000213          	addi	x4,x0,0
  b0:	00ff00b7          	lui	x1,0xff0
  b4:	0ff08093          	addi	x1,x1,255 # ff00ff <_end+0xfee0ff>
  b8:	0f00f713          	andi	x14,x1,240
  bc:	00000013          	addi	x0,x0,0
  c0:	00070313          	addi	x6,x14,0
  c4:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
  c8:	00200293          	addi	x5,x0,2
  cc:	fe5212e3          	bne	x4,x5,b0 <test_8+0x4>
  d0:	0f000393          	addi	x7,x0,240
  d4:	00800193          	addi	x3,x0,8
  d8:	0e731663          	bne	x6,x7,1c4 <fail>

000000dc <test_9>:
  dc:	00000213          	addi	x4,x0,0
  e0:	f00ff0b7          	lui	x1,0xf00ff
  e4:	00f08093          	addi	x1,x1,15 # f00ff00f <_end+0xf00fd00f>
  e8:	f0f0f713          	andi	x14,x1,-241
  ec:	00000013          	addi	x0,x0,0
  f0:	00000013          	addi	x0,x0,0
  f4:	00070313          	addi	x6,x14,0
  f8:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
  fc:	00200293          	addi	x5,x0,2
 100:	fe5210e3          	bne	x4,x5,e0 <test_9+0x4>
 104:	f00ff3b7          	lui	x7,0xf00ff
 108:	00f38393          	addi	x7,x7,15 # f00ff00f <_end+0xf00fd00f>
 10c:	00900193          	addi	x3,x0,9
 110:	0a731a63          	bne	x6,x7,1c4 <fail>

00000114 <test_10>:
 114:	00000213          	addi	x4,x0,0
 118:	0ff010b7          	lui	x1,0xff01
 11c:	ff008093          	addi	x1,x1,-16 # ff00ff0 <_end+0xfefeff0>
 120:	70f0f713          	andi	x14,x1,1807
 124:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 128:	00200293          	addi	x5,x0,2
 12c:	fe5216e3          	bne	x4,x5,118 <test_10+0x4>
 130:	70000393          	addi	x7,x0,1792
 134:	00a00193          	addi	x3,x0,10
 138:	08771663          	bne	x14,x7,1c4 <fail>

0000013c <test_11>:
 13c:	00000213          	addi	x4,x0,0
 140:	00ff00b7          	lui	x1,0xff0
 144:	0ff08093          	addi	x1,x1,255 # ff00ff <_end+0xfee0ff>
 148:	00000013          	addi	x0,x0,0
 14c:	0f00f713          	andi	x14,x1,240
 150:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 154:	00200293          	addi	x5,x0,2
 158:	fe5214e3          	bne	x4,x5,140 <test_11+0x4>
 15c:	0f000393          	addi	x7,x0,240
 160:	00b00193          	addi	x3,x0,11
 164:	06771063          	bne	x14,x7,1c4 <fail>

00000168 <test_12>:
 168:	00000213          	addi	x4,x0,0
 16c:	f00ff0b7          	lui	x1,0xf00ff
 170:	00f08093          	addi	x1,x1,15 # f00ff00f <_end+0xf00fd00f>
 174:	00000013          	addi	x0,x0,0
 178:	00000013          	addi	x0,x0,0
 17c:	70f0f713          	andi	x14,x1,1807
 180:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 184:	00200293          	addi	x5,x0,2
 188:	fe5212e3          	bne	x4,x5,16c <test_12+0x4>
 18c:	00f00393          	addi	x7,x0,15
 190:	00c00193          	addi	x3,x0,12
 194:	02771863          	bne	x14,x7,1c4 <fail>

00000198 <test_13>:
 198:	0f007093          	andi	x1,x0,240
 19c:	00000393          	addi	x7,x0,0
 1a0:	00d00193          	addi	x3,x0,13
 1a4:	02709063          	bne	x1,x7,1c4 <fail>

000001a8 <test_14>:
 1a8:	00ff00b7          	lui	x1,0xff0
 1ac:	0ff08093          	addi	x1,x1,255 # ff00ff <_end+0xfee0ff>
 1b0:	70f0f013          	andi	x0,x1,1807
 1b4:	00000393          	addi	x7,x0,0
 1b8:	00e00193          	addi	x3,x0,14
 1bc:	00701463          	bne	x0,x7,1c4 <fail>
 1c0:	00301e63          	bne	x0,x3,1dc <pass>

000001c4 <fail>:
 1c4:	00018063          	beq	x3,x0,1c4 <fail>
 1c8:	00119193          	slli	x3,x3,0x1
 1cc:	0011e193          	ori	x3,x3,1
 1d0:	05d00893          	addi	x17,x0,93
 1d4:	00018513          	addi	x10,x3,0
 1d8:	00000073          	ecall

000001dc <pass>:
 1dc:	00100193          	addi	x3,x0,1
 1e0:	05d00893          	addi	x17,x0,93
 1e4:	00000513          	addi	x10,x0,0
 1e8:	00000073          	ecall
 1ec:	c0001073          	unimp
 1f0:	0000                	c.unimp
 1f2:	0000                	c.unimp
 1f4:	0000                	c.unimp
 1f6:	0000                	c.unimp
 1f8:	0000                	c.unimp
 1fa:	0000                	c.unimp
 1fc:	0000                	c.unimp
 1fe:	0000                	c.unimp
 200:	0000                	c.unimp
 202:	0000                	c.unimp
