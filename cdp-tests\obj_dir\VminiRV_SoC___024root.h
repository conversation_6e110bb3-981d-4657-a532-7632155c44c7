// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design internal header
// See VminiRV_SoC.h for the primary calling header

#ifndef VERILATED_VMINIRV_SOC___024ROOT_H_
#define VERILATED_VMINIRV_SOC___024ROOT_H_  // guard

#include "verilated.h"

class VminiRV_SoC__Syms;
VL_MODULE(VminiRV_SoC___024root) {
  public:

    // DESIGN SPECIFIC STATE
    // Anonymous structures to workaround compiler member-count bugs
    struct {
        VL_IN8(fpga_rst,0,0);
        VL_IN8(fpga_clk,0,0);
        VL_IN8(button,4,0);
        VL_OUT8(dig_en,7,0);
        VL_OUT8(DN_A0,0,0);
        VL_OUT8(DN_A1,0,0);
        VL_OUT8(DN_B0,0,0);
        VL_OUT8(DN_B1,0,0);
        VL_OUT8(DN_C0,0,0);
        VL_OUT8(DN_C1,0,0);
        VL_OUT8(DN_D0,0,0);
        VL_OUT8(DN_D1,0,0);
        VL_OUT8(DN_E0,0,0);
        VL_OUT8(DN_E1,0,0);
        VL_OUT8(DN_F0,0,0);
        VL_OUT8(DN_F1,0,0);
        VL_OUT8(DN_G0,0,0);
        VL_OUT8(DN_G1,0,0);
        VL_OUT8(DN_DP0,0,0);
        VL_OUT8(DN_DP1,0,0);
        VL_OUT8(debug_wb_have_inst,0,0);
        VL_OUT8(debug_wb_ena,0,0);
        VL_OUT8(debug_wb_reg,4,0);
        CData/*0:0*/ miniRV_SoC__DOT__pll_lock;
        CData/*0:0*/ miniRV_SoC__DOT__pll_clk;
        CData/*0:0*/ miniRV_SoC__DOT__we_bridge2dig;
        CData/*0:0*/ miniRV_SoC__DOT__we_bridge2tim;
        CData/*4:0*/ miniRV_SoC__DOT__Core_cpu__DOT__prev_rd;
        CData/*0:0*/ miniRV_SoC__DOT__Core_cpu__DOT__prev_rf_we;
        CData/*0:0*/ miniRV_SoC__DOT__Core_cpu__DOT__have_prev_inst;
        CData/*0:0*/ miniRV_SoC__DOT__Core_cpu__DOT__rf_we;
        CData/*1:0*/ miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel;
        CData/*0:0*/ miniRV_SoC__DOT__Core_cpu__DOT__alu_f;
        CData/*3:0*/ miniRV_SoC__DOT__Core_cpu__DOT__alu_op;
        CData/*0:0*/ miniRV_SoC__DOT__Core_cpu__DOT__alub_sel;
        CData/*2:0*/ miniRV_SoC__DOT__Core_cpu__DOT__sext_op;
        CData/*1:0*/ miniRV_SoC__DOT__Core_cpu__DOT__npc_op;
        CData/*0:0*/ miniRV_SoC__DOT__Core_cpu__DOT__ram_we;
        CData/*0:0*/ miniRV_SoC__DOT__Bridge__DOT__access_tim;
        CData/*5:0*/ miniRV_SoC__DOT__Bridge__DOT__access_bit;
        CData/*0:0*/ miniRV_SoC__DOT__U_Dig__DOT__data_pending;
        CData/*2:0*/ miniRV_SoC__DOT__U_Dig__DOT__scan_cnt;
        CData/*6:0*/ miniRV_SoC__DOT__U_Dig__DOT__seg_code;
        CData/*4:0*/ miniRV_SoC__DOT__U_Button__DOT__button_sync1;
        CData/*4:0*/ miniRV_SoC__DOT__U_Button__DOT__button_sync2;
        CData/*4:0*/ miniRV_SoC__DOT__U_Button__DOT__button_stable;
        CData/*0:0*/ __Vclklast__TOP__fpga_clk;
        CData/*0:0*/ __Vclklast__TOP__fpga_rst;
        VL_IN16(sw,15,0);
        VL_OUT16(led,15,0);
        SData/*15:0*/ miniRV_SoC__DOT__U_Dig__DOT__scan_clk_cnt;
        VL_OUT(debug_wb_pc,31,0);
        VL_OUT(debug_wb_value,31,0);
        IData/*31:0*/ miniRV_SoC__DOT__inst;
        IData/*31:0*/ miniRV_SoC__DOT__Core_cpu__DOT__pc_current;
        IData/*31:0*/ miniRV_SoC__DOT__Core_cpu__DOT__pc_next;
        IData/*31:0*/ miniRV_SoC__DOT__Core_cpu__DOT__prev_pc;
        IData/*31:0*/ miniRV_SoC__DOT__Core_cpu__DOT__prev_rf_wD;
        IData/*31:0*/ miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1;
        IData/*31:0*/ miniRV_SoC__DOT__Core_cpu__DOT__rf_rD2;
        IData/*31:0*/ miniRV_SoC__DOT__Core_cpu__DOT__rf_wD;
        IData/*31:0*/ miniRV_SoC__DOT__Core_cpu__DOT__alu_B;
        IData/*31:0*/ miniRV_SoC__DOT__Core_cpu__DOT__alu_C;
        IData/*31:0*/ miniRV_SoC__DOT__Core_cpu__DOT__sext_ext;
    };
    struct {
        IData/*31:0*/ miniRV_SoC__DOT__Mem_IROM__DOT__i;
        IData/*31:0*/ miniRV_SoC__DOT__Mem_IROM__DOT__j;
        IData/*31:0*/ miniRV_SoC__DOT__Mem_IROM__DOT__mem_file;
        IData/*31:0*/ miniRV_SoC__DOT__Mem_DRAM__DOT__i;
        IData/*31:0*/ miniRV_SoC__DOT__Mem_DRAM__DOT__j;
        IData/*31:0*/ miniRV_SoC__DOT__Mem_DRAM__DOT__mem_file;
        IData/*31:0*/ miniRV_SoC__DOT__U_Dig__DOT__display_data;
        IData/*31:0*/ miniRV_SoC__DOT__U_Dig__DOT__pending_data;
        IData/*23:0*/ miniRV_SoC__DOT__U_Dig__DOT__update_limit_cnt;
        IData/*31:0*/ miniRV_SoC__DOT__U_Timer__DOT__counter0;
        IData/*31:0*/ miniRV_SoC__DOT__U_Timer__DOT__counter1;
        IData/*31:0*/ miniRV_SoC__DOT__U_Timer__DOT__threshold;
        VlUnpacked<IData/*31:0*/, 32> miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers;
        VlUnpacked<IData/*31:0*/, 1048576> miniRV_SoC__DOT__Mem_IROM__DOT__mem;
        VlUnpacked<IData/*31:0*/, 1048576> miniRV_SoC__DOT__Mem_IROM__DOT__mem_rd;
        VlUnpacked<IData/*31:0*/, 1048576> miniRV_SoC__DOT__Mem_DRAM__DOT__mem;
        VlUnpacked<IData/*31:0*/, 1048576> miniRV_SoC__DOT__Mem_DRAM__DOT__mem_rd;
        VlUnpacked<IData/*19:0*/, 5> miniRV_SoC__DOT__U_Button__DOT__debounce_cnt;
        VlUnpacked<CData/*0:0*/, 4> __Vm_traceActivity;
    };

    // INTERNAL VARIABLES
    VminiRV_SoC__Syms* const vlSymsp;

    // CONSTRUCTORS
    VminiRV_SoC___024root(VminiRV_SoC__Syms* symsp, const char* name);
    ~VminiRV_SoC___024root();
    VL_UNCOPYABLE(VminiRV_SoC___024root);

    // INTERNAL METHODS
    void __Vconfigure(bool first);
} VL_ATTR_ALIGNED(VL_CACHE_LINE_BYTES);


#endif  // guard
