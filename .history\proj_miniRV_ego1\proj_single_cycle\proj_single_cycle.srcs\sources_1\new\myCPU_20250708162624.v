`timescale 1ns / 1ps

`include "defines.vh"

module myCPU (
    input  wire         cpu_rst,
    input  wire         cpu_clk,

    // Interface to IROM
`ifdef RUN_TRACE
    output wire [15:0]  inst_addr,
`else
    output wire [13:0]  inst_addr,
`endif
    input  wire [31:0]  inst,
    
    // Interface to Bridge
    output wire [31:0]  Bus_addr,
    input  wire [31:0]  Bus_rdata,
    output wire         Bus_we,
    output wire [31:0]  Bus_wdata

`ifdef RUN_TRACE
    ,// Debug Interface
    output wire         debug_wb_have_inst,
    output wire [31:0]  debug_wb_pc,
    output              debug_wb_ena,
    output wire [ 4:0]  debug_wb_reg,
    output wire [31:0]  debug_wb_value
`endif
);

    // TODO: 完成你自己的单周期CPU设计
    // 内部信号定义
    wire [31:0] pc_current;
    wire [31:0] pc_next;
    wire [31:0] pc_plus4;

`ifdef RUN_TRACE
    // Trace模式下需要保存当前正在执行的指令的信息
    // 因为pc_current和inst在每个周期开始时就更新到下一条指令
    reg [31:0] executing_pc;
    reg [31:0] executing_inst;
    reg [4:0]  executing_rd;
    reg        executing_rf_we;
    reg [31:0] executing_rf_wD;

    always @(posedge cpu_clk or posedge cpu_rst) begin
        if (cpu_rst) begin
            executing_pc <= 32'h0;
            executing_inst <= 32'h0;
            executing_rd <= 5'h0;
            executing_rf_we <= 1'b0;
            executing_rf_wD <= 32'h0;
        end else begin
            executing_pc <= pc_current;
            executing_inst <= inst;
            executing_rd <= rd;
            executing_rf_we <= rf_we;
            executing_rf_wD <= rf_wD;
        end
    end
`endif

    // 指令字段
    wire [6:0]  opcode;            
    wire [4:0]  rs1;               
    wire [4:0]  rs2;                
    wire [4:0]  rd;                
    wire [2:0]  funct3;          
    wire [6:0]  funct7;             

    // 寄存器堆信号
    wire [31:0] rf_rD1;            
    wire [31:0] rf_rD2;             
    reg  [31:0] rf_wD;             
    wire        rf_we;              
    wire [1:0]  rf_wsel;           

    // ALU信号
    wire [31:0] alu_A;              
    wire [31:0] alu_B;             
    wire [31:0] alu_C;             
    wire        alu_f;              
    wire [3:0]  alu_op;             
    wire        alub_sel;           

    // 立即数扩展信号
    wire [31:0] sext_ext;           
    wire [2:0]  sext_op;           

    // NPC信号
    wire [1:0]  npc_op;            
    wire [31:0] npc_pc4;            

    // 内存信号
    wire        ram_we;            

    // 指令字段解析
    assign opcode = inst[6:0];
    assign rs1    = inst[19:15];
    assign rs2    = inst[24:20];
    assign rd     = inst[11:7];
    assign funct3 = inst[14:12];
    assign funct7 = inst[31:25];

    assign pc_plus4 = pc_current + 4;

    // 指令地址输出
`ifdef RUN_TRACE
    assign inst_addr = pc_current[15:0];  // Trace模式：16位字节地址
`else
    assign inst_addr = pc_current[15:2];  // 下板模式：14位字地址
`endif

    // ALU操作数选择
    assign alu_A = rf_rD1;
    assign alu_B = alub_sel ? sext_ext : rf_rD2;

    // 写回数据选择
    always @(*) begin
        case (rf_wsel)
            `WB_ALU:   rf_wD = alu_C;
            `WB_DRAM:  rf_wD = Bus_rdata;
            `WB_PC4:   rf_wD = npc_pc4;
            `WB_EXT:   rf_wD = sext_ext;
            default:   rf_wD = alu_C;
        endcase
    end

    // 总线接口
    assign Bus_addr  = alu_C;
    assign Bus_wdata = rf_rD2;
    assign Bus_we    = ram_we;

    // 模块实例化
    PC U_PC (
        .rst    (cpu_rst),
        .clk    (cpu_clk),
        .din    (pc_next),
        .pc     (pc_current)
    );

    NPC U_NPC (
        .PC       (pc_current),
        .IMM      (sext_ext),
        .ALU_C    (alu_C),
        .npc_op   (npc_op),
        .npc      (pc_next),
        .pc4      (npc_pc4)
    );

    RF U_RF (
        .clk    (cpu_clk),
        .rR1    (rs1),
        .rD1    (rf_rD1),
        .rR2    (rs2),
        .rD2    (rf_rD2),
        .we     (rf_we),
        .wR     (rd),
        .wD     (rf_wD)
    );

    ALU U_ALU (
        .A      (alu_A),
        .B      (alu_B),
        .alu_op (alu_op),
        .C      (alu_C),
        .f      (alu_f)
    );

    SEXT U_SEXT (
        .inst    (inst),
        .sext_op (sext_op),
        .ext     (sext_ext)
    );

    Ctrl U_Ctrl (
        .opcode   (opcode),
        .funct3   (funct3),
        .funct7   (funct7),
        .alu_f    (alu_f),
        .npc_op   (npc_op),
        .rf_we    (rf_we),
        .rf_wsel  (rf_wsel),
        .sext_op  (sext_op),
        .alu_op   (alu_op),
        .alub_sel (alub_sel),
        .ram_we   (ram_we)
    );

`ifdef RUN_TRACE


    // 调试信息输出
    always @(posedge cpu_clk) begin
        if (!cpu_rst) begin
            $display("DEBUG: PC=%h, inst=%h, rd=%d, rf_we=%b, rf_wD=%h",
                     pc_current, inst, rd, rf_we, rf_wD);
        end
    end

    // Debug Interface - 输出当前正在执行的指令的WB阶段信息
    assign debug_wb_have_inst = !cpu_rst;           // 复位后恒为1
    assign debug_wb_pc        = executing_pc;       // 当前正在执行的指令PC
    assign debug_wb_ena       = executing_rf_we;    // 当前指令的写使能
    assign debug_wb_reg       = executing_rd;       // 当前指令的目标寄存器
    assign debug_wb_value     = executing_rf_wD;    // 当前指令的写回值
`endif

endmodule
