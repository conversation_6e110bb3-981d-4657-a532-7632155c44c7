/*

Xilinx Vivado v2018.3 (64-bit) [Major: 2018, Minor: 3]
SW Build: 2405991 on Thu Dec  6 23:38:27 MST 2018
IP Build: 2404404 on Fri Dec  7 01:43:56 MST 2018

Process ID (PID): 26680
License: Customer

Current time: 	Tue Jul 08 14:40:00 CST 2025
Time zone: 	China Standard Time (Asia/Shanghai)

OS: Windows 10
OS Version: 10.0
OS Architecture: amd64
Available processors (cores): 32

Screen size: 2560x1600
Screen resolution (DPI): 144
Available screens: 1
Available disk space: 175 GB
Default font: family=Dialog,name=Dialog,style=plain,size=18

Java version: 	9.0.4 64-bit
Java home: 	E:/Vivado/2018.3/tps/win64/jre9.0.4
Java executable location: 	E:/Vivado/2018.3/tps/win64/jre9.0.4/bin/java.exe
Java initial memory (-Xms): 	128 MB
Java maximum memory (-Xmx):	 3 GB


User name: 	12460
User home directory: C:/Users/<USER>
User working directory: H:/lab2/proj_miniRV_ego1/proj_single_cycle
User country: 	CN
User language: 	zh
User locale: 	zh_CN

RDI_BASEROOT: E:/Vivado
HDI_APPROOT: E:/Vivado/2018.3
RDI_DATADIR: E:/Vivado/2018.3/data
RDI_BINDIR: E:/Vivado/2018.3/bin

Vivado preferences file location: C:/Users/<USER>/AppData/Roaming/Xilinx/Vivado/2018.3/vivado.xml
Vivado preferences directory: C:/Users/<USER>/AppData/Roaming/Xilinx/Vivado/2018.3/
Vivado layouts directory: C:/Users/<USER>/AppData/Roaming/Xilinx/Vivado/2018.3/layouts
PlanAhead jar file location: 	E:/Vivado/2018.3/lib/classes/planAhead.jar
Vivado log file location: 	H:/lab2/proj_miniRV_ego1/proj_single_cycle/vivado.log
Vivado journal file location: 	H:/lab2/proj_miniRV_ego1/proj_single_cycle/vivado.jou
Engine tmp dir: 	H:/lab2/proj_miniRV_ego1/proj_single_cycle/.Xil/Vivado-26680-L

Xilinx Environment Variables
----------------------------
XILINX: E:/Vivado/2018.3/ids_lite/ISE
XILINX_DSP: E:/Vivado/2018.3/ids_lite/ISE
XILINX_PLANAHEAD: E:/Vivado/2018.3
XILINX_SDK: E:/SDK/2018.3
XILINX_VIVADO: E:/Vivado/2018.3
XILINX_VIVADO_HLS: E:/Vivado/2018.3


GUI allocated memory:	129 MB
GUI max memory:		3,072 MB
Engine allocated memory: 705 MB

Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.

*/

// TclEventType: START_GUI
// Tcl Message: start_gui 
// TclEventType: PROJECT_OPEN_DIALOG
// [GUI Memory]: 81 MB (+82333kb) [00:00:08]
// [Engine Memory]: 607 MB (+485026kb) [00:00:08]
// bx (cp):  Open Project : addNotify
// Opening Vivado Project: H:\lab2\proj_miniRV_ego1\proj_single_cycle\proj_single_cycle.xpr. Version: Vivado v2018.3 
// TclEventType: DEBUG_PROBE_SET_CHANGE
// Tcl Message: open_project H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.xpr 
// TclEventType: MSGMGR_MOVEMSG
// TclEventType: FILESET_TARGET_UCF_CHANGE
// TclEventType: FILE_SET_NEW
// TclEventType: RUN_COMPLETED
// TclEventType: FILESET_TARGET_UCF_CHANGE
// TclEventType: RUN_COMPLETED
// TclEventType: FILESET_TARGET_UCF_CHANGE
// TclEventType: RUN_CURRENT
// TclEventType: PROJECT_DASHBOARD_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// TclEventType: PROJECT_DASHBOARD_GADGET_NEW
// TclEventType: PROJECT_DASHBOARD_GADGET_CHANGE
// TclEventType: FILE_SET_CHANGE
// TclEventType: PROJECT_NEW
// [GUI Memory]: 107 MB (+23464kb) [00:00:11]
// [Engine Memory]: 752 MB (+120347kb) [00:00:11]
// [GUI Memory]: 117 MB (+3967kb) [00:00:11]
// [Engine Memory]: 791 MB (+1343kb) [00:00:12]
// WARNING: HEventQueue.dispatchEvent() is taking  1651 ms.
// Tcl Message: open_project H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.xpr 
// Tcl Message: Scanning sources... Finished scanning sources 
// Tcl Message: INFO: [IP_Flow 19-234] Refreshing IP repositories INFO: [IP_Flow 19-1704] No user IP repositories specified INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'E:/Vivado/2018.3/data/ip'. 
// [Engine Memory]: 846 MB (+15883kb) [00:00:12]
// HMemoryUtils.trashcanNow. Engine heap size: 940 MB. GUI used memory: 64 MB. Current time: 7/8/25, 2:40:02 PM CST
// Project name: proj_single_cycle; location: H:/lab2/proj_miniRV_ego1/proj_single_cycle; part: xc7a35tcsg324-1
// [Engine Memory]: 940 MB (+54228kb) [00:00:13]
dismissDialog("Open Project"); // bx (cp)
// Tcl Message: update_compile_order -fileset sources_1 
// Elapsed time: 16 seconds
selectMenu(PAResourceItoN.MainMenuMgr_TOOLS, "Tools"); // X (q, cp)
selectMenu(RDIResourceCommand.RDICommands_CUSTOM_COMMANDS, "Custom Commands"); // ac (cp)
selectMenuItem(RDIResourceCommand.RDICommands_SETTINGS, "Settings..."); // af (cp)
dismissMenu(PAResourceItoN.MainMenuMgr_TOOLS, "Tools"); // X (q, cp)
// Run Command: RDIResourceCommand.RDICommands_SETTINGS
// Tcl Command: 'rdi::info_commands {device::*}'
// Tcl Command: 'rdi::info_commands {debug::*}'
// Tcl Command: 'rdi::info_commands {*}'
// F (cp): Settings: addNotify
// [GUI Memory]: 123 MB (+1059kb) [00:00:31]
// HMemoryUtils.trashcanNow. Engine heap size: 995 MB. GUI used memory: 70 MB. Current time: 7/8/25, 2:40:22 PM CST
selectTree(PAResourceQtoS.SettingsDialog_OPTIONS_TREE, "[optionsRoot, Text Editor]", 6, true); // L (C, F) - Node
selectButton(PAResourceQtoS.SettingsEditorPage_CUSTOM_EDITOR, (String) null); // q (Q, F)
// az (F): Custom Editor Definition: addNotify
setText(PAResourceQtoS.SettingsEditorPage_ENTER_COMMAND_LINE_FOR_CUSTOM, "\"C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe\" -g [file name]:[line number]"); // Y (az)
selectButton(RDIResource.BaseDialog_OK, "OK"); // a (az)
// [Engine Memory]: 995 MB (+8039kb) [00:00:38]
dismissDialog("Custom Editor Definition"); // az (F)
selectButton(RDIResource.BaseDialog_OK, "OK"); // a (F)
// [GUI Memory]: 136 MB (+6330kb) [00:00:39]
dismissDialog("Settings"); // F (cp)
expandTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, Verilog Header]", 1); // B (D, cp)
selectTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, Verilog Header, defines.vh]", 2, false); // B (D, cp)
selectTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, Verilog Header, defines.vh]", 2, false, false, false, false, false, true); // B (D, cp) - Double Click
// Launch External Editor: '"C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/Code.exe" -g "H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/defines.vh":0'
// [GUI Memory]: 152 MB (+9746kb) [00:01:09]
// TclEventType: DG_GRAPH_STALE
// TclEventType: FILE_SET_CHANGE
// TclEventType: DG_GRAPH_STALE
// TclEventType: FILE_SET_CHANGE
// [GUI Memory]: 162 MB (+2996kb) [00:02:12]
// Elapsed time: 179 seconds
selectTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, miniRV_SoC (miniRV_SoC.v)]", 3, true); // B (D, cp) - Node
selectTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, miniRV_SoC (miniRV_SoC.v), Bridge : Bridge (Bridge.v)]", 6, false, false, false, false, false, true); // B (D, cp) - Double Click
// Launch External Editor: '"C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/Code.exe" -g "H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/miniRV_SoC.v":5'
// Elapsed time: 157 seconds
selectTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, Verilog Header, defines.vh]", 2, false); // B (D, cp)
selectTree(PAResourceEtoH.FileSetPanel_FILE_SET_PANEL_TREE, "[root, Design Sources, Verilog Header, defines.vh]", 2, false, false, false, false, false, true); // B (D, cp) - Double Click
// Launch External Editor: '"C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/Code.exe" -g "H:/lab2/proj_miniRV_ego1/proj_single_cycle/proj_single_cycle.srcs/sources_1/new/defines.vh":0'
// TclEventType: DG_GRAPH_STALE
// TclEventType: FILE_SET_CHANGE
// TclEventType: DG_GRAPH_STALE
// TclEventType: FILE_SET_CHANGE
// TclEventType: DG_GRAPH_STALE
// TclEventType: FILE_SET_CHANGE
// TclEventType: DG_GRAPH_STALE
// TclEventType: FILE_SET_CHANGE
