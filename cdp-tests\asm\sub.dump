
sub:     file format elf32-littleriscv


Disassembly of section .text.init:

00000000 <_start>:
   0:	0040006f          	jal	x0,4 <reset_vector>

00000004 <reset_vector>:
   4:	00000093          	addi	x1,x0,0
   8:	00000113          	addi	x2,x0,0
   c:	40208733          	sub	x14,x1,x2
  10:	00000393          	addi	x7,x0,0
  14:	00200193          	addi	x3,x0,2
  18:	4a771663          	bne	x14,x7,4c4 <fail>

0000001c <test_3>:
  1c:	00100093          	addi	x1,x0,1
  20:	00100113          	addi	x2,x0,1
  24:	40208733          	sub	x14,x1,x2
  28:	00000393          	addi	x7,x0,0
  2c:	00300193          	addi	x3,x0,3
  30:	48771a63          	bne	x14,x7,4c4 <fail>

00000034 <test_4>:
  34:	00300093          	addi	x1,x0,3
  38:	00700113          	addi	x2,x0,7
  3c:	40208733          	sub	x14,x1,x2
  40:	ffc00393          	addi	x7,x0,-4
  44:	00400193          	addi	x3,x0,4
  48:	46771e63          	bne	x14,x7,4c4 <fail>

0000004c <test_5>:
  4c:	00000093          	addi	x1,x0,0
  50:	ffff8137          	lui	x2,0xffff8
  54:	40208733          	sub	x14,x1,x2
  58:	000083b7          	lui	x7,0x8
  5c:	00500193          	addi	x3,x0,5
  60:	46771263          	bne	x14,x7,4c4 <fail>

00000064 <test_6>:
  64:	800000b7          	lui	x1,0x80000
  68:	00000113          	addi	x2,x0,0
  6c:	40208733          	sub	x14,x1,x2
  70:	800003b7          	lui	x7,0x80000
  74:	00600193          	addi	x3,x0,6
  78:	44771663          	bne	x14,x7,4c4 <fail>

0000007c <test_7>:
  7c:	800000b7          	lui	x1,0x80000
  80:	ffff8137          	lui	x2,0xffff8
  84:	40208733          	sub	x14,x1,x2
  88:	800083b7          	lui	x7,0x80008
  8c:	00700193          	addi	x3,x0,7
  90:	42771a63          	bne	x14,x7,4c4 <fail>

00000094 <test_8>:
  94:	00000093          	addi	x1,x0,0
  98:	00008137          	lui	x2,0x8
  9c:	fff10113          	addi	x2,x2,-1 # 7fff <_end+0x5fff>
  a0:	40208733          	sub	x14,x1,x2
  a4:	ffff83b7          	lui	x7,0xffff8
  a8:	00138393          	addi	x7,x7,1 # ffff8001 <_end+0xffff6001>
  ac:	00800193          	addi	x3,x0,8
  b0:	40771a63          	bne	x14,x7,4c4 <fail>

000000b4 <test_9>:
  b4:	800000b7          	lui	x1,0x80000
  b8:	fff08093          	addi	x1,x1,-1 # 7fffffff <_end+0x7fffdfff>
  bc:	00000113          	addi	x2,x0,0
  c0:	40208733          	sub	x14,x1,x2
  c4:	800003b7          	lui	x7,0x80000
  c8:	fff38393          	addi	x7,x7,-1 # 7fffffff <_end+0x7fffdfff>
  cc:	00900193          	addi	x3,x0,9
  d0:	3e771a63          	bne	x14,x7,4c4 <fail>

000000d4 <test_10>:
  d4:	800000b7          	lui	x1,0x80000
  d8:	fff08093          	addi	x1,x1,-1 # 7fffffff <_end+0x7fffdfff>
  dc:	00008137          	lui	x2,0x8
  e0:	fff10113          	addi	x2,x2,-1 # 7fff <_end+0x5fff>
  e4:	40208733          	sub	x14,x1,x2
  e8:	7fff83b7          	lui	x7,0x7fff8
  ec:	00a00193          	addi	x3,x0,10
  f0:	3c771a63          	bne	x14,x7,4c4 <fail>

000000f4 <test_11>:
  f4:	800000b7          	lui	x1,0x80000
  f8:	00008137          	lui	x2,0x8
  fc:	fff10113          	addi	x2,x2,-1 # 7fff <_end+0x5fff>
 100:	40208733          	sub	x14,x1,x2
 104:	7fff83b7          	lui	x7,0x7fff8
 108:	00138393          	addi	x7,x7,1 # 7fff8001 <_end+0x7fff6001>
 10c:	00b00193          	addi	x3,x0,11
 110:	3a771a63          	bne	x14,x7,4c4 <fail>

00000114 <test_12>:
 114:	800000b7          	lui	x1,0x80000
 118:	fff08093          	addi	x1,x1,-1 # 7fffffff <_end+0x7fffdfff>
 11c:	ffff8137          	lui	x2,0xffff8
 120:	40208733          	sub	x14,x1,x2
 124:	800083b7          	lui	x7,0x80008
 128:	fff38393          	addi	x7,x7,-1 # 80007fff <_end+0x80005fff>
 12c:	00c00193          	addi	x3,x0,12
 130:	38771a63          	bne	x14,x7,4c4 <fail>

00000134 <test_13>:
 134:	00000093          	addi	x1,x0,0
 138:	fff00113          	addi	x2,x0,-1
 13c:	40208733          	sub	x14,x1,x2
 140:	00100393          	addi	x7,x0,1
 144:	00d00193          	addi	x3,x0,13
 148:	36771e63          	bne	x14,x7,4c4 <fail>

0000014c <test_14>:
 14c:	fff00093          	addi	x1,x0,-1
 150:	00100113          	addi	x2,x0,1
 154:	40208733          	sub	x14,x1,x2
 158:	ffe00393          	addi	x7,x0,-2
 15c:	00e00193          	addi	x3,x0,14
 160:	36771263          	bne	x14,x7,4c4 <fail>

00000164 <test_15>:
 164:	fff00093          	addi	x1,x0,-1
 168:	fff00113          	addi	x2,x0,-1
 16c:	40208733          	sub	x14,x1,x2
 170:	00000393          	addi	x7,x0,0
 174:	00f00193          	addi	x3,x0,15
 178:	34771663          	bne	x14,x7,4c4 <fail>

0000017c <test_16>:
 17c:	00d00093          	addi	x1,x0,13
 180:	00b00113          	addi	x2,x0,11
 184:	402080b3          	sub	x1,x1,x2
 188:	00200393          	addi	x7,x0,2
 18c:	01000193          	addi	x3,x0,16
 190:	32709a63          	bne	x1,x7,4c4 <fail>

00000194 <test_17>:
 194:	00e00093          	addi	x1,x0,14
 198:	00b00113          	addi	x2,x0,11
 19c:	40208133          	sub	x2,x1,x2
 1a0:	00300393          	addi	x7,x0,3
 1a4:	01100193          	addi	x3,x0,17
 1a8:	30711e63          	bne	x2,x7,4c4 <fail>

000001ac <test_18>:
 1ac:	00d00093          	addi	x1,x0,13
 1b0:	401080b3          	sub	x1,x1,x1
 1b4:	00000393          	addi	x7,x0,0
 1b8:	01200193          	addi	x3,x0,18
 1bc:	30709463          	bne	x1,x7,4c4 <fail>

000001c0 <test_19>:
 1c0:	00000213          	addi	x4,x0,0
 1c4:	00d00093          	addi	x1,x0,13
 1c8:	00b00113          	addi	x2,x0,11
 1cc:	40208733          	sub	x14,x1,x2
 1d0:	00070313          	addi	x6,x14,0
 1d4:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 1d8:	00200293          	addi	x5,x0,2
 1dc:	fe5214e3          	bne	x4,x5,1c4 <test_19+0x4>
 1e0:	00200393          	addi	x7,x0,2
 1e4:	01300193          	addi	x3,x0,19
 1e8:	2c731e63          	bne	x6,x7,4c4 <fail>

000001ec <test_20>:
 1ec:	00000213          	addi	x4,x0,0
 1f0:	00e00093          	addi	x1,x0,14
 1f4:	00b00113          	addi	x2,x0,11
 1f8:	40208733          	sub	x14,x1,x2
 1fc:	00000013          	addi	x0,x0,0
 200:	00070313          	addi	x6,x14,0
 204:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 208:	00200293          	addi	x5,x0,2
 20c:	fe5212e3          	bne	x4,x5,1f0 <test_20+0x4>
 210:	00300393          	addi	x7,x0,3
 214:	01400193          	addi	x3,x0,20
 218:	2a731663          	bne	x6,x7,4c4 <fail>

0000021c <test_21>:
 21c:	00000213          	addi	x4,x0,0
 220:	00f00093          	addi	x1,x0,15
 224:	00b00113          	addi	x2,x0,11
 228:	40208733          	sub	x14,x1,x2
 22c:	00000013          	addi	x0,x0,0
 230:	00000013          	addi	x0,x0,0
 234:	00070313          	addi	x6,x14,0
 238:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 23c:	00200293          	addi	x5,x0,2
 240:	fe5210e3          	bne	x4,x5,220 <test_21+0x4>
 244:	00400393          	addi	x7,x0,4
 248:	01500193          	addi	x3,x0,21
 24c:	26731c63          	bne	x6,x7,4c4 <fail>

00000250 <test_22>:
 250:	00000213          	addi	x4,x0,0
 254:	00d00093          	addi	x1,x0,13
 258:	00b00113          	addi	x2,x0,11
 25c:	40208733          	sub	x14,x1,x2
 260:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 264:	00200293          	addi	x5,x0,2
 268:	fe5216e3          	bne	x4,x5,254 <test_22+0x4>
 26c:	00200393          	addi	x7,x0,2
 270:	01600193          	addi	x3,x0,22
 274:	24771863          	bne	x14,x7,4c4 <fail>

00000278 <test_23>:
 278:	00000213          	addi	x4,x0,0
 27c:	00e00093          	addi	x1,x0,14
 280:	00b00113          	addi	x2,x0,11
 284:	00000013          	addi	x0,x0,0
 288:	40208733          	sub	x14,x1,x2
 28c:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 290:	00200293          	addi	x5,x0,2
 294:	fe5214e3          	bne	x4,x5,27c <test_23+0x4>
 298:	00300393          	addi	x7,x0,3
 29c:	01700193          	addi	x3,x0,23
 2a0:	22771263          	bne	x14,x7,4c4 <fail>

000002a4 <test_24>:
 2a4:	00000213          	addi	x4,x0,0
 2a8:	00f00093          	addi	x1,x0,15
 2ac:	00b00113          	addi	x2,x0,11
 2b0:	00000013          	addi	x0,x0,0
 2b4:	00000013          	addi	x0,x0,0
 2b8:	40208733          	sub	x14,x1,x2
 2bc:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 2c0:	00200293          	addi	x5,x0,2
 2c4:	fe5212e3          	bne	x4,x5,2a8 <test_24+0x4>
 2c8:	00400393          	addi	x7,x0,4
 2cc:	01800193          	addi	x3,x0,24
 2d0:	1e771a63          	bne	x14,x7,4c4 <fail>

000002d4 <test_25>:
 2d4:	00000213          	addi	x4,x0,0
 2d8:	00d00093          	addi	x1,x0,13
 2dc:	00000013          	addi	x0,x0,0
 2e0:	00b00113          	addi	x2,x0,11
 2e4:	40208733          	sub	x14,x1,x2
 2e8:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 2ec:	00200293          	addi	x5,x0,2
 2f0:	fe5214e3          	bne	x4,x5,2d8 <test_25+0x4>
 2f4:	00200393          	addi	x7,x0,2
 2f8:	01900193          	addi	x3,x0,25
 2fc:	1c771463          	bne	x14,x7,4c4 <fail>

00000300 <test_26>:
 300:	00000213          	addi	x4,x0,0
 304:	00e00093          	addi	x1,x0,14
 308:	00000013          	addi	x0,x0,0
 30c:	00b00113          	addi	x2,x0,11
 310:	00000013          	addi	x0,x0,0
 314:	40208733          	sub	x14,x1,x2
 318:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 31c:	00200293          	addi	x5,x0,2
 320:	fe5212e3          	bne	x4,x5,304 <test_26+0x4>
 324:	00300393          	addi	x7,x0,3
 328:	01a00193          	addi	x3,x0,26
 32c:	18771c63          	bne	x14,x7,4c4 <fail>

00000330 <test_27>:
 330:	00000213          	addi	x4,x0,0
 334:	00f00093          	addi	x1,x0,15
 338:	00000013          	addi	x0,x0,0
 33c:	00000013          	addi	x0,x0,0
 340:	00b00113          	addi	x2,x0,11
 344:	40208733          	sub	x14,x1,x2
 348:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 34c:	00200293          	addi	x5,x0,2
 350:	fe5212e3          	bne	x4,x5,334 <test_27+0x4>
 354:	00400393          	addi	x7,x0,4
 358:	01b00193          	addi	x3,x0,27
 35c:	16771463          	bne	x14,x7,4c4 <fail>

00000360 <test_28>:
 360:	00000213          	addi	x4,x0,0
 364:	00b00113          	addi	x2,x0,11
 368:	00d00093          	addi	x1,x0,13
 36c:	40208733          	sub	x14,x1,x2
 370:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 374:	00200293          	addi	x5,x0,2
 378:	fe5216e3          	bne	x4,x5,364 <test_28+0x4>
 37c:	00200393          	addi	x7,x0,2
 380:	01c00193          	addi	x3,x0,28
 384:	14771063          	bne	x14,x7,4c4 <fail>

00000388 <test_29>:
 388:	00000213          	addi	x4,x0,0
 38c:	00b00113          	addi	x2,x0,11
 390:	00e00093          	addi	x1,x0,14
 394:	00000013          	addi	x0,x0,0
 398:	40208733          	sub	x14,x1,x2
 39c:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 3a0:	00200293          	addi	x5,x0,2
 3a4:	fe5214e3          	bne	x4,x5,38c <test_29+0x4>
 3a8:	00300393          	addi	x7,x0,3
 3ac:	01d00193          	addi	x3,x0,29
 3b0:	10771a63          	bne	x14,x7,4c4 <fail>

000003b4 <test_30>:
 3b4:	00000213          	addi	x4,x0,0
 3b8:	00b00113          	addi	x2,x0,11
 3bc:	00f00093          	addi	x1,x0,15
 3c0:	00000013          	addi	x0,x0,0
 3c4:	00000013          	addi	x0,x0,0
 3c8:	40208733          	sub	x14,x1,x2
 3cc:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 3d0:	00200293          	addi	x5,x0,2
 3d4:	fe5212e3          	bne	x4,x5,3b8 <test_30+0x4>
 3d8:	00400393          	addi	x7,x0,4
 3dc:	01e00193          	addi	x3,x0,30
 3e0:	0e771263          	bne	x14,x7,4c4 <fail>

000003e4 <test_31>:
 3e4:	00000213          	addi	x4,x0,0
 3e8:	00b00113          	addi	x2,x0,11
 3ec:	00000013          	addi	x0,x0,0
 3f0:	00d00093          	addi	x1,x0,13
 3f4:	40208733          	sub	x14,x1,x2
 3f8:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 3fc:	00200293          	addi	x5,x0,2
 400:	fe5214e3          	bne	x4,x5,3e8 <test_31+0x4>
 404:	00200393          	addi	x7,x0,2
 408:	01f00193          	addi	x3,x0,31
 40c:	0a771c63          	bne	x14,x7,4c4 <fail>

00000410 <test_32>:
 410:	00000213          	addi	x4,x0,0
 414:	00b00113          	addi	x2,x0,11
 418:	00000013          	addi	x0,x0,0
 41c:	00e00093          	addi	x1,x0,14
 420:	00000013          	addi	x0,x0,0
 424:	40208733          	sub	x14,x1,x2
 428:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 42c:	00200293          	addi	x5,x0,2
 430:	fe5212e3          	bne	x4,x5,414 <test_32+0x4>
 434:	00300393          	addi	x7,x0,3
 438:	02000193          	addi	x3,x0,32
 43c:	08771463          	bne	x14,x7,4c4 <fail>

00000440 <test_33>:
 440:	00000213          	addi	x4,x0,0
 444:	00b00113          	addi	x2,x0,11
 448:	00000013          	addi	x0,x0,0
 44c:	00000013          	addi	x0,x0,0
 450:	00f00093          	addi	x1,x0,15
 454:	40208733          	sub	x14,x1,x2
 458:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 45c:	00200293          	addi	x5,x0,2
 460:	fe5212e3          	bne	x4,x5,444 <test_33+0x4>
 464:	00400393          	addi	x7,x0,4
 468:	02100193          	addi	x3,x0,33
 46c:	04771c63          	bne	x14,x7,4c4 <fail>

00000470 <test_34>:
 470:	ff100093          	addi	x1,x0,-15
 474:	40100133          	sub	x2,x0,x1
 478:	00f00393          	addi	x7,x0,15
 47c:	02200193          	addi	x3,x0,34
 480:	04711263          	bne	x2,x7,4c4 <fail>

00000484 <test_35>:
 484:	02000093          	addi	x1,x0,32
 488:	40008133          	sub	x2,x1,x0
 48c:	02000393          	addi	x7,x0,32
 490:	02300193          	addi	x3,x0,35
 494:	02711863          	bne	x2,x7,4c4 <fail>

00000498 <test_36>:
 498:	400000b3          	sub	x1,x0,x0
 49c:	00000393          	addi	x7,x0,0
 4a0:	02400193          	addi	x3,x0,36
 4a4:	02709063          	bne	x1,x7,4c4 <fail>

000004a8 <test_37>:
 4a8:	01000093          	addi	x1,x0,16
 4ac:	01e00113          	addi	x2,x0,30
 4b0:	40208033          	sub	x0,x1,x2
 4b4:	00000393          	addi	x7,x0,0
 4b8:	02500193          	addi	x3,x0,37
 4bc:	00701463          	bne	x0,x7,4c4 <fail>
 4c0:	00301e63          	bne	x0,x3,4dc <pass>

000004c4 <fail>:
 4c4:	00018063          	beq	x3,x0,4c4 <fail>
 4c8:	00119193          	slli	x3,x3,0x1
 4cc:	0011e193          	ori	x3,x3,1
 4d0:	05d00893          	addi	x17,x0,93
 4d4:	00018513          	addi	x10,x3,0
 4d8:	00000073          	ecall

000004dc <pass>:
 4dc:	00100193          	addi	x3,x0,1
 4e0:	05d00893          	addi	x17,x0,93
 4e4:	00000513          	addi	x10,x0,0
 4e8:	00000073          	ecall
 4ec:	c0001073          	unimp
 4f0:	0000                	c.unimp
 4f2:	0000                	c.unimp
 4f4:	0000                	c.unimp
 4f6:	0000                	c.unimp
 4f8:	0000                	c.unimp
 4fa:	0000                	c.unimp
 4fc:	0000                	c.unimp
 4fe:	0000                	c.unimp
 500:	0000                	c.unimp
 502:	0000                	c.unimp
