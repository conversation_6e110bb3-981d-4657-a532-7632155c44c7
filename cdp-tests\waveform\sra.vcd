$version Generated by VerilatedVcd $end
$date Tue Jul  8 10:26:38 2025 $end
$timescale 1ps $end

 $scope module TOP $end
  $var wire  1 -! DN_A0 $end
  $var wire  1 .! DN_A1 $end
  $var wire  1 /! DN_B0 $end
  $var wire  1 0! DN_B1 $end
  $var wire  1 1! DN_C0 $end
  $var wire  1 2! DN_C1 $end
  $var wire  1 3! DN_D0 $end
  $var wire  1 4! DN_D1 $end
  $var wire  1 ;! DN_DP0 $end
  $var wire  1 <! DN_DP1 $end
  $var wire  1 5! DN_E0 $end
  $var wire  1 6! DN_E1 $end
  $var wire  1 7! DN_F0 $end
  $var wire  1 8! DN_F1 $end
  $var wire  1 9! DN_G0 $end
  $var wire  1 :! DN_G1 $end
  $var wire  5 +! button [4:0] $end
  $var wire  1 @! debug_wb_ena $end
  $var wire  1 >! debug_wb_have_inst $end
  $var wire 32 ?! debug_wb_pc [31:0] $end
  $var wire  5 A! debug_wb_reg [4:0] $end
  $var wire 32 B! debug_wb_value [31:0] $end
  $var wire  8 ,! dig_en [7:0] $end
  $var wire  1 )! fpga_clk $end
  $var wire  1 (! fpga_rst $end
  $var wire 16 =! led [15:0] $end
  $var wire 16 *! sw [15:0] $end
  $scope module miniRV_SoC $end
   $var wire 32 c Bus_addr [31:0] $end
   $var wire 32 C! Bus_rdata [31:0] $end
   $var wire 32 e Bus_wdata [31:0] $end
   $var wire  1 d Bus_we $end
   $var wire  1 -! DN_A0 $end
   $var wire  1 .! DN_A1 $end
   $var wire  1 /! DN_B0 $end
   $var wire  1 0! DN_B1 $end
   $var wire  1 1! DN_C0 $end
   $var wire  1 2! DN_C1 $end
   $var wire  1 3! DN_D0 $end
   $var wire  1 4! DN_D1 $end
   $var wire  1 ;! DN_DP0 $end
   $var wire  1 <! DN_DP1 $end
   $var wire  1 5! DN_E0 $end
   $var wire  1 6! DN_E1 $end
   $var wire  1 7! DN_F0 $end
   $var wire  1 8! DN_F1 $end
   $var wire  1 9! DN_G0 $end
   $var wire  1 :! DN_G1 $end
   $var wire 32 c addr_bridge2btn [31:0] $end
   $var wire 32 c addr_bridge2dig [31:0] $end
   $var wire 32 c addr_bridge2dram [31:0] $end
   $var wire 32 c addr_bridge2led [31:0] $end
   $var wire 32 c addr_bridge2sw [31:0] $end
   $var wire 32 c addr_bridge2tim [31:0] $end
   $var wire  5 +! button [4:0] $end
   $var wire  1 )! clk_bridge2btn $end
   $var wire  1 )! clk_bridge2dig $end
   $var wire  1 )! clk_bridge2dram $end
   $var wire  1 )! clk_bridge2led $end
   $var wire  1 )! clk_bridge2sw $end
   $var wire  1 )! clk_bridge2tim $end
   $var wire  1 )! cpu_clk $end
   $var wire  1 @! debug_wb_ena $end
   $var wire  1 >! debug_wb_have_inst $end
   $var wire 32 ?! debug_wb_pc [31:0] $end
   $var wire  5 A! debug_wb_reg [4:0] $end
   $var wire 32 B! debug_wb_value [31:0] $end
   $var wire  8 ,! dig_en [7:0] $end
   $var wire  1 )! fpga_clk $end
   $var wire  1 (! fpga_rst $end
   $var wire 32 b inst [31:0] $end
   $var wire 16 a inst_addr [15:0] $end
   $var wire 16 =! led [15:0] $end
   $var wire  1 J! pll_clk $end
   $var wire  1 I! pll_lock $end
   $var wire 32 G! rdata_btn2bridge [31:0] $end
   $var wire 32 D! rdata_dram2bridge [31:0] $end
   $var wire 32 F! rdata_sw2bridge [31:0] $end
   $var wire 32 E! rdata_tim2bridge [31:0] $end
   $var wire  1 (! rst_bridge2btn $end
   $var wire  1 (! rst_bridge2dig $end
   $var wire  1 (! rst_bridge2led $end
   $var wire  1 (! rst_bridge2sw $end
   $var wire  1 (! rst_bridge2tim $end
   $var wire 16 *! sw [15:0] $end
   $var wire 32 e wdata_bridge2dig [31:0] $end
   $var wire 32 e wdata_bridge2dram [31:0] $end
   $var wire 32 e wdata_bridge2led [31:0] $end
   $var wire 32 e wdata_bridge2tim [31:0] $end
   $var wire  1 g we_bridge2dig $end
   $var wire  1 f we_bridge2dram $end
   $var wire  1 i we_bridge2led $end
   $var wire  1 h we_bridge2tim $end
   $scope module Bridge $end
    $var wire  6 p access_bit [5:0] $end
    $var wire  1 o access_btn $end
    $var wire  1 k access_dig $end
    $var wire  1 m access_led $end
    $var wire  1 j access_mem $end
    $var wire  1 n access_sw $end
    $var wire  1 l access_tim $end
    $var wire 32 c addr_from_cpu [31:0] $end
    $var wire 32 c addr_to_btn [31:0] $end
    $var wire 32 c addr_to_dig [31:0] $end
    $var wire 32 c addr_to_dram [31:0] $end
    $var wire 32 c addr_to_led [31:0] $end
    $var wire 32 c addr_to_sw [31:0] $end
    $var wire 32 c addr_to_tim [31:0] $end
    $var wire  1 )! clk_from_cpu $end
    $var wire  1 )! clk_to_btn $end
    $var wire  1 )! clk_to_dig $end
    $var wire  1 )! clk_to_dram $end
    $var wire  1 )! clk_to_led $end
    $var wire  1 )! clk_to_sw $end
    $var wire  1 )! clk_to_tim $end
    $var wire 32 G! rdata_from_btn [31:0] $end
    $var wire 32 D! rdata_from_dram [31:0] $end
    $var wire 32 F! rdata_from_sw [31:0] $end
    $var wire 32 E! rdata_from_tim [31:0] $end
    $var wire 32 C! rdata_to_cpu [31:0] $end
    $var wire  1 (! rst_from_cpu $end
    $var wire  1 (! rst_to_btn $end
    $var wire  1 (! rst_to_dig $end
    $var wire  1 (! rst_to_led $end
    $var wire  1 (! rst_to_sw $end
    $var wire  1 (! rst_to_tim $end
    $var wire 32 e wdata_from_cpu [31:0] $end
    $var wire 32 e wdata_to_dig [31:0] $end
    $var wire 32 e wdata_to_dram [31:0] $end
    $var wire 32 e wdata_to_led [31:0] $end
    $var wire 32 e wdata_to_tim [31:0] $end
    $var wire  1 d we_from_cpu $end
    $var wire  1 g we_to_dig $end
    $var wire  1 f we_to_dram $end
    $var wire  1 i we_to_led $end
    $var wire  1 h we_to_tim $end
   $upscope $end
   $scope module Core_cpu $end
    $var wire 32 c Bus_addr [31:0] $end
    $var wire 32 C! Bus_rdata [31:0] $end
    $var wire 32 e Bus_wdata [31:0] $end
    $var wire  1 d Bus_we $end
    $var wire 32 z alu_A [31:0] $end
    $var wire 32 } alu_B [31:0] $end
    $var wire 32 c alu_C [31:0] $end
    $var wire  1 ~ alu_f $end
    $var wire  4 !! alu_op [3:0] $end
    $var wire  1 "! alub_sel $end
    $var wire  1 )! cpu_clk $end
    $var wire  1 (! cpu_rst $end
    $var wire  1 @! debug_wb_ena $end
    $var wire  1 >! debug_wb_have_inst $end
    $var wire 32 ?! debug_wb_pc [31:0] $end
    $var wire  5 A! debug_wb_reg [4:0] $end
    $var wire 32 B! debug_wb_value [31:0] $end
    $var wire  3 x funct3 [2:0] $end
    $var wire  7 y funct7 [6:0] $end
    $var wire  1 - have_prev_inst $end
    $var wire 32 b inst [31:0] $end
    $var wire 16 a inst_addr [15:0] $end
    $var wire  2 %! npc_op [1:0] $end
    $var wire 32 s npc_pc4 [31:0] $end
    $var wire  7 t opcode [6:0] $end
    $var wire 32 q pc_current [31:0] $end
    $var wire 32 r pc_next [31:0] $end
    $var wire 32 s pc_plus4 [31:0] $end
    $var wire 32 ) prev_pc [31:0] $end
    $var wire  5 * prev_rd [4:0] $end
    $var wire 32 , prev_rf_wD [31:0] $end
    $var wire  1 + prev_rf_we $end
    $var wire  1 d ram_we $end
    $var wire  5 w rd [4:0] $end
    $var wire 32 z rf_rD1 [31:0] $end
    $var wire 32 e rf_rD2 [31:0] $end
    $var wire 32 H! rf_wD [31:0] $end
    $var wire  1 { rf_we $end
    $var wire  2 | rf_wsel [1:0] $end
    $var wire  5 u rs1 [4:0] $end
    $var wire  5 v rs2 [4:0] $end
    $var wire 32 #! sext_ext [31:0] $end
    $var wire  3 $! sext_op [2:0] $end
    $scope module U_ALU $end
     $var wire 32 z A [31:0] $end
     $var wire 32 } B [31:0] $end
     $var wire 32 c C [31:0] $end
     $var wire  4 !! alu_op [3:0] $end
     $var wire  1 ~ f $end
    $upscope $end
    $scope module U_Ctrl $end
     $var wire  1 ~ alu_f $end
     $var wire  4 !! alu_op [3:0] $end
     $var wire  1 "! alub_sel $end
     $var wire  3 x funct3 [2:0] $end
     $var wire  7 y funct7 [6:0] $end
     $var wire  2 %! npc_op [1:0] $end
     $var wire  7 t opcode [6:0] $end
     $var wire  1 d ram_we $end
     $var wire  1 { rf_we $end
     $var wire  2 | rf_wsel [1:0] $end
     $var wire  3 $! sext_op [2:0] $end
    $upscope $end
    $scope module U_NPC $end
     $var wire 32 c ALU_C [31:0] $end
     $var wire 32 #! IMM [31:0] $end
     $var wire 32 q PC [31:0] $end
     $var wire 32 r npc [31:0] $end
     $var wire  2 %! npc_op [1:0] $end
     $var wire 32 s pc4 [31:0] $end
    $upscope $end
    $scope module U_PC $end
     $var wire  1 )! clk $end
     $var wire 32 r din [31:0] $end
     $var wire 32 q pc [31:0] $end
     $var wire  1 (! rst $end
    $upscope $end
    $scope module U_RF $end
     $var wire  1 )! clk $end
     $var wire 32 z rD1 [31:0] $end
     $var wire 32 e rD2 [31:0] $end
     $var wire  5 u rR1 [4:0] $end
     $var wire  5 v rR2 [4:0] $end
     $var wire 32 A registers[0] [31:0] $end
     $var wire 32 K registers[10] [31:0] $end
     $var wire 32 L registers[11] [31:0] $end
     $var wire 32 M registers[12] [31:0] $end
     $var wire 32 N registers[13] [31:0] $end
     $var wire 32 O registers[14] [31:0] $end
     $var wire 32 P registers[15] [31:0] $end
     $var wire 32 Q registers[16] [31:0] $end
     $var wire 32 R registers[17] [31:0] $end
     $var wire 32 S registers[18] [31:0] $end
     $var wire 32 T registers[19] [31:0] $end
     $var wire 32 B registers[1] [31:0] $end
     $var wire 32 U registers[20] [31:0] $end
     $var wire 32 V registers[21] [31:0] $end
     $var wire 32 W registers[22] [31:0] $end
     $var wire 32 X registers[23] [31:0] $end
     $var wire 32 Y registers[24] [31:0] $end
     $var wire 32 Z registers[25] [31:0] $end
     $var wire 32 [ registers[26] [31:0] $end
     $var wire 32 \ registers[27] [31:0] $end
     $var wire 32 ] registers[28] [31:0] $end
     $var wire 32 ^ registers[29] [31:0] $end
     $var wire 32 C registers[2] [31:0] $end
     $var wire 32 _ registers[30] [31:0] $end
     $var wire 32 ` registers[31] [31:0] $end
     $var wire 32 D registers[3] [31:0] $end
     $var wire 32 E registers[4] [31:0] $end
     $var wire 32 F registers[5] [31:0] $end
     $var wire 32 G registers[6] [31:0] $end
     $var wire 32 H registers[7] [31:0] $end
     $var wire 32 I registers[8] [31:0] $end
     $var wire 32 J registers[9] [31:0] $end
     $var wire 32 H! wD [31:0] $end
     $var wire  5 w wR [4:0] $end
     $var wire  1 { we $end
    $upscope $end
    $scope module U_SEXT $end
     $var wire 32 #! ext [31:0] $end
     $var wire 32 b inst [31:0] $end
     $var wire  3 $! sext_op [2:0] $end
    $upscope $end
   $upscope $end
   $scope module Mem_DRAM $end
    $var wire 32 K! ADDR_BITS [31:0] $end
    $var wire 16 &! a [15:0] $end
    $var wire  1 )! clk $end
    $var wire 32 e d [31:0] $end
    $var wire 32 # i [31:0] $end
    $var wire 32 $ j [31:0] $end
    $var wire 32 % mem_file [31:0] $end
    $var wire 32 D! spo [31:0] $end
    $var wire  1 f we $end
   $upscope $end
   $scope module Mem_IROM $end
    $var wire 32 K! ADDR_BITS [31:0] $end
    $var wire 16 '! a [15:0] $end
    $var wire 32 & i [31:0] $end
    $var wire 32 ' j [31:0] $end
    $var wire 32 ( mem_file [31:0] $end
    $var wire 32 b spo [31:0] $end
   $upscope $end
   $scope module U_Button $end
    $var wire 32 c addr [31:0] $end
    $var wire  5 +! button [4:0] $end
    $var wire  5 0 button_stable [4:0] $end
    $var wire  5 . button_sync1 [4:0] $end
    $var wire  5 / button_sync2 [4:0] $end
    $var wire  1 )! clk $end
    $var wire 20 1 debounce_cnt[0] [19:0] $end
    $var wire 20 2 debounce_cnt[1] [19:0] $end
    $var wire 20 3 debounce_cnt[2] [19:0] $end
    $var wire 20 4 debounce_cnt[3] [19:0] $end
    $var wire 20 5 debounce_cnt[4] [19:0] $end
    $var wire 32 G! rdata [31:0] $end
    $var wire  1 (! rst $end
   $upscope $end
   $scope module U_Dig $end
    $var wire  1 -! DN_A0 $end
    $var wire  1 .! DN_A1 $end
    $var wire  1 /! DN_B0 $end
    $var wire  1 0! DN_B1 $end
    $var wire  1 1! DN_C0 $end
    $var wire  1 2! DN_C1 $end
    $var wire  1 3! DN_D0 $end
    $var wire  1 4! DN_D1 $end
    $var wire  1 ;! DN_DP0 $end
    $var wire  1 <! DN_DP1 $end
    $var wire  1 5! DN_E0 $end
    $var wire  1 6! DN_E1 $end
    $var wire  1 7! DN_F0 $end
    $var wire  1 8! DN_F1 $end
    $var wire  1 9! DN_G0 $end
    $var wire  1 :! DN_G1 $end
    $var wire 32 c addr [31:0] $end
    $var wire  1 )! clk $end
    $var wire  4 < current_digit [3:0] $end
    $var wire  1 8 data_pending $end
    $var wire  8 ,! dig_en [7:0] $end
    $var wire 32 6 display_data [31:0] $end
    $var wire 32 7 pending_data [31:0] $end
    $var wire  1 (! rst $end
    $var wire 16 : scan_clk_cnt [15:0] $end
    $var wire  3 9 scan_cnt [2:0] $end
    $var wire  7 = seg_code [6:0] $end
    $var wire 24 ; update_limit_cnt [23:0] $end
    $var wire 32 e wdata [31:0] $end
    $var wire  1 g we $end
   $upscope $end
   $scope module U_Led $end
    $var wire 32 c addr [31:0] $end
    $var wire  1 )! clk $end
    $var wire 16 =! led [15:0] $end
    $var wire  1 (! rst $end
    $var wire 32 e wdata [31:0] $end
    $var wire  1 i we $end
   $upscope $end
   $scope module U_Switch $end
    $var wire 32 c addr [31:0] $end
    $var wire  1 )! clk $end
    $var wire 32 F! rdata [31:0] $end
    $var wire  1 (! rst $end
    $var wire 16 *! sw [15:0] $end
   $upscope $end
   $scope module U_Timer $end
    $var wire 32 c addr [31:0] $end
    $var wire  1 )! clk $end
    $var wire 32 > counter0 [31:0] $end
    $var wire 32 ? counter1 [31:0] $end
    $var wire 32 E! rdata [31:0] $end
    $var wire  1 (! rst $end
    $var wire 32 @ threshold [31:0] $end
    $var wire 32 e wdata [31:0] $end
    $var wire  1 h we $end
   $upscope $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#9
b00000000000100000000000000000000 #
b00000000000100000000000000000000 $
b10000000000000000000000000101100 %
b00000000000100000000000000000000 &
b00000000000100000000000000000000 '
b10000000000000000000000000101011 (
b00000000000000000000000000000000 )
b00000 *
0+
b00000000000000000000000000000000 ,
0-
b00000 .
b00000 /
b00000 0
b00000000000000000000 1
b00000000000000000000 2
b00000000000000000000 3
b00000000000000000000 4
b00000000000000000000 5
b00000000000000000000000000000000 6
b00000000000000000000000000000000 7
08
b000 9
b0000000000000000 :
b000000000000000000000000 ;
b0000 <
b0111111 =
b00000000000000000000000000000000 >
b00000000000000000000000000000000 ?
b00000000000000000000000000000000 @
b00000000000000000000000000000000 A
b00000000000000000000000000000000 B
b00000000000000000000000000000000 C
b00000000000000000000000000000000 D
b00000000000000000000000000000000 E
b00000000000000000000000000000000 F
b00000000000000000000000000000000 G
b00000000000000000000000000000000 H
b00000000000000000000000000000000 I
b00000000000000000000000000000000 J
b00000000000000000000000000000000 K
b00000000000000000000000000000000 L
b00000000000000000000000000000000 M
b00000000000000000000000000000000 N
b00000000000000000000000000000000 O
b00000000000000000000000000000000 P
b00000000000000000000000000000000 Q
b00000000000000000000000000000000 R
b00000000000000000000000000000000 S
b00000000000000000000000000000000 T
b00000000000000000000000000000000 U
b00000000000000000000000000000000 V
b00000000000000000000000000000000 W
b00000000000000000000000000000000 X
b00000000000000000000000000000000 Y
b00000000000000000000000000000000 Z
b00000000000000000000000000000000 [
b00000000000000000000000000000000 \
b00000000000000000000000000000000 ]
b00000000000000000000000000000000 ^
b00000000000000000000000000000000 _
b00000000000000000000000000000000 `
b0000000000000000 a
b00000000010000000000000001101111 b
b00000000000000000000000000000000 c
0d
b00000000000000000000000000000000 e
0f
0g
0h
0i
1j
0k
0l
0m
0n
0o
b100000 p
b00000000000000000000000000000000 q
b00000000000000000000000000000100 r
b00000000000000000000000000000100 s
b1101111 t
b00000 u
b00100 v
b00000 w
b000 x
b0000000 y
b00000000000000000000000000000000 z
1{
b10 |
b00000000000000000000000000000000 }
0~
b0000 !!
0"!
b00000000000000000000000000000100 #!
b100 $!
b01 %!
b0000000000000000 &!
b0000000000000000 '!
1(!
0)!
b0000000000000000 *!
b00000 +!
b00000001 ,!
1-!
1.!
1/!
10!
11!
12!
13!
14!
15!
16!
17!
18!
09!
0:!
0;!
0<!
b0000000000000000 =!
0>!
b00000000000000000000000000000000 ?!
0@!
b00000 A!
b00000000000000000000000000000000 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 E!
b00000000000000000000000000000000 F!
b00000000000000000000000000000000 G!
b00000000000000000000000000000100 H!
0I!
0J!
b00000000000000000000000000010000 K!
#10
b00000001011111010111100001000000 @
1)!
#15
0)!
#19
#20
1)!
#25
0)!
#29
#30
1)!
#35
0)!
#39
#40
1)!
#45
0)!
#49
#50
1)!
#55
0)!
#59
#60
1)!
#65
0)!
#69
#70
1)!
#75
0)!
#79
#80
1)!
#85
0)!
#89
#90
1)!
#95
0)!
#99
#100
1)!
#105
0)!
#109
#110
1)!
#115
0)!
#119
#120
1)!
#125
0)!
#129
#130
1)!
#135
0)!
#139
#140
1)!
#145
0)!
#149
#150
1)!
#155
0)!
#159
#160
1)!
#165
0)!
#169
#170
1)!
#175
0)!
#179
#180
1)!
#185
0)!
#189
#190
1)!
#195
0)!
#199
#200
1)!
#205
0)!
#209
0(!
#210
1+
b00000000000000000000000000000100 ,
1-
b0000000000000001 :
b000000000000000000000001 ;
b00000000000000000000000000000001 ?
b0000000000000100 a
b10000000000000000000000010110111 b
b00000000000000000000000000000100 q
b00000000000000000000000000001000 r
b00000000000000000000000000001000 s
b0110111 t
b00000 v
b00001 w
b1000000 y
b11 |
b10000000000000000000000000000000 #!
b011 $!
b00 %!
b0000000000000001 '!
1)!
1>!
1@!
b00000000000000000000000000000100 B!
b10000000000000000000000000000000 H!
#215
0)!
#219
#220
b00000000000000000000000000000100 )
b00001 *
b10000000000000000000000000000000 ,
b0000000000000010 :
b000000000000000000000010 ;
b00000000000000000000000000000010 ?
b10000000000000000000000000000000 B
b0000000000001000 a
b00000000000000000000000100010011 b
b00000000000000000000000000001000 q
b00000000000000000000000000001100 r
b00000000000000000000000000001100 s
b0010011 t
b00010 w
b0000000 y
b00 |
1"!
b00000000000000000000000000000000 #!
b000 $!
b0000000000000010 '!
1)!
b00000000000000000000000000000100 ?!
b00001 A!
b10000000000000000000000000000000 B!
b00000000000000000000000000000000 H!
#225
0)!
#229
#230
b00000000000000000000000000001000 )
b00010 *
b00000000000000000000000000000000 ,
b0000000000000011 :
b000000000000000000000011 ;
b00000000000000000000000000000011 ?
b0000000000001100 a
b01000000001000001101011100110011 b
b10000000000000000000000000000000 c
b00000000000000000000000000001100 q
b00000000000000000000000000010000 r
b00000000000000000000000000010000 s
b0110011 t
b00001 u
b00010 v
b01110 w
b101 x
b0100000 y
b10000000000000000000000000000000 z
b0111 !!
0"!
b00000000000000000000010000000010 #!
b0000000000000011 '!
1)!
b00000000000000000000000000001000 ?!
b00010 A!
b00000000000000000000000000000000 B!
b10000000000000000000000000000000 H!
#235
0)!
#239
#240
b00000000000000000000000000001100 )
b01110 *
b10000000000000000000000000000000 ,
b0000000000000100 :
b000000000000000000000100 ;
b00000000000000000000000000000100 ?
b10000000000000000000000000000000 O
b0000000000010000 a
b10000000000000000000001110110111 b
b00000000000000000000000000000000 c
b00000000000000000000000000010000 q
b00000000000000000000000000010100 r
b00000000000000000000000000010100 s
b0110111 t
b00000 u
b00000 v
b00111 w
b000 x
b1000000 y
b00000000000000000000000000000000 z
b11 |
b0000 !!
b10000000000000000000000000000000 #!
b011 $!
b0000000000000100 '!
1)!
b00000000000000000000000000001100 ?!
b01110 A!
b10000000000000000000000000000000 B!
#245
0)!
#249
#250
b00000000000000000000000000010000 )
b00111 *
b0000000000000101 :
b000000000000000000000101 ;
b00000000000000000000000000000101 ?
b10000000000000000000000000000000 H
b0000000000010100 a
b00000000001000000000000110010011 b
b00000000000000000000000000000010 c
b00000000000000000000000000010100 q
b00000000000000000000000000011000 r
b00000000000000000000000000011000 s
b0010011 t
b00010 v
b00011 w
b0000000 y
b00 |
b00000000000000000000000000000010 }
1"!
b00000000000000000000000000000010 #!
b000 $!
b0000000000000101 '!
1)!
b00000000000000000000000000010000 ?!
b00111 A!
b00000000000000000000000000000010 H!
#255
0)!
#259
#260
b00000000000000000000000000010100 )
b00011 *
b00000000000000000000000000000010 ,
b0000000000000110 :
b000000000000000000000110 ;
b00000000000000000000000000000110 ?
b00000000000000000000000000000010 D
b0000000000011000 a
b01011000011101110001010001100011 b
b00000000000000000000000000000000 c
b10000000000000000000000000000000 e
b00000000000000000000000000011000 q
b00000000000000000000000000011100 r
b00000000000000000000000000011100 s
b1100011 t
b01110 u
b00111 v
b01000 w
b001 x
b0101100 y
b10000000000000000000000000000000 z
0{
b10000000000000000000000000000000 }
1~
b1010 !!
0"!
b00000000000000000000010110001000 #!
b010 $!
b0000000000000110 '!
1)!
b00000000000000000000000000010100 ?!
b00011 A!
b00000000000000000000000000000010 B!
b00000000000000000000000000000000 H!
#265
0)!
#269
#270
b00000000000000000000000000011000 )
b01000 *
0+
b00000000000000000000000000000000 ,
b0000000000000111 :
b000000000000000000000111 ;
b00000000000000000000000000000111 ?
b0000000000011100 a
b10000000000000000000000010110111 b
b00000000000000000000000000000000 e
b00000000000000000000000000011100 q
b00000000000000000000000000100000 r
b00000000000000000000000000100000 s
b0110111 t
b00000 u
b00000 v
b00001 w
b000 x
b1000000 y
b00000000000000000000000000000000 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b10000000000000000000000000000000 #!
b011 $!
b0000000000000111 '!
1)!
b00000000000000000000000000011000 ?!
0@!
b01000 A!
b00000000000000000000000000000000 B!
b10000000000000000000000000000000 H!
#275
0)!
#279
#280
b00000000000000000000000000011100 )
b00001 *
1+
b10000000000000000000000000000000 ,
b0000000000001000 :
b000000000000000000001000 ;
b00000000000000000000000000001000 ?
b0000000000100000 a
b00000000000100000000000100010011 b
b00000000000000000000000000000001 c
b10000000000000000000000000000000 e
b00000000000000000000000000100000 q
b00000000000000000000000000100100 r
b00000000000000000000000000100100 s
b0010011 t
b00001 v
b00010 w
b0000000 y
b00 |
b00000000000000000000000000000001 }
1"!
b00000000000000000000000000000001 #!
b000 $!
b0000000000001000 '!
1)!
b00000000000000000000000000011100 ?!
1@!
b00001 A!
b10000000000000000000000000000000 B!
b00000000000000000000000000000001 H!
#285
0)!
#289
#290
b00000000000000000000000000100000 )
b00010 *
b00000000000000000000000000000001 ,
b0000000000001001 :
b000000000000000000001001 ;
b00000000000000000000000000001001 ?
b00000000000000000000000000000001 C
b0000000000100100 a
b01000000001000001101011100110011 b
b11000000000000000000000000000000 c
b00000000000000000000000000000001 e
b00000000000000000000000000100100 q
b00000000000000000000000000101000 r
b00000000000000000000000000101000 s
b0110011 t
b00001 u
b00010 v
b01110 w
b101 x
b0100000 y
b10000000000000000000000000000000 z
b0111 !!
0"!
b00000000000000000000010000000010 #!
b0000000000001001 '!
1)!
b00000000000000000000000000100000 ?!
b00010 A!
b00000000000000000000000000000001 B!
b11000000000000000000000000000000 H!
#295
0)!
#299
#300
b00000000000000000000000000100100 )
b01110 *
b11000000000000000000000000000000 ,
b0000000000001010 :
b000000000000000000001010 ;
b00000000000000000000000000001010 ?
b11000000000000000000000000000000 O
b0000000000101000 a
b11000000000000000000001110110111 b
b00000000000000000000000000000000 c
b00000000000000000000000000000000 e
b00000000000000000000000000101000 q
b00000000000000000000000000101100 r
b00000000000000000000000000101100 s
b0110111 t
b00000 u
b00000 v
b00111 w
b000 x
b1100000 y
b00000000000000000000000000000000 z
b11 |
b00000000000000000000000000000000 }
b0000 !!
b11000000000000000000000000000000 #!
b011 $!
b0000000000001010 '!
1)!
b00000000000000000000000000100100 ?!
b01110 A!
b11000000000000000000000000000000 B!
#305
0)!
#309
#310
b00000000000000000000000000101000 )
b00111 *
b0000000000001011 :
b000000000000000000001011 ;
b00000000000000000000000000001011 ?
b11000000000000000000000000000000 H
b0000000000101100 a
b00000000001100000000000110010011 b
b00000000000000000000000000000011 c
b00000000000000000000000000000010 e
b00000000000000000000000000101100 q
b00000000000000000000000000110000 r
b00000000000000000000000000110000 s
b0010011 t
b00011 v
b00011 w
b0000000 y
b00 |
b00000000000000000000000000000011 }
1"!
b00000000000000000000000000000011 #!
b000 $!
b0000000000001011 '!
1)!
b00000000000000000000000000101000 ?!
b00111 A!
b00000000000000000000000000000011 H!
#315
0)!
#319
#320
b00000000000000000000000000101100 )
b00011 *
b00000000000000000000000000000011 ,
b0000000000001100 :
b000000000000000000001100 ;
b00000000000000000000000000001100 ?
b00000000000000000000000000000011 D
b0000000000110000 a
b01010110011101110001100001100011 b
b00000000000000000000000000000000 c
b11000000000000000000000000000000 e
b00000000000000000000000000110000 q
b00000000000000000000000000110100 r
b00000000000000000000000000110100 s
b1100011 t
b01110 u
b00111 v
b10000 w
b001 x
b0101011 y
b11000000000000000000000000000000 z
0{
b11000000000000000000000000000000 }
1~
b1010 !!
0"!
b00000000000000000000010101110000 #!
b010 $!
b0000000000001100 '!
1)!
b00000000000000000000000000101100 ?!
b00011 A!
b00000000000000000000000000000011 B!
b00000000000000000000000000000000 H!
#325
0)!
#329
#330
b00000000000000000000000000110000 )
b10000 *
0+
b00000000000000000000000000000000 ,
b0000000000001101 :
b000000000000000000001101 ;
b00000000000000000000000000001101 ?
b0000000000110100 a
b10000000000000000000000010110111 b
b00000000000000000000000000000000 e
b00000000000000000000000000110100 q
b00000000000000000000000000111000 r
b00000000000000000000000000111000 s
b0110111 t
b00000 u
b00000 v
b00001 w
b000 x
b1000000 y
b00000000000000000000000000000000 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b10000000000000000000000000000000 #!
b011 $!
b0000000000001101 '!
1)!
b00000000000000000000000000110000 ?!
0@!
b10000 A!
b00000000000000000000000000000000 B!
b10000000000000000000000000000000 H!
#335
0)!
#339
#340
b00000000000000000000000000110100 )
b00001 *
1+
b10000000000000000000000000000000 ,
b0000000000001110 :
b000000000000000000001110 ;
b00000000000000000000000000001110 ?
b0000000000111000 a
b00000000011100000000000100010011 b
b00000000000000000000000000000111 c
b11000000000000000000000000000000 e
b00000000000000000000000000111000 q
b00000000000000000000000000111100 r
b00000000000000000000000000111100 s
b0010011 t
b00111 v
b00010 w
b0000000 y
b00 |
b00000000000000000000000000000111 }
1"!
b00000000000000000000000000000111 #!
b000 $!
b0000000000000001 &!
b0000000000001110 '!
1)!
b00000000000000000000000000110100 ?!
1@!
b00001 A!
b10000000000000000000000000000000 B!
b10000000000000000000000010110111 C!
b10000000000000000000000010110111 D!
b00000000000000000000000000000111 H!
#345
0)!
#349
#350
b00000000000000000000000000111000 )
b00010 *
b00000000000000000000000000000111 ,
b0000000000001111 :
b000000000000000000001111 ;
b00000000000000000000000000001111 ?
b00000000000000000000000000000111 C
b0000000000111100 a
b01000000001000001101011100110011 b
b11111111000000000000000000000000 c
b00000000000000000000000000000111 e
b00000000000000000000000000111100 q
b00000000000000000000000001000000 r
b00000000000000000000000001000000 s
b0110011 t
b00001 u
b00010 v
b01110 w
b101 x
b0100000 y
b10000000000000000000000000000000 z
b0111 !!
0"!
b00000000000000000000010000000010 #!
b0000000000000000 &!
b0000000000001111 '!
1)!
b00000000000000000000000000111000 ?!
b00010 A!
b00000000000000000000000000000111 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b11111111000000000000000000000000 H!
#355
0)!
#359
#360
b00000000000000000000000000111100 )
b01110 *
b11111111000000000000000000000000 ,
b0000000000010000 :
b000000000000000000010000 ;
b00000000000000000000000000010000 ?
b11111111000000000000000000000000 O
b0000000001000000 a
b11111111000000000000001110110111 b
b00000000000000000000000000000000 c
b00000000000000000000000000000000 e
b00000000000000000000000001000000 q
b00000000000000000000000001000100 r
b00000000000000000000000001000100 s
b0110111 t
b00000 u
b10000 v
b00111 w
b000 x
b1111111 y
b00000000000000000000000000000000 z
b11 |
b00000000000000000000000000000000 }
b0000 !!
b11111111000000000000000000000000 #!
b011 $!
b0000000000010000 '!
1)!
b00000000000000000000000000111100 ?!
b01110 A!
b11111111000000000000000000000000 B!
#365
0)!
#369
#370
b00000000000000000000000001000000 )
b00111 *
b0000000000010001 :
b000000000000000000010001 ;
b00000000000000000000000000010001 ?
b11111111000000000000000000000000 H
b0000000001000100 a
b00000000010000000000000110010011 b
b00000000000000000000000000000100 c
b00000000000000000000000001000100 q
b00000000000000000000000001001000 r
b00000000000000000000000001001000 s
b0010011 t
b00100 v
b00011 w
b0000000 y
b00 |
b00000000000000000000000000000100 }
1"!
b00000000000000000000000000000100 #!
b000 $!
b0000000000000001 &!
b0000000000010001 '!
1)!
b00000000000000000000000001000000 ?!
b00111 A!
b10000000000000000000000010110111 C!
b10000000000000000000000010110111 D!
b00000000000000000000000000000100 H!
#375
0)!
#379
#380
b00000000000000000000000001000100 )
b00011 *
b00000000000000000000000000000100 ,
b0000000000010010 :
b000000000000000000010010 ;
b00000000000000000000000000010010 ?
b00000000000000000000000000000100 D
b0000000001001000 a
b01010100011101110001110001100011 b
b00000000000000000000000000000000 c
b11111111000000000000000000000000 e
b00000000000000000000000001001000 q
b00000000000000000000000001001100 r
b00000000000000000000000001001100 s
b1100011 t
b01110 u
b00111 v
b11000 w
b001 x
b0101010 y
b11111111000000000000000000000000 z
0{
b11111111000000000000000000000000 }
1~
b1010 !!
0"!
b00000000000000000000010101011000 #!
b010 $!
b0000000000000000 &!
b0000000000010010 '!
1)!
b00000000000000000000000001000100 ?!
b00011 A!
b00000000000000000000000000000100 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#385
0)!
#389
#390
b00000000000000000000000001001000 )
b11000 *
0+
b00000000000000000000000000000000 ,
b0000000000010011 :
b000000000000000000010011 ;
b00000000000000000000000000010011 ?
b0000000001001100 a
b10000000000000000000000010110111 b
b00000000000000000000000000000000 e
b00000000000000000000000001001100 q
b00000000000000000000000001010000 r
b00000000000000000000000001010000 s
b0110111 t
b00000 u
b00000 v
b00001 w
b000 x
b1000000 y
b00000000000000000000000000000000 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b10000000000000000000000000000000 #!
b011 $!
b0000000000010011 '!
1)!
b00000000000000000000000001001000 ?!
0@!
b11000 A!
b00000000000000000000000000000000 B!
b10000000000000000000000000000000 H!
#395
0)!
#399
#400
b00000000000000000000000001001100 )
b00001 *
1+
b10000000000000000000000000000000 ,
b0000000000010100 :
b000000000000000000010100 ;
b00000000000000000000000000010100 ?
b0000000001010000 a
b00000000111000000000000100010011 b
b00000000000000000000000000001110 c
b11111111000000000000000000000000 e
b00000000000000000000000001010000 q
b00000000000000000000000001010100 r
b00000000000000000000000001010100 s
b0010011 t
b01110 v
b00010 w
b0000000 y
b00 |
b00000000000000000000000000001110 }
1"!
b00000000000000000000000000001110 #!
b000 $!
b0000000000000011 &!
b0000000000010100 '!
1)!
b00000000000000000000000001001100 ?!
1@!
b00001 A!
b10000000000000000000000000000000 B!
b01000000001000001101011100110011 C!
b01000000001000001101011100110011 D!
b00000000000000000000000000001110 H!
#405
0)!
#409
#410
b00000000000000000000000001010000 )
b00010 *
b00000000000000000000000000001110 ,
b0000000000010101 :
b000000000000000000010101 ;
b00000000000000000000000000010101 ?
b00000000000000000000000000001110 C
b0000000001010100 a
b01000000001000001101011100110011 b
b11111111111111100000000000000000 c
b00000000000000000000000000001110 e
b00000000000000000000000001010100 q
b00000000000000000000000001011000 r
b00000000000000000000000001011000 s
b0110011 t
b00001 u
b00010 v
b01110 w
b101 x
b0100000 y
b10000000000000000000000000000000 z
b0111 !!
0"!
b00000000000000000000010000000010 #!
b0000000000000000 &!
b0000000000010101 '!
1)!
b00000000000000000000000001010000 ?!
b00010 A!
b00000000000000000000000000001110 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b11111111111111100000000000000000 H!
#415
0)!
#419
#420
b00000000000000000000000001010100 )
b01110 *
b11111111111111100000000000000000 ,
b0000000000010110 :
b000000000000000000010110 ;
b00000000000000000000000000010110 ?
b11111111111111100000000000000000 O
b0000000001011000 a
b11111111111111100000001110110111 b
b00000000000000000000000000000000 c
b00000000000000000000000000000000 e
b00000000000000000000000001011000 q
b00000000000000000000000001011100 r
b00000000000000000000000001011100 s
b0110111 t
b11100 u
b11111 v
b00111 w
b000 x
b1111111 y
b00000000000000000000000000000000 z
b11 |
b00000000000000000000000000000000 }
b0000 !!
b11111111111111100000000000000000 #!
b011 $!
b0000000000010110 '!
1)!
b00000000000000000000000001010100 ?!
b01110 A!
b11111111111111100000000000000000 B!
#425
0)!
#429
#430
b00000000000000000000000001011000 )
b00111 *
b0000000000010111 :
b000000000000000000010111 ;
b00000000000000000000000000010111 ?
b11111111111111100000000000000000 H
b0000000001011100 a
b00000000010100000000000110010011 b
b00000000000000000000000000000101 c
b00000000000000000000000001011100 q
b00000000000000000000000001100000 r
b00000000000000000000000001100000 s
b0010011 t
b00000 u
b00101 v
b00011 w
b0000000 y
b00 |
b00000000000000000000000000000101 }
1"!
b00000000000000000000000000000101 #!
b000 $!
b0000000000000001 &!
b0000000000010111 '!
1)!
b00000000000000000000000001011000 ?!
b00111 A!
b10000000000000000000000010110111 C!
b10000000000000000000000010110111 D!
b00000000000000000000000000000101 H!
#435
0)!
#439
#440
b00000000000000000000000001011100 )
b00011 *
b00000000000000000000000000000101 ,
b0000000000011000 :
b000000000000000000011000 ;
b00000000000000000000000000011000 ?
b00000000000000000000000000000101 D
b0000000001100000 a
b01010100011101110001000001100011 b
b00000000000000000000000000000000 c
b11111111111111100000000000000000 e
b00000000000000000000000001100000 q
b00000000000000000000000001100100 r
b00000000000000000000000001100100 s
b1100011 t
b01110 u
b00111 v
b00000 w
b001 x
b0101010 y
b11111111111111100000000000000000 z
0{
b11111111111111100000000000000000 }
1~
b1010 !!
0"!
b00000000000000000000010101000000 #!
b010 $!
b0000000000000000 &!
b0000000000011000 '!
1)!
b00000000000000000000000001011100 ?!
b00011 A!
b00000000000000000000000000000101 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#445
0)!
#449
#450
b00000000000000000000000001100000 )
b00000 *
0+
b00000000000000000000000000000000 ,
b0000000000011001 :
b000000000000000000011001 ;
b00000000000000000000000000011001 ?
b0000000001100100 a
b10000000000000000000000010110111 b
b00000000000000000000000000000000 e
b00000000000000000000000001100100 q
b00000000000000000000000001101000 r
b00000000000000000000000001101000 s
b0110111 t
b00000 u
b00000 v
b00001 w
b000 x
b1000000 y
b00000000000000000000000000000000 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b10000000000000000000000000000000 #!
b011 $!
b0000000000011001 '!
1)!
b00000000000000000000000001100000 ?!
0@!
b00000 A!
b00000000000000000000000000000000 B!
b10000000000000000000000000000000 H!
#455
0)!
#459
#460
b00000000000000000000000001100100 )
b00001 *
1+
b10000000000000000000000000000000 ,
b0000000000011010 :
b000000000000000000011010 ;
b00000000000000000000000000011010 ?
b0000000001101000 a
b00000000000100001000000010010011 b
b10000000000000000000000000000001 c
b10000000000000000000000000000000 e
b00000000000000000000000001101000 q
b00000000000000000000000001101100 r
b00000000000000000000000001101100 s
b0010011 t
b00001 u
b00001 v
b0000000 y
b10000000000000000000000000000000 z
b00 |
b00000000000000000000000000000001 }
1"!
b00000000000000000000000000000001 #!
b000 $!
b0000000000011010 '!
1)!
b00000000000000000000000001100100 ?!
1@!
b00001 A!
b10000000000000000000000000000000 B!
b10000000000000000000000000000001 H!
#465
0)!
#469
#470
b00000000000000000000000001101000 )
b10000000000000000000000000000001 ,
b0000000000011011 :
b000000000000000000011011 ;
b00000000000000000000000000011011 ?
b10000000000000000000000000000001 B
b0000000001101100 a
b00000001111100000000000100010011 b
b00000000000000000000000000011111 c
b00000000000000000000000000000000 e
b00000000000000000000000001101100 q
b00000000000000000000000001110000 r
b00000000000000000000000001110000 s
b00000 u
b11111 v
b00010 w
b00000000000000000000000000000000 z
b00000000000000000000000000011111 }
b00000000000000000000000000011111 #!
b0000000000000111 &!
b0000000000011011 '!
1)!
b00000000000000000000000001101000 ?!
b10000000000000000000000000000001 B!
b10000000000000000000000010110111 C!
b10000000000000000000000010110111 D!
b00000000000000000000000000011111 H!
#475
0)!
#479
#480
b00000000000000000000000001101100 )
b00010 *
b00000000000000000000000000011111 ,
b0000000000011100 :
b000000000000000000011100 ;
b00000000000000000000000000011100 ?
b00000000000000000000000000011111 C
b0000000001110000 a
b01000000001000001101011100110011 b
b11111111111111111111111111111111 c
b00000000000000000000000000011111 e
0j
b000000 p
b00000000000000000000000001110000 q
b00000000000000000000000001110100 r
b00000000000000000000000001110100 s
b0110011 t
b00001 u
b00010 v
b01110 w
b101 x
b0100000 y
b10000000000000000000000000000001 z
b0111 !!
0"!
b00000000000000000000010000000010 #!
b0011111111111111 &!
b0000000000011100 '!
1)!
b00000000000000000000000001101100 ?!
b00010 A!
b00000000000000000000000000011111 B!
b11111111111111111111111111111111 C!
b00000000000000000000000000000000 D!
b11111111111111111111111111111111 H!
#485
0)!
#489
#490
b00000000000000000000000001110000 )
b01110 *
b11111111111111111111111111111111 ,
b0000000000011101 :
b000000000000000000011101 ;
b00000000000000000000000000011101 ?
b11111111111111111111111111111111 O
b0000000001110100 a
b11111111111100000000001110010011 b
b00000000000000000000000000000000 e
b00000000000000000000000001110100 q
b00000000000000000000000001111000 r
b00000000000000000000000001111000 s
b0010011 t
b00000 u
b11111 v
b00111 w
b000 x
b1111111 y
b00000000000000000000000000000000 z
b11111111111111111111111111111111 }
b0000 !!
1"!
b11111111111111111111111111111111 #!
b0000000000011101 '!
1)!
b00000000000000000000000001110000 ?!
b01110 A!
b11111111111111111111111111111111 B!
#495
0)!
#499
#500
b00000000000000000000000001110100 )
b00111 *
b0000000000011110 :
b000000000000000000011110 ;
b00000000000000000000000000011110 ?
b11111111111111111111111111111111 H
b0000000001111000 a
b00000000011000000000000110010011 b
b00000000000000000000000000000110 c
1j
b100000 p
b00000000000000000000000001111000 q
b00000000000000000000000001111100 r
b00000000000000000000000001111100 s
b00110 v
b00011 w
b0000000 y
b00000000000000000000000000000110 }
b00000000000000000000000000000110 #!
b0000000000000001 &!
b0000000000011110 '!
1)!
b00000000000000000000000001110100 ?!
b00111 A!
b10000000000000000000000010110111 C!
b10000000000000000000000010110111 D!
b00000000000000000000000000000110 H!
#505
0)!
#509
#510
b00000000000000000000000001111000 )
b00011 *
b00000000000000000000000000000110 ,
b0000000000011111 :
b000000000000000000011111 ;
b00000000000000000000000000011111 ?
b00000000000000000000000000000110 D
b0000000001111100 a
b01010010011101110001001001100011 b
b00000000000000000000000000000000 c
b11111111111111111111111111111111 e
b00000000000000000000000001111100 q
b00000000000000000000000010000000 r
b00000000000000000000000010000000 s
b1100011 t
b01110 u
b00111 v
b00100 w
b001 x
b0101001 y
b11111111111111111111111111111111 z
0{
b11111111111111111111111111111111 }
1~
b1010 !!
0"!
b00000000000000000000010100100100 #!
b010 $!
b0000000000000000 &!
b0000000000011111 '!
1)!
b00000000000000000000000001111000 ?!
b00011 A!
b00000000000000000000000000000110 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#515
0)!
#519
#520
b00000000000000000000000001111100 )
b00100 *
0+
b00000000000000000000000000000000 ,
b0000000000100000 :
b000000000000000000100000 ;
b00000000000000000000000000100000 ?
b0000000010000000 a
b10000000000000000000000010110111 b
b00000000000000000000000000000000 e
b00000000000000000000000010000000 q
b00000000000000000000000010000100 r
b00000000000000000000000010000100 s
b0110111 t
b00000 u
b00000 v
b00001 w
b000 x
b1000000 y
b00000000000000000000000000000000 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b10000000000000000000000000000000 #!
b011 $!
b0000000000100000 '!
1)!
b00000000000000000000000001111100 ?!
0@!
b00100 A!
b00000000000000000000000000000000 B!
b10000000000000000000000000000000 H!
#525
0)!
#529
#530
b00000000000000000000000010000000 )
b00001 *
1+
b10000000000000000000000000000000 ,
b0000000000100001 :
b000000000000000000100001 ;
b00000000000000000000000000100001 ?
b10000000000000000000000000000000 B
b0000000010000100 a
b11111111111100001000000010010011 b
b01111111111111111111111111111111 c
b00000000000000000000000010000100 q
b00000000000000000000000010001000 r
b00000000000000000000000010001000 s
b0010011 t
b00001 u
b11111 v
b1111111 y
b10000000000000000000000000000000 z
b00 |
b11111111111111111111111111111111 }
1"!
b11111111111111111111111111111111 #!
b000 $!
b0011111111111111 &!
b0000000000100001 '!
1)!
b00000000000000000000000010000000 ?!
1@!
b00001 A!
b10000000000000000000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b01111111111111111111111111111111 H!
#535
0)!
#539
#540
b00000000000000000000000010000100 )
b01111111111111111111111111111111 ,
b0000000000100010 :
b000000000000000000100010 ;
b00000000000000000000000000100010 ?
b01111111111111111111111111111111 B
b0000000010001000 a
b00000000000000000000000100010011 b
b00000000000000000000000000000000 c
b00000000000000000000000010001000 q
b00000000000000000000000010001100 r
b00000000000000000000000010001100 s
b00000 u
b00000 v
b00010 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000000000 }
b00000000000000000000000000000000 #!
b0000000000000000 &!
b0000000000100010 '!
1)!
b00000000000000000000000010000100 ?!
b01111111111111111111111111111111 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#545
0)!
#549
#550
b00000000000000000000000010001000 )
b00010 *
b00000000000000000000000000000000 ,
b0000000000100011 :
b000000000000000000100011 ;
b00000000000000000000000000100011 ?
b00000000000000000000000000000000 C
b0000000010001100 a
b01000000001000001101011100110011 b
b01111111111111111111111111111111 c
b00000000000000000000000010001100 q
b00000000000000000000000010010000 r
b00000000000000000000000010010000 s
b0110011 t
b00001 u
b00010 v
b01110 w
b101 x
b0100000 y
b01111111111111111111111111111111 z
b0111 !!
0"!
b00000000000000000000010000000010 #!
b0011111111111111 &!
b0000000000100011 '!
1)!
b00000000000000000000000010001000 ?!
b00010 A!
b00000000000000000000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b01111111111111111111111111111111 H!
#555
0)!
#559
#560
b00000000000000000000000010001100 )
b01110 *
b01111111111111111111111111111111 ,
b0000000000100100 :
b000000000000000000100100 ;
b00000000000000000000000000100100 ?
b01111111111111111111111111111111 O
b0000000010010000 a
b10000000000000000000001110110111 b
b00000000000000000000000000000000 c
b00000000000000000000000010010000 q
b00000000000000000000000010010100 r
b00000000000000000000000010010100 s
b0110111 t
b00000 u
b00000 v
b00111 w
b000 x
b1000000 y
b00000000000000000000000000000000 z
b11 |
b0000 !!
b10000000000000000000000000000000 #!
b011 $!
b0000000000000000 &!
b0000000000100100 '!
1)!
b00000000000000000000000010001100 ?!
b01110 A!
b01111111111111111111111111111111 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b10000000000000000000000000000000 H!
#565
0)!
#569
#570
b00000000000000000000000010010000 )
b00111 *
b10000000000000000000000000000000 ,
b0000000000100101 :
b000000000000000000100101 ;
b00000000000000000000000000100101 ?
b10000000000000000000000000000000 H
b0000000010010100 a
b11111111111100111000001110010011 b
b01111111111111111111111111111111 c
b00000000000000000000000010010100 q
b00000000000000000000000010011000 r
b00000000000000000000000010011000 s
b0010011 t
b00111 u
b11111 v
b1111111 y
b10000000000000000000000000000000 z
b00 |
b11111111111111111111111111111111 }
1"!
b11111111111111111111111111111111 #!
b000 $!
b0011111111111111 &!
b0000000000100101 '!
1)!
b00000000000000000000000010010000 ?!
b00111 A!
b10000000000000000000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b01111111111111111111111111111111 H!
#575
0)!
#579
#580
b00000000000000000000000010010100 )
b01111111111111111111111111111111 ,
b0000000000100110 :
b000000000000000000100110 ;
b00000000000000000000000000100110 ?
b01111111111111111111111111111111 H
b0000000010011000 a
b00000000011100000000000110010011 b
b00000000000000000000000000000111 c
b01111111111111111111111111111111 e
b00000000000000000000000010011000 q
b00000000000000000000000010011100 r
b00000000000000000000000010011100 s
b00000 u
b00111 v
b00011 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000000111 }
b00000000000000000000000000000111 #!
b0000000000000001 &!
b0000000000100110 '!
1)!
b00000000000000000000000010010100 ?!
b01111111111111111111111111111111 B!
b10000000000000000000000010110111 C!
b10000000000000000000000010110111 D!
b00000000000000000000000000000111 H!
#585
0)!
#589
#590
b00000000000000000000000010011000 )
b00011 *
b00000000000000000000000000000111 ,
b0000000000100111 :
b000000000000000000100111 ;
b00000000000000000000000000100111 ?
b00000000000000000000000000000111 D
b0000000010011100 a
b01010000011101110001001001100011 b
b00000000000000000000000000000000 c
b00000000000000000000000010011100 q
b00000000000000000000000010100000 r
b00000000000000000000000010100000 s
b1100011 t
b01110 u
b00100 w
b001 x
b0101000 y
b01111111111111111111111111111111 z
0{
b01111111111111111111111111111111 }
1~
b1010 !!
0"!
b00000000000000000000010100000100 #!
b010 $!
b0000000000000000 &!
b0000000000100111 '!
1)!
b00000000000000000000000010011000 ?!
b00011 A!
b00000000000000000000000000000111 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#595
0)!
#599
#600
b00000000000000000000000010011100 )
b00100 *
0+
b00000000000000000000000000000000 ,
b0000000000101000 :
b000000000000000000101000 ;
b00000000000000000000000000101000 ?
b0000000010100000 a
b10000000000000000000000010110111 b
b00000000000000000000000000000000 e
b00000000000000000000000010100000 q
b00000000000000000000000010100100 r
b00000000000000000000000010100100 s
b0110111 t
b00000 u
b00000 v
b00001 w
b000 x
b1000000 y
b00000000000000000000000000000000 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b10000000000000000000000000000000 #!
b011 $!
b0000000000101000 '!
1)!
b00000000000000000000000010011100 ?!
0@!
b00100 A!
b00000000000000000000000000000000 B!
b10000000000000000000000000000000 H!
#605
0)!
#609
#610
b00000000000000000000000010100000 )
b00001 *
1+
b10000000000000000000000000000000 ,
b0000000000101001 :
b000000000000000000101001 ;
b00000000000000000000000000101001 ?
b10000000000000000000000000000000 B
b0000000010100100 a
b11111111111100001000000010010011 b
b01111111111111111111111111111111 c
b00000000000000000000000010100100 q
b00000000000000000000000010101000 r
b00000000000000000000000010101000 s
b0010011 t
b00001 u
b11111 v
b1111111 y
b10000000000000000000000000000000 z
b00 |
b11111111111111111111111111111111 }
1"!
b11111111111111111111111111111111 #!
b000 $!
b0011111111111111 &!
b0000000000101001 '!
1)!
b00000000000000000000000010100000 ?!
1@!
b00001 A!
b10000000000000000000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b01111111111111111111111111111111 H!
#615
0)!
#619
#620
b00000000000000000000000010100100 )
b01111111111111111111111111111111 ,
b0000000000101010 :
b000000000000000000101010 ;
b00000000000000000000000000101010 ?
b01111111111111111111111111111111 B
b0000000010101000 a
b00000000000100000000000100010011 b
b00000000000000000000000000000001 c
b01111111111111111111111111111111 e
b00000000000000000000000010101000 q
b00000000000000000000000010101100 r
b00000000000000000000000010101100 s
b00000 u
b00001 v
b00010 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000000001 }
b00000000000000000000000000000001 #!
b0000000000000000 &!
b0000000000101010 '!
1)!
b00000000000000000000000010100100 ?!
b01111111111111111111111111111111 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000001 H!
#625
0)!
#629
#630
b00000000000000000000000010101000 )
b00010 *
b00000000000000000000000000000001 ,
b0000000000101011 :
b000000000000000000101011 ;
b00000000000000000000000000101011 ?
b00000000000000000000000000000001 C
b0000000010101100 a
b01000000001000001101011100110011 b
b00111111111111111111111111111111 c
b00000000000000000000000000000001 e
b00000000000000000000000010101100 q
b00000000000000000000000010110000 r
b00000000000000000000000010110000 s
b0110011 t
b00001 u
b00010 v
b01110 w
b101 x
b0100000 y
b01111111111111111111111111111111 z
b0111 !!
0"!
b00000000000000000000010000000010 #!
b0011111111111111 &!
b0000000000101011 '!
1)!
b00000000000000000000000010101000 ?!
b00010 A!
b00000000000000000000000000000001 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b00111111111111111111111111111111 H!
#635
0)!
#639
#640
b00000000000000000000000010101100 )
b01110 *
b00111111111111111111111111111111 ,
b0000000000101100 :
b000000000000000000101100 ;
b00000000000000000000000000101100 ?
b00111111111111111111111111111111 O
b0000000010110000 a
b01000000000000000000001110110111 b
b00000000000000000000000000000000 c
b00000000000000000000000000000000 e
b00000000000000000000000010110000 q
b00000000000000000000000010110100 r
b00000000000000000000000010110100 s
b0110111 t
b00000 u
b00000 v
b00111 w
b000 x
b00000000000000000000000000000000 z
b11 |
b00000000000000000000000000000000 }
b0000 !!
b01000000000000000000000000000000 #!
b011 $!
b0000000000000000 &!
b0000000000101100 '!
1)!
b00000000000000000000000010101100 ?!
b01110 A!
b00111111111111111111111111111111 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b01000000000000000000000000000000 H!
#645
0)!
#649
#650
b00000000000000000000000010110000 )
b00111 *
b01000000000000000000000000000000 ,
b0000000000101101 :
b000000000000000000101101 ;
b00000000000000000000000000101101 ?
b01000000000000000000000000000000 H
b0000000010110100 a
b11111111111100111000001110010011 b
b00111111111111111111111111111111 c
b00000000000000000000000010110100 q
b00000000000000000000000010111000 r
b00000000000000000000000010111000 s
b0010011 t
b00111 u
b11111 v
b1111111 y
b01000000000000000000000000000000 z
b00 |
b11111111111111111111111111111111 }
1"!
b11111111111111111111111111111111 #!
b000 $!
b0011111111111111 &!
b0000000000101101 '!
1)!
b00000000000000000000000010110000 ?!
b00111 A!
b01000000000000000000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b00111111111111111111111111111111 H!
#655
0)!
#659
#660
b00000000000000000000000010110100 )
b00111111111111111111111111111111 ,
b0000000000101110 :
b000000000000000000101110 ;
b00000000000000000000000000101110 ?
b00111111111111111111111111111111 H
b0000000010111000 a
b00000000100000000000000110010011 b
b00000000000000000000000000001000 c
b00000000000000000000000010111000 q
b00000000000000000000000010111100 r
b00000000000000000000000010111100 s
b00000 u
b01000 v
b00011 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000001000 }
b00000000000000000000000000001000 #!
b0000000000000010 &!
b0000000000101110 '!
1)!
b00000000000000000000000010110100 ?!
b00111111111111111111111111111111 B!
b00000000000000000000000100010011 C!
b00000000000000000000000100010011 D!
b00000000000000000000000000001000 H!
#665
0)!
#669
#670
b00000000000000000000000010111000 )
b00011 *
b00000000000000000000000000001000 ,
b0000000000101111 :
b000000000000000000101111 ;
b00000000000000000000000000101111 ?
b00000000000000000000000000001000 D
b0000000010111100 a
b01001110011101110001001001100011 b
b00000000000000000000000000000000 c
b00111111111111111111111111111111 e
b00000000000000000000000010111100 q
b00000000000000000000000011000000 r
b00000000000000000000000011000000 s
b1100011 t
b01110 u
b00111 v
b00100 w
b001 x
b0100111 y
b00111111111111111111111111111111 z
0{
b00111111111111111111111111111111 }
1~
b1010 !!
0"!
b00000000000000000000010011100100 #!
b010 $!
b0000000000000000 &!
b0000000000101111 '!
1)!
b00000000000000000000000010111000 ?!
b00011 A!
b00000000000000000000000000001000 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#675
0)!
#679
#680
b00000000000000000000000010111100 )
b00100 *
0+
b00000000000000000000000000000000 ,
b0000000000110000 :
b000000000000000000110000 ;
b00000000000000000000000000110000 ?
b0000000011000000 a
b10000000000000000000000010110111 b
b00000000000000000000000000000000 e
b00000000000000000000000011000000 q
b00000000000000000000000011000100 r
b00000000000000000000000011000100 s
b0110111 t
b00000 u
b00000 v
b00001 w
b000 x
b1000000 y
b00000000000000000000000000000000 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b10000000000000000000000000000000 #!
b011 $!
b0000000000110000 '!
1)!
b00000000000000000000000010111100 ?!
0@!
b00100 A!
b00000000000000000000000000000000 B!
b10000000000000000000000000000000 H!
#685
0)!
#689
#690
b00000000000000000000000011000000 )
b00001 *
1+
b10000000000000000000000000000000 ,
b0000000000110001 :
b000000000000000000110001 ;
b00000000000000000000000000110001 ?
b10000000000000000000000000000000 B
b0000000011000100 a
b11111111111100001000000010010011 b
b01111111111111111111111111111111 c
b00000000000000000000000011000100 q
b00000000000000000000000011001000 r
b00000000000000000000000011001000 s
b0010011 t
b00001 u
b11111 v
b1111111 y
b10000000000000000000000000000000 z
b00 |
b11111111111111111111111111111111 }
1"!
b11111111111111111111111111111111 #!
b000 $!
b0011111111111111 &!
b0000000000110001 '!
1)!
b00000000000000000000000011000000 ?!
1@!
b00001 A!
b10000000000000000000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b01111111111111111111111111111111 H!
#695
0)!
#699
#700
b00000000000000000000000011000100 )
b01111111111111111111111111111111 ,
b0000000000110010 :
b000000000000000000110010 ;
b00000000000000000000000000110010 ?
b01111111111111111111111111111111 B
b0000000011001000 a
b00000000011100000000000100010011 b
b00000000000000000000000000000111 c
b00111111111111111111111111111111 e
b00000000000000000000000011001000 q
b00000000000000000000000011001100 r
b00000000000000000000000011001100 s
b00000 u
b00111 v
b00010 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000000111 }
b00000000000000000000000000000111 #!
b0000000000000001 &!
b0000000000110010 '!
1)!
b00000000000000000000000011000100 ?!
b01111111111111111111111111111111 B!
b10000000000000000000000010110111 C!
b10000000000000000000000010110111 D!
b00000000000000000000000000000111 H!
#705
0)!
#709
#710
b00000000000000000000000011001000 )
b00010 *
b00000000000000000000000000000111 ,
b0000000000110011 :
b000000000000000000110011 ;
b00000000000000000000000000110011 ?
b00000000000000000000000000000111 C
b0000000011001100 a
b01000000001000001101011100110011 b
b00000000111111111111111111111111 c
b00000000000000000000000000000111 e
b00000000000000000000000011001100 q
b00000000000000000000000011010000 r
b00000000000000000000000011010000 s
b0110011 t
b00001 u
b00010 v
b01110 w
b101 x
b0100000 y
b01111111111111111111111111111111 z
b0111 !!
0"!
b00000000000000000000010000000010 #!
b0011111111111111 &!
b0000000000110011 '!
1)!
b00000000000000000000000011001000 ?!
b00010 A!
b00000000000000000000000000000111 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b00000000111111111111111111111111 H!
#715
0)!
#719
#720
b00000000000000000000000011001100 )
b01110 *
b00000000111111111111111111111111 ,
b0000000000110100 :
b000000000000000000110100 ;
b00000000000000000000000000110100 ?
b00000000111111111111111111111111 O
b0000000011010000 a
b00000001000000000000001110110111 b
b00000000000000000000000000000000 c
b00000000000000000000000000000000 e
b00000000000000000000000011010000 q
b00000000000000000000000011010100 r
b00000000000000000000000011010100 s
b0110111 t
b00000 u
b10000 v
b00111 w
b000 x
b0000000 y
b00000000000000000000000000000000 z
b11 |
b00000000000000000000000000000000 }
b0000 !!
b00000001000000000000000000000000 #!
b011 $!
b0000000000000000 &!
b0000000000110100 '!
1)!
b00000000000000000000000011001100 ?!
b01110 A!
b00000000111111111111111111111111 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000001000000000000000000000000 H!
#725
0)!
#729
#730
b00000000000000000000000011010000 )
b00111 *
b00000001000000000000000000000000 ,
b0000000000110101 :
b000000000000000000110101 ;
b00000000000000000000000000110101 ?
b00000001000000000000000000000000 H
b0000000011010100 a
b11111111111100111000001110010011 b
b00000000111111111111111111111111 c
b00000000000000000000000011010100 q
b00000000000000000000000011011000 r
b00000000000000000000000011011000 s
b0010011 t
b00111 u
b11111 v
b1111111 y
b00000001000000000000000000000000 z
b00 |
b11111111111111111111111111111111 }
1"!
b11111111111111111111111111111111 #!
b000 $!
b0011111111111111 &!
b0000000000110101 '!
1)!
b00000000000000000000000011010000 ?!
b00111 A!
b00000001000000000000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b00000000111111111111111111111111 H!
#735
0)!
#739
#740
b00000000000000000000000011010100 )
b00000000111111111111111111111111 ,
b0000000000110110 :
b000000000000000000110110 ;
b00000000000000000000000000110110 ?
b00000000111111111111111111111111 H
b0000000011011000 a
b00000000100100000000000110010011 b
b00000000000000000000000000001001 c
b00000000000000000000000011011000 q
b00000000000000000000000011011100 r
b00000000000000000000000011011100 s
b00000 u
b01001 v
b00011 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000001001 }
b00000000000000000000000000001001 #!
b0000000000000010 &!
b0000000000110110 '!
1)!
b00000000000000000000000011010100 ?!
b00000000111111111111111111111111 B!
b00000000000000000000000100010011 C!
b00000000000000000000000100010011 D!
b00000000000000000000000000001001 H!
#745
0)!
#749
#750
b00000000000000000000000011011000 )
b00011 *
b00000000000000000000000000001001 ,
b0000000000110111 :
b000000000000000000110111 ;
b00000000000000000000000000110111 ?
b00000000000000000000000000001001 D
b0000000011011100 a
b01001100011101110001001001100011 b
b00000000000000000000000000000000 c
b00000000111111111111111111111111 e
b00000000000000000000000011011100 q
b00000000000000000000000011100000 r
b00000000000000000000000011100000 s
b1100011 t
b01110 u
b00111 v
b00100 w
b001 x
b0100110 y
b00000000111111111111111111111111 z
0{
b00000000111111111111111111111111 }
1~
b1010 !!
0"!
b00000000000000000000010011000100 #!
b010 $!
b0000000000000000 &!
b0000000000110111 '!
1)!
b00000000000000000000000011011000 ?!
b00011 A!
b00000000000000000000000000001001 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#755
0)!
#759
#760
b00000000000000000000000011011100 )
b00100 *
0+
b00000000000000000000000000000000 ,
b0000000000111000 :
b000000000000000000111000 ;
b00000000000000000000000000111000 ?
b0000000011100000 a
b10000000000000000000000010110111 b
b00000000000000000000000000000000 e
b00000000000000000000000011100000 q
b00000000000000000000000011100100 r
b00000000000000000000000011100100 s
b0110111 t
b00000 u
b00000 v
b00001 w
b000 x
b1000000 y
b00000000000000000000000000000000 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b10000000000000000000000000000000 #!
b011 $!
b0000000000111000 '!
1)!
b00000000000000000000000011011100 ?!
0@!
b00100 A!
b00000000000000000000000000000000 B!
b10000000000000000000000000000000 H!
#765
0)!
#769
#770
b00000000000000000000000011100000 )
b00001 *
1+
b10000000000000000000000000000000 ,
b0000000000111001 :
b000000000000000000111001 ;
b00000000000000000000000000111001 ?
b10000000000000000000000000000000 B
b0000000011100100 a
b11111111111100001000000010010011 b
b01111111111111111111111111111111 c
b00000000000000000000000011100100 q
b00000000000000000000000011101000 r
b00000000000000000000000011101000 s
b0010011 t
b00001 u
b11111 v
b1111111 y
b10000000000000000000000000000000 z
b00 |
b11111111111111111111111111111111 }
1"!
b11111111111111111111111111111111 #!
b000 $!
b0011111111111111 &!
b0000000000111001 '!
1)!
b00000000000000000000000011100000 ?!
1@!
b00001 A!
b10000000000000000000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b01111111111111111111111111111111 H!
#775
0)!
#779
#780
b00000000000000000000000011100100 )
b01111111111111111111111111111111 ,
b0000000000111010 :
b000000000000000000111010 ;
b00000000000000000000000000111010 ?
b01111111111111111111111111111111 B
b0000000011101000 a
b00000000111000000000000100010011 b
b00000000000000000000000000001110 c
b00000000111111111111111111111111 e
b00000000000000000000000011101000 q
b00000000000000000000000011101100 r
b00000000000000000000000011101100 s
b00000 u
b01110 v
b00010 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000001110 }
b00000000000000000000000000001110 #!
b0000000000000011 &!
b0000000000111010 '!
1)!
b00000000000000000000000011100100 ?!
b01111111111111111111111111111111 B!
b01000000001000001101011100110011 C!
b01000000001000001101011100110011 D!
b00000000000000000000000000001110 H!
#785
0)!
#789
#790
b00000000000000000000000011101000 )
b00010 *
b00000000000000000000000000001110 ,
b0000000000111011 :
b000000000000000000111011 ;
b00000000000000000000000000111011 ?
b00000000000000000000000000001110 C
b0000000011101100 a
b01000000001000001101011100110011 b
b00000000000000011111111111111111 c
b00000000000000000000000000001110 e
b00000000000000000000000011101100 q
b00000000000000000000000011110000 r
b00000000000000000000000011110000 s
b0110011 t
b00001 u
b00010 v
b01110 w
b101 x
b0100000 y
b01111111111111111111111111111111 z
b0111 !!
0"!
b00000000000000000000010000000010 #!
b0011111111111111 &!
b0000000000111011 '!
1)!
b00000000000000000000000011101000 ?!
b00010 A!
b00000000000000000000000000001110 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b00000000000000011111111111111111 H!
#795
0)!
#799
#800
b00000000000000000000000011101100 )
b01110 *
b00000000000000011111111111111111 ,
b0000000000111100 :
b000000000000000000111100 ;
b00000000000000000000000000111100 ?
b00000000000000011111111111111111 O
b0000000011110000 a
b00000000000000100000001110110111 b
b00000000000000000000000000000000 c
b00000000000000000000000000000000 e
b00000000000000000000000011110000 q
b00000000000000000000000011110100 r
b00000000000000000000000011110100 s
b0110111 t
b00100 u
b00000 v
b00111 w
b000 x
b0000000 y
b00000000000000000000000000000000 z
b11 |
b00000000000000000000000000000000 }
b0000 !!
b00000000000000100000000000000000 #!
b011 $!
b0000000000000000 &!
b0000000000111100 '!
1)!
b00000000000000000000000011101100 ?!
b01110 A!
b00000000000000011111111111111111 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000100000000000000000 H!
#805
0)!
#809
#810
b00000000000000000000000011110000 )
b00111 *
b00000000000000100000000000000000 ,
b0000000000111101 :
b000000000000000000111101 ;
b00000000000000000000000000111101 ?
b00000000000000100000000000000000 H
b0000000011110100 a
b11111111111100111000001110010011 b
b00000000000000011111111111111111 c
b00000000000000000000000011110100 q
b00000000000000000000000011111000 r
b00000000000000000000000011111000 s
b0010011 t
b00111 u
b11111 v
b1111111 y
b00000000000000100000000000000000 z
b00 |
b11111111111111111111111111111111 }
1"!
b11111111111111111111111111111111 #!
b000 $!
b0011111111111111 &!
b0000000000111101 '!
1)!
b00000000000000000000000011110000 ?!
b00111 A!
b00000000000000100000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b00000000000000011111111111111111 H!
#815
0)!
#819
#820
b00000000000000000000000011110100 )
b00000000000000011111111111111111 ,
b0000000000111110 :
b000000000000000000111110 ;
b00000000000000000000000000111110 ?
b00000000000000011111111111111111 H
b0000000011111000 a
b00000000101000000000000110010011 b
b00000000000000000000000000001010 c
b00000000000000000000000011111000 q
b00000000000000000000000011111100 r
b00000000000000000000000011111100 s
b00000 u
b01010 v
b00011 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000001010 }
b00000000000000000000000000001010 #!
b0000000000000010 &!
b0000000000111110 '!
1)!
b00000000000000000000000011110100 ?!
b00000000000000011111111111111111 B!
b00000000000000000000000100010011 C!
b00000000000000000000000100010011 D!
b00000000000000000000000000001010 H!
#825
0)!
#829
#830
b00000000000000000000000011111000 )
b00011 *
b00000000000000000000000000001010 ,
b0000000000111111 :
b000000000000000000111111 ;
b00000000000000000000000000111111 ?
b00000000000000000000000000001010 D
b0000000011111100 a
b01001010011101110001001001100011 b
b00000000000000000000000000000000 c
b00000000000000011111111111111111 e
b00000000000000000000000011111100 q
b00000000000000000000000100000000 r
b00000000000000000000000100000000 s
b1100011 t
b01110 u
b00111 v
b00100 w
b001 x
b0100101 y
b00000000000000011111111111111111 z
0{
b00000000000000011111111111111111 }
1~
b1010 !!
0"!
b00000000000000000000010010100100 #!
b010 $!
b0000000000000000 &!
b0000000000111111 '!
1)!
b00000000000000000000000011111000 ?!
b00011 A!
b00000000000000000000000000001010 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#835
0)!
#839
#840
b00000000000000000000000011111100 )
b00100 *
0+
b00000000000000000000000000000000 ,
b0000000001000000 :
b000000000000000001000000 ;
b00000000000000000000000001000000 ?
b0000000100000000 a
b10000000000000000000000010110111 b
b00000000000000000000000000000000 e
b00000000000000000000000100000000 q
b00000000000000000000000100000100 r
b00000000000000000000000100000100 s
b0110111 t
b00000 u
b00000 v
b00001 w
b000 x
b1000000 y
b00000000000000000000000000000000 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b10000000000000000000000000000000 #!
b011 $!
b0000000001000000 '!
1)!
b00000000000000000000000011111100 ?!
0@!
b00100 A!
b00000000000000000000000000000000 B!
b10000000000000000000000000000000 H!
#845
0)!
#849
#850
b00000000000000000000000100000000 )
b00001 *
1+
b10000000000000000000000000000000 ,
b0000000001000001 :
b000000000000000001000001 ;
b00000000000000000000000001000001 ?
b10000000000000000000000000000000 B
b0000000100000100 a
b11111111111100001000000010010011 b
b01111111111111111111111111111111 c
b00000000000000000000000100000100 q
b00000000000000000000000100001000 r
b00000000000000000000000100001000 s
b0010011 t
b00001 u
b11111 v
b1111111 y
b10000000000000000000000000000000 z
b00 |
b11111111111111111111111111111111 }
1"!
b11111111111111111111111111111111 #!
b000 $!
b0011111111111111 &!
b0000000001000001 '!
1)!
b00000000000000000000000100000000 ?!
1@!
b00001 A!
b10000000000000000000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b01111111111111111111111111111111 H!
#855
0)!
#859
#860
b00000000000000000000000100000100 )
b01111111111111111111111111111111 ,
b0000000001000010 :
b000000000000000001000010 ;
b00000000000000000000000001000010 ?
b01111111111111111111111111111111 B
b0000000100001000 a
b00000001111100000000000100010011 b
b00000000000000000000000000011111 c
b00000000000000000000000100001000 q
b00000000000000000000000100001100 r
b00000000000000000000000100001100 s
b00000 u
b00010 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000011111 }
b00000000000000000000000000011111 #!
b0000000000000111 &!
b0000000001000010 '!
1)!
b00000000000000000000000100000100 ?!
b01111111111111111111111111111111 B!
b10000000000000000000000010110111 C!
b10000000000000000000000010110111 D!
b00000000000000000000000000011111 H!
#865
0)!
#869
#870
b00000000000000000000000100001000 )
b00010 *
b00000000000000000000000000011111 ,
b0000000001000011 :
b000000000000000001000011 ;
b00000000000000000000000001000011 ?
b00000000000000000000000000011111 C
b0000000100001100 a
b01000000001000001101011100110011 b
b00000000000000000000000000000000 c
b00000000000000000000000000011111 e
b00000000000000000000000100001100 q
b00000000000000000000000100010000 r
b00000000000000000000000100010000 s
b0110011 t
b00001 u
b00010 v
b01110 w
b101 x
b0100000 y
b01111111111111111111111111111111 z
b0111 !!
0"!
b00000000000000000000010000000010 #!
b0000000000000000 &!
b0000000001000011 '!
1)!
b00000000000000000000000100001000 ?!
b00010 A!
b00000000000000000000000000011111 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#875
0)!
#879
#880
b00000000000000000000000100001100 )
b01110 *
b00000000000000000000000000000000 ,
b0000000001000100 :
b000000000000000001000100 ;
b00000000000000000000000001000100 ?
b00000000000000000000000000000000 O
b0000000100010000 a
b00000000000000000000001110010011 b
b00000000000000000000000000000000 e
b00000000000000000000000100010000 q
b00000000000000000000000100010100 r
b00000000000000000000000100010100 s
b0010011 t
b00000 u
b00000 v
b00111 w
b000 x
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000000000 }
b0000 !!
1"!
b00000000000000000000000000000000 #!
b0000000001000100 '!
1)!
b00000000000000000000000100001100 ?!
b01110 A!
b00000000000000000000000000000000 B!
#885
0)!
#889
#890
b00000000000000000000000100010000 )
b00111 *
b0000000001000101 :
b000000000000000001000101 ;
b00000000000000000000000001000101 ?
b00000000000000000000000000000000 H
b0000000100010100 a
b00000000101100000000000110010011 b
b00000000000000000000000000001011 c
b00000000000000000000000100010100 q
b00000000000000000000000100011000 r
b00000000000000000000000100011000 s
b01011 v
b00011 w
b00000000000000000000000000001011 }
b00000000000000000000000000001011 #!
b0000000000000010 &!
b0000000001000101 '!
1)!
b00000000000000000000000100010000 ?!
b00111 A!
b00000000000000000000000100010011 C!
b00000000000000000000000100010011 D!
b00000000000000000000000000001011 H!
#895
0)!
#899
#900
b00000000000000000000000100010100 )
b00011 *
b00000000000000000000000000001011 ,
b0000000001000110 :
b000000000000000001000110 ;
b00000000000000000000000001000110 ?
b00000000000000000000000000001011 D
b0000000100011000 a
b01001000011101110001010001100011 b
b00000000000000000000000000000000 c
b00000000000000000000000100011000 q
b00000000000000000000000100011100 r
b00000000000000000000000100011100 s
b1100011 t
b01110 u
b00111 v
b01000 w
b001 x
b0100100 y
0{
b00000000000000000000000000000000 }
1~
b1010 !!
0"!
b00000000000000000000010010001000 #!
b010 $!
b0000000000000000 &!
b0000000001000110 '!
1)!
b00000000000000000000000100010100 ?!
b00011 A!
b00000000000000000000000000001011 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#905
0)!
#909
#910
b00000000000000000000000100011000 )
b01000 *
0+
b00000000000000000000000000000000 ,
b0000000001000111 :
b000000000000000001000111 ;
b00000000000000000000000001000111 ?
b0000000100011100 a
b10000001100000011000000010110111 b
b00000000000000000000000000001011 c
b00000000000000000000000100011100 q
b00000000000000000000000100100000 r
b00000000000000000000000100100000 s
b0110111 t
b00011 u
b11000 v
b00001 w
b000 x
b1000000 y
b00000000000000000000000000001011 z
1{
b11 |
0~
b0000 !!
b10000001100000011000000000000000 #!
b011 $!
b0000000000000010 &!
b0000000001000111 '!
1)!
b00000000000000000000000100011000 ?!
0@!
b01000 A!
b00000000000000000000000000000000 B!
b00000000000000000000000100010011 C!
b00000000000000000000000100010011 D!
b10000001100000011000000000000000 H!
#915
0)!
#919
#920
b00000000000000000000000100011100 )
b00001 *
1+
b10000001100000011000000000000000 ,
b0000000001001000 :
b000000000000000001001000 ;
b00000000000000000000000001001000 ?
b10000001100000011000000000000000 B
b0000000100100000 a
b00011000000100001000000010010011 b
b10000001100000011000000110000001 c
b10000001100000011000000000000000 e
b00000000000000000000000100100000 q
b00000000000000000000000100100100 r
b00000000000000000000000100100100 s
b0010011 t
b00001 u
b00001 v
b0001100 y
b10000001100000011000000000000000 z
b00 |
b00000000000000000000000110000001 }
1"!
b00000000000000000000000110000001 #!
b000 $!
b0010000001100000 &!
b0000000001001000 '!
1)!
b00000000000000000000000100011100 ?!
1@!
b00001 A!
b10000001100000011000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b10000001100000011000000110000001 H!
#925
0)!
#929
#930
b00000000000000000000000100100000 )
b10000001100000011000000110000001 ,
b0000000001001001 :
b000000000000000001001001 ;
b00000000000000000000000001001001 ?
b10000001100000011000000110000001 B
b0000000100100100 a
b00000000000000000000000100010011 b
b00000000000000000000000000000000 c
b00000000000000000000000000000000 e
b00000000000000000000000100100100 q
b00000000000000000000000100101000 r
b00000000000000000000000100101000 s
b00000 u
b00000 v
b00010 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000000000 }
b00000000000000000000000000000000 #!
b0000000000000000 &!
b0000000001001001 '!
1)!
b00000000000000000000000100100000 ?!
b10000001100000011000000110000001 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#935
0)!
#939
#940
b00000000000000000000000100100100 )
b00010 *
b00000000000000000000000000000000 ,
b0000000001001010 :
b000000000000000001001010 ;
b00000000000000000000000001001010 ?
b00000000000000000000000000000000 C
b0000000100101000 a
b01000000001000001101011100110011 b
b10000001100000011000000110000001 c
b00000000000000000000000100101000 q
b00000000000000000000000100101100 r
b00000000000000000000000100101100 s
b0110011 t
b00001 u
b00010 v
b01110 w
b101 x
b0100000 y
b10000001100000011000000110000001 z
b0111 !!
0"!
b00000000000000000000010000000010 #!
b0010000001100000 &!
b0000000001001010 '!
1)!
b00000000000000000000000100100100 ?!
b00010 A!
b00000000000000000000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b10000001100000011000000110000001 H!
#945
0)!
#949
#950
b00000000000000000000000100101000 )
b01110 *
b10000001100000011000000110000001 ,
b0000000001001011 :
b000000000000000001001011 ;
b00000000000000000000000001001011 ?
b10000001100000011000000110000001 O
b0000000100101100 a
b10000001100000011000001110110111 b
b00000000000000000000000000001011 c
b00000000000000000000000100101100 q
b00000000000000000000000100110000 r
b00000000000000000000000100110000 s
b0110111 t
b00011 u
b11000 v
b00111 w
b000 x
b1000000 y
b00000000000000000000000000001011 z
b11 |
b0000 !!
b10000001100000011000000000000000 #!
b011 $!
b0000000000000010 &!
b0000000001001011 '!
1)!
b00000000000000000000000100101000 ?!
b01110 A!
b10000001100000011000000110000001 B!
b00000000000000000000000100010011 C!
b00000000000000000000000100010011 D!
b10000001100000011000000000000000 H!
#955
0)!
#959
#960
b00000000000000000000000100101100 )
b00111 *
b10000001100000011000000000000000 ,
b0000000001001100 :
b000000000000000001001100 ;
b00000000000000000000000001001100 ?
b10000001100000011000000000000000 H
b0000000100110000 a
b00011000000100111000001110010011 b
b10000001100000011000000110000001 c
b10000001100000011000000110000001 e
b00000000000000000000000100110000 q
b00000000000000000000000100110100 r
b00000000000000000000000100110100 s
b0010011 t
b00111 u
b00001 v
b0001100 y
b10000001100000011000000000000000 z
b00 |
b00000000000000000000000110000001 }
1"!
b00000000000000000000000110000001 #!
b000 $!
b0010000001100000 &!
b0000000001001100 '!
1)!
b00000000000000000000000100101100 ?!
b00111 A!
b10000001100000011000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b10000001100000011000000110000001 H!
#965
0)!
#969
#970
b00000000000000000000000100110000 )
b10000001100000011000000110000001 ,
b0000000001001101 :
b000000000000000001001101 ;
b00000000000000000000000001001101 ?
b10000001100000011000000110000001 H
b0000000100110100 a
b00000000110000000000000110010011 b
b00000000000000000000000000001100 c
b00000000000000000000000000000000 e
b00000000000000000000000100110100 q
b00000000000000000000000100111000 r
b00000000000000000000000100111000 s
b00000 u
b01100 v
b00011 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000001100 }
b00000000000000000000000000001100 #!
b0000000000000011 &!
b0000000001001101 '!
1)!
b00000000000000000000000100110000 ?!
b10000001100000011000000110000001 B!
b01000000001000001101011100110011 C!
b01000000001000001101011100110011 D!
b00000000000000000000000000001100 H!
#975
0)!
#979
#980
b00000000000000000000000100110100 )
b00011 *
b00000000000000000000000000001100 ,
b0000000001001110 :
b000000000000000001001110 ;
b00000000000000000000000001001110 ?
b00000000000000000000000000001100 D
b0000000100111000 a
b01000110011101110001010001100011 b
b00000000000000000000000000000000 c
b10000001100000011000000110000001 e
b00000000000000000000000100111000 q
b00000000000000000000000100111100 r
b00000000000000000000000100111100 s
b1100011 t
b01110 u
b00111 v
b01000 w
b001 x
b0100011 y
b10000001100000011000000110000001 z
0{
b10000001100000011000000110000001 }
1~
b1010 !!
0"!
b00000000000000000000010001101000 #!
b010 $!
b0000000000000000 &!
b0000000001001110 '!
1)!
b00000000000000000000000100110100 ?!
b00011 A!
b00000000000000000000000000001100 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#985
0)!
#989
#990
b00000000000000000000000100111000 )
b01000 *
0+
b00000000000000000000000000000000 ,
b0000000001001111 :
b000000000000000001001111 ;
b00000000000000000000000001001111 ?
b0000000100111100 a
b10000001100000011000000010110111 b
b00000000000000000000000000001100 c
b00000000000000000000000000000000 e
b00000000000000000000000100111100 q
b00000000000000000000000101000000 r
b00000000000000000000000101000000 s
b0110111 t
b00011 u
b11000 v
b00001 w
b000 x
b1000000 y
b00000000000000000000000000001100 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b10000001100000011000000000000000 #!
b011 $!
b0000000000000011 &!
b0000000001001111 '!
1)!
b00000000000000000000000100111000 ?!
0@!
b01000 A!
b00000000000000000000000000000000 B!
b01000000001000001101011100110011 C!
b01000000001000001101011100110011 D!
b10000001100000011000000000000000 H!
#995
0)!
#999
#1000
b00000000000000000000000100111100 )
b00001 *
1+
b10000001100000011000000000000000 ,
b0000000001010000 :
b000000000000000001010000 ;
b00000000000000000000000001010000 ?
b10000001100000011000000000000000 B
b0000000101000000 a
b00011000000100001000000010010011 b
b10000001100000011000000110000001 c
b10000001100000011000000000000000 e
b00000000000000000000000101000000 q
b00000000000000000000000101000100 r
b00000000000000000000000101000100 s
b0010011 t
b00001 u
b00001 v
b0001100 y
b10000001100000011000000000000000 z
b00 |
b00000000000000000000000110000001 }
1"!
b00000000000000000000000110000001 #!
b000 $!
b0010000001100000 &!
b0000000001010000 '!
1)!
b00000000000000000000000100111100 ?!
1@!
b00001 A!
b10000001100000011000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b10000001100000011000000110000001 H!
#1005
0)!
#1009
#1010
b00000000000000000000000101000000 )
b10000001100000011000000110000001 ,
b0000000001010001 :
b000000000000000001010001 ;
b00000000000000000000000001010001 ?
b10000001100000011000000110000001 B
b0000000101000100 a
b00000000000100000000000100010011 b
b00000000000000000000000000000001 c
b10000001100000011000000110000001 e
b00000000000000000000000101000100 q
b00000000000000000000000101001000 r
b00000000000000000000000101001000 s
b00000 u
b00010 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000000001 }
b00000000000000000000000000000001 #!
b0000000000000000 &!
b0000000001010001 '!
1)!
b00000000000000000000000101000000 ?!
b10000001100000011000000110000001 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000001 H!
#1015
0)!
#1019
#1020
b00000000000000000000000101000100 )
b00010 *
b00000000000000000000000000000001 ,
b0000000001010010 :
b000000000000000001010010 ;
b00000000000000000000000001010010 ?
b00000000000000000000000000000001 C
b0000000101001000 a
b01000000001000001101011100110011 b
b11000000110000001100000011000000 c
b00000000000000000000000000000001 e
b00000000000000000000000101001000 q
b00000000000000000000000101001100 r
b00000000000000000000000101001100 s
b0110011 t
b00001 u
b00010 v
b01110 w
b101 x
b0100000 y
b10000001100000011000000110000001 z
b0111 !!
0"!
b00000000000000000000010000000010 #!
b0011000000110000 &!
b0000000001010010 '!
1)!
b00000000000000000000000101000100 ?!
b00010 A!
b00000000000000000000000000000001 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b11000000110000001100000011000000 H!
#1025
0)!
#1029
#1030
b00000000000000000000000101001000 )
b01110 *
b11000000110000001100000011000000 ,
b0000000001010011 :
b000000000000000001010011 ;
b00000000000000000000000001010011 ?
b11000000110000001100000011000000 O
b0000000101001100 a
b11000000110000001100001110110111 b
b10000001100000011000000110000001 c
b00000000000000000000000000000000 e
b00000000000000000000000101001100 q
b00000000000000000000000101010000 r
b00000000000000000000000101010000 s
b0110111 t
b01100 v
b00111 w
b100 x
b1100000 y
b11 |
b00000000000000000000000000000000 }
b0000 !!
b11000000110000001100000000000000 #!
b011 $!
b0010000001100000 &!
b0000000001010011 '!
1)!
b00000000000000000000000101001000 ?!
b01110 A!
b11000000110000001100000011000000 B!
b11000000110000001100000000000000 H!
#1035
0)!
#1039
#1040
b00000000000000000000000101001100 )
b00111 *
b11000000110000001100000000000000 ,
b0000000001010100 :
b000000000000000001010100 ;
b00000000000000000000000001010100 ?
b11000000110000001100000000000000 H
b0000000101010000 a
b00001100000000111000001110010011 b
b11000000110000001100000011000000 c
b00000000000000000000000101010000 q
b00000000000000000000000101010100 r
b00000000000000000000000101010100 s
b0010011 t
b00111 u
b00000 v
b000 x
b0000110 y
b11000000110000001100000000000000 z
b00 |
b00000000000000000000000011000000 }
1"!
b00000000000000000000000011000000 #!
b000 $!
b0011000000110000 &!
b0000000001010100 '!
1)!
b00000000000000000000000101001100 ?!
b00111 A!
b11000000110000001100000000000000 B!
b11000000110000001100000011000000 H!
#1045
0)!
#1049
#1050
b00000000000000000000000101010000 )
b11000000110000001100000011000000 ,
b0000000001010101 :
b000000000000000001010101 ;
b00000000000000000000000001010101 ?
b11000000110000001100000011000000 H
b0000000101010100 a
b00000000110100000000000110010011 b
b00000000000000000000000000001101 c
b00000000000000000000000101010100 q
b00000000000000000000000101011000 r
b00000000000000000000000101011000 s
b00000 u
b01101 v
b00011 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000001101 }
b00000000000000000000000000001101 #!
b0000000000000011 &!
b0000000001010101 '!
1)!
b00000000000000000000000101010000 ?!
b11000000110000001100000011000000 B!
b01000000001000001101011100110011 C!
b01000000001000001101011100110011 D!
b00000000000000000000000000001101 H!
#1055
0)!
#1059
#1060
b00000000000000000000000101010100 )
b00011 *
b00000000000000000000000000001101 ,
b0000000001010110 :
b000000000000000001010110 ;
b00000000000000000000000001010110 ?
b00000000000000000000000000001101 D
b0000000101011000 a
b01000100011101110001010001100011 b
b00000000000000000000000000000000 c
b11000000110000001100000011000000 e
b00000000000000000000000101011000 q
b00000000000000000000000101011100 r
b00000000000000000000000101011100 s
b1100011 t
b01110 u
b00111 v
b01000 w
b001 x
b0100010 y
b11000000110000001100000011000000 z
0{
b11000000110000001100000011000000 }
1~
b1010 !!
0"!
b00000000000000000000010001001000 #!
b010 $!
b0000000000000000 &!
b0000000001010110 '!
1)!
b00000000000000000000000101010100 ?!
b00011 A!
b00000000000000000000000000001101 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#1065
0)!
#1069
#1070
b00000000000000000000000101011000 )
b01000 *
0+
b00000000000000000000000000000000 ,
b0000000001010111 :
b000000000000000001010111 ;
b00000000000000000000000001010111 ?
b0000000101011100 a
b10000001100000011000000010110111 b
b00000000000000000000000000001101 c
b00000000000000000000000000000000 e
b00000000000000000000000101011100 q
b00000000000000000000000101100000 r
b00000000000000000000000101100000 s
b0110111 t
b00011 u
b11000 v
b00001 w
b000 x
b1000000 y
b00000000000000000000000000001101 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b10000001100000011000000000000000 #!
b011 $!
b0000000000000011 &!
b0000000001010111 '!
1)!
b00000000000000000000000101011000 ?!
0@!
b01000 A!
b00000000000000000000000000000000 B!
b01000000001000001101011100110011 C!
b01000000001000001101011100110011 D!
b10000001100000011000000000000000 H!
#1075
0)!
#1079
#1080
b00000000000000000000000101011100 )
b00001 *
1+
b10000001100000011000000000000000 ,
b0000000001011000 :
b000000000000000001011000 ;
b00000000000000000000000001011000 ?
b10000001100000011000000000000000 B
b0000000101100000 a
b00011000000100001000000010010011 b
b10000001100000011000000110000001 c
b10000001100000011000000000000000 e
b00000000000000000000000101100000 q
b00000000000000000000000101100100 r
b00000000000000000000000101100100 s
b0010011 t
b00001 u
b00001 v
b0001100 y
b10000001100000011000000000000000 z
b00 |
b00000000000000000000000110000001 }
1"!
b00000000000000000000000110000001 #!
b000 $!
b0010000001100000 &!
b0000000001011000 '!
1)!
b00000000000000000000000101011100 ?!
1@!
b00001 A!
b10000001100000011000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b10000001100000011000000110000001 H!
#1085
0)!
#1089
#1090
b00000000000000000000000101100000 )
b10000001100000011000000110000001 ,
b0000000001011001 :
b000000000000000001011001 ;
b00000000000000000000000001011001 ?
b10000001100000011000000110000001 B
b0000000101100100 a
b00000000011100000000000100010011 b
b00000000000000000000000000000111 c
b11000000110000001100000011000000 e
b00000000000000000000000101100100 q
b00000000000000000000000101101000 r
b00000000000000000000000101101000 s
b00000 u
b00111 v
b00010 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000000111 }
b00000000000000000000000000000111 #!
b0000000000000001 &!
b0000000001011001 '!
1)!
b00000000000000000000000101100000 ?!
b10000001100000011000000110000001 B!
b10000000000000000000000010110111 C!
b10000000000000000000000010110111 D!
b00000000000000000000000000000111 H!
#1095
0)!
#1099
#1100
b00000000000000000000000101100100 )
b00010 *
b00000000000000000000000000000111 ,
b0000000001011010 :
b000000000000000001011010 ;
b00000000000000000000000001011010 ?
b00000000000000000000000000000111 C
b0000000101101000 a
b01000000001000001101011100110011 b
b11111111000000110000001100000011 c
b00000000000000000000000000000111 e
b00000000000000000000000101101000 q
b00000000000000000000000101101100 r
b00000000000000000000000101101100 s
b0110011 t
b00001 u
b00010 v
b01110 w
b101 x
b0100000 y
b10000001100000011000000110000001 z
b0111 !!
0"!
b00000000000000000000010000000010 #!
b0000000011000000 &!
b0000000001011010 '!
1)!
b00000000000000000000000101100100 ?!
b00010 A!
b00000000000000000000000000000111 B!
b01000000001000001101011100110011 C!
b01000000001000001101011100110011 D!
b11111111000000110000001100000011 H!
#1105
0)!
#1109
#1110
b00000000000000000000000101101000 )
b01110 *
b11111111000000110000001100000011 ,
b0000000001011011 :
b000000000000000001011011 ;
b00000000000000000000000001011011 ?
b11111111000000110000001100000011 O
b0000000101101100 a
b11111111000000110000001110110111 b
b00000000000000000000000000000000 c
b00000000000000000000000000000000 e
b00000000000000000000000101101100 q
b00000000000000000000000101110000 r
b00000000000000000000000101110000 s
b0110111 t
b00110 u
b10000 v
b00111 w
b000 x
b1111111 y
b00000000000000000000000000000000 z
b11 |
b00000000000000000000000000000000 }
b0000 !!
b11111111000000110000000000000000 #!
b011 $!
b0000000000000000 &!
b0000000001011011 '!
1)!
b00000000000000000000000101101000 ?!
b01110 A!
b11111111000000110000001100000011 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b11111111000000110000000000000000 H!
#1115
0)!
#1119
#1120
b00000000000000000000000101101100 )
b00111 *
b11111111000000110000000000000000 ,
b0000000001011100 :
b000000000000000001011100 ;
b00000000000000000000000001011100 ?
b11111111000000110000000000000000 H
b0000000101110000 a
b00110000001100111000001110010011 b
b11111111000000110000001100000011 c
b00000000000000000000000000001101 e
b00000000000000000000000101110000 q
b00000000000000000000000101110100 r
b00000000000000000000000101110100 s
b0010011 t
b00111 u
b00011 v
b0011000 y
b11111111000000110000000000000000 z
b00 |
b00000000000000000000001100000011 }
1"!
b00000000000000000000001100000011 #!
b000 $!
b0000000011000000 &!
b0000000001011100 '!
1)!
b00000000000000000000000101101100 ?!
b00111 A!
b11111111000000110000000000000000 B!
b01000000001000001101011100110011 C!
b01000000001000001101011100110011 D!
b11111111000000110000001100000011 H!
#1125
0)!
#1129
#1130
b00000000000000000000000101110000 )
b11111111000000110000001100000011 ,
b0000000001011101 :
b000000000000000001011101 ;
b00000000000000000000000001011101 ?
b11111111000000110000001100000011 H
b0000000101110100 a
b00000000111000000000000110010011 b
b00000000000000000000000000001110 c
b11111111000000110000001100000011 e
b00000000000000000000000101110100 q
b00000000000000000000000101111000 r
b00000000000000000000000101111000 s
b00000 u
b01110 v
b00011 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000001110 }
b00000000000000000000000000001110 #!
b0000000000000011 &!
b0000000001011101 '!
1)!
b00000000000000000000000101110000 ?!
b11111111000000110000001100000011 B!
b00000000000000000000000000001110 H!
#1135
0)!
#1139
#1140
b00000000000000000000000101110100 )
b00011 *
b00000000000000000000000000001110 ,
b0000000001011110 :
b000000000000000001011110 ;
b00000000000000000000000001011110 ?
b00000000000000000000000000001110 D
b0000000101111000 a
b01000010011101110001010001100011 b
b00000000000000000000000000000000 c
b00000000000000000000000101111000 q
b00000000000000000000000101111100 r
b00000000000000000000000101111100 s
b1100011 t
b01110 u
b00111 v
b01000 w
b001 x
b0100001 y
b11111111000000110000001100000011 z
0{
b11111111000000110000001100000011 }
1~
b1010 !!
0"!
b00000000000000000000010000101000 #!
b010 $!
b0000000000000000 &!
b0000000001011110 '!
1)!
b00000000000000000000000101110100 ?!
b00011 A!
b00000000000000000000000000001110 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#1145
0)!
#1149
#1150
b00000000000000000000000101111000 )
b01000 *
0+
b00000000000000000000000000000000 ,
b0000000001011111 :
b000000000000000001011111 ;
b00000000000000000000000001011111 ?
b0000000101111100 a
b10000001100000011000000010110111 b
b00000000000000000000000000001110 c
b00000000000000000000000000000000 e
b00000000000000000000000101111100 q
b00000000000000000000000110000000 r
b00000000000000000000000110000000 s
b0110111 t
b00011 u
b11000 v
b00001 w
b000 x
b1000000 y
b00000000000000000000000000001110 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b10000001100000011000000000000000 #!
b011 $!
b0000000000000011 &!
b0000000001011111 '!
1)!
b00000000000000000000000101111000 ?!
0@!
b01000 A!
b00000000000000000000000000000000 B!
b01000000001000001101011100110011 C!
b01000000001000001101011100110011 D!
b10000001100000011000000000000000 H!
#1155
0)!
#1159
#1160
b00000000000000000000000101111100 )
b00001 *
1+
b10000001100000011000000000000000 ,
b0000000001100000 :
b000000000000000001100000 ;
b00000000000000000000000001100000 ?
b10000001100000011000000000000000 B
b0000000110000000 a
b00011000000100001000000010010011 b
b10000001100000011000000110000001 c
b10000001100000011000000000000000 e
b00000000000000000000000110000000 q
b00000000000000000000000110000100 r
b00000000000000000000000110000100 s
b0010011 t
b00001 u
b00001 v
b0001100 y
b10000001100000011000000000000000 z
b00 |
b00000000000000000000000110000001 }
1"!
b00000000000000000000000110000001 #!
b000 $!
b0010000001100000 &!
b0000000001100000 '!
1)!
b00000000000000000000000101111100 ?!
1@!
b00001 A!
b10000001100000011000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b10000001100000011000000110000001 H!
#1165
0)!
#1169
#1170
b00000000000000000000000110000000 )
b10000001100000011000000110000001 ,
b0000000001100001 :
b000000000000000001100001 ;
b00000000000000000000000001100001 ?
b10000001100000011000000110000001 B
b0000000110000100 a
b00000000111000000000000100010011 b
b00000000000000000000000000001110 c
b11111111000000110000001100000011 e
b00000000000000000000000110000100 q
b00000000000000000000000110001000 r
b00000000000000000000000110001000 s
b00000 u
b01110 v
b00010 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000001110 }
b00000000000000000000000000001110 #!
b0000000000000011 &!
b0000000001100001 '!
1)!
b00000000000000000000000110000000 ?!
b10000001100000011000000110000001 B!
b01000000001000001101011100110011 C!
b01000000001000001101011100110011 D!
b00000000000000000000000000001110 H!
#1175
0)!
#1179
#1180
b00000000000000000000000110000100 )
b00010 *
b00000000000000000000000000001110 ,
b0000000001100010 :
b000000000000000001100010 ;
b00000000000000000000000001100010 ?
b00000000000000000000000000001110 C
b0000000110001000 a
b01000000001000001101011100110011 b
b11111111111111100000011000000110 c
b00000000000000000000000000001110 e
b00000000000000000000000110001000 q
b00000000000000000000000110001100 r
b00000000000000000000000110001100 s
b0110011 t
b00001 u
b00010 v
b01110 w
b101 x
b0100000 y
b10000001100000011000000110000001 z
b0111 !!
0"!
b00000000000000000000010000000010 #!
b0000000110000001 &!
b0000000001100010 '!
1)!
b00000000000000000000000110000100 ?!
b00010 A!
b00000000000000000000000000001110 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b11111111111111100000011000000110 H!
#1185
0)!
#1189
#1190
b00000000000000000000000110001000 )
b01110 *
b11111111111111100000011000000110 ,
b0000000001100011 :
b000000000000000001100011 ;
b00000000000000000000000001100011 ?
b11111111111111100000011000000110 O
b0000000110001100 a
b11111111111111100000001110110111 b
b00000000000000000000000000000000 c
b00000000000000000000000000000000 e
b00000000000000000000000110001100 q
b00000000000000000000000110010000 r
b00000000000000000000000110010000 s
b0110111 t
b11100 u
b11111 v
b00111 w
b000 x
b1111111 y
b00000000000000000000000000000000 z
b11 |
b00000000000000000000000000000000 }
b0000 !!
b11111111111111100000000000000000 #!
b011 $!
b0000000000000000 &!
b0000000001100011 '!
1)!
b00000000000000000000000110001000 ?!
b01110 A!
b11111111111111100000011000000110 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b11111111111111100000000000000000 H!
#1195
0)!
#1199
#1200
b00000000000000000000000110001100 )
b00111 *
b11111111111111100000000000000000 ,
b0000000001100100 :
b000000000000000001100100 ;
b00000000000000000000000001100100 ?
b11111111111111100000000000000000 H
b0000000110010000 a
b01100000011000111000001110010011 b
b11111111111111100000011000000110 c
b00000000000000000000000110010000 q
b00000000000000000000000110010100 r
b00000000000000000000000110010100 s
b0010011 t
b00111 u
b00110 v
b0110000 y
b11111111111111100000000000000000 z
b00 |
b00000000000000000000011000000110 }
1"!
b00000000000000000000011000000110 #!
b000 $!
b0000000110000001 &!
b0000000001100100 '!
1)!
b00000000000000000000000110001100 ?!
b00111 A!
b11111111111111100000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b11111111111111100000011000000110 H!
#1205
0)!
#1209
#1210
b00000000000000000000000110010000 )
b11111111111111100000011000000110 ,
b0000000001100101 :
b000000000000000001100101 ;
b00000000000000000000000001100101 ?
b11111111111111100000011000000110 H
b0000000110010100 a
b00000000111100000000000110010011 b
b00000000000000000000000000001111 c
b00000000000000000000000110010100 q
b00000000000000000000000110011000 r
b00000000000000000000000110011000 s
b00000 u
b01111 v
b00011 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000001111 }
b00000000000000000000000000001111 #!
b0000000000000011 &!
b0000000001100101 '!
1)!
b00000000000000000000000110010000 ?!
b11111111111111100000011000000110 B!
b01000000001000001101011100110011 C!
b01000000001000001101011100110011 D!
b00000000000000000000000000001111 H!
#1215
0)!
#1219
#1220
b00000000000000000000000110010100 )
b00011 *
b00000000000000000000000000001111 ,
b0000000001100110 :
b000000000000000001100110 ;
b00000000000000000000000001100110 ?
b00000000000000000000000000001111 D
b0000000110011000 a
b01000000011101110001010001100011 b
b00000000000000000000000000000000 c
b11111111111111100000011000000110 e
b00000000000000000000000110011000 q
b00000000000000000000000110011100 r
b00000000000000000000000110011100 s
b1100011 t
b01110 u
b00111 v
b01000 w
b001 x
b0100000 y
b11111111111111100000011000000110 z
0{
b11111111111111100000011000000110 }
1~
b1010 !!
0"!
b00000000000000000000010000001000 #!
b010 $!
b0000000000000000 &!
b0000000001100110 '!
1)!
b00000000000000000000000110010100 ?!
b00011 A!
b00000000000000000000000000001111 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#1225
0)!
#1229
#1230
b00000000000000000000000110011000 )
b01000 *
0+
b00000000000000000000000000000000 ,
b0000000001100111 :
b000000000000000001100111 ;
b00000000000000000000000001100111 ?
b0000000110011100 a
b10000001100000011000000010110111 b
b00000000000000000000000000001111 c
b00000000000000000000000000000000 e
b00000000000000000000000110011100 q
b00000000000000000000000110100000 r
b00000000000000000000000110100000 s
b0110111 t
b00011 u
b11000 v
b00001 w
b000 x
b1000000 y
b00000000000000000000000000001111 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b10000001100000011000000000000000 #!
b011 $!
b0000000000000011 &!
b0000000001100111 '!
1)!
b00000000000000000000000110011000 ?!
0@!
b01000 A!
b00000000000000000000000000000000 B!
b01000000001000001101011100110011 C!
b01000000001000001101011100110011 D!
b10000001100000011000000000000000 H!
#1235
0)!
#1239
#1240
b00000000000000000000000110011100 )
b00001 *
1+
b10000001100000011000000000000000 ,
b0000000001101000 :
b000000000000000001101000 ;
b00000000000000000000000001101000 ?
b10000001100000011000000000000000 B
b0000000110100000 a
b00011000000100001000000010010011 b
b10000001100000011000000110000001 c
b10000001100000011000000000000000 e
b00000000000000000000000110100000 q
b00000000000000000000000110100100 r
b00000000000000000000000110100100 s
b0010011 t
b00001 u
b00001 v
b0001100 y
b10000001100000011000000000000000 z
b00 |
b00000000000000000000000110000001 }
1"!
b00000000000000000000000110000001 #!
b000 $!
b0010000001100000 &!
b0000000001101000 '!
1)!
b00000000000000000000000110011100 ?!
1@!
b00001 A!
b10000001100000011000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b10000001100000011000000110000001 H!
#1245
0)!
#1249
#1250
b00000000000000000000000110100000 )
b10000001100000011000000110000001 ,
b0000000001101001 :
b000000000000000001101001 ;
b00000000000000000000000001101001 ?
b10000001100000011000000110000001 B
b0000000110100100 a
b00000001111100000000000100010011 b
b00000000000000000000000000011111 c
b00000000000000000000000000000000 e
b00000000000000000000000110100100 q
b00000000000000000000000110101000 r
b00000000000000000000000110101000 s
b00000 u
b11111 v
b00010 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000011111 }
b00000000000000000000000000011111 #!
b0000000000000111 &!
b0000000001101001 '!
1)!
b00000000000000000000000110100000 ?!
b10000001100000011000000110000001 B!
b10000000000000000000000010110111 C!
b10000000000000000000000010110111 D!
b00000000000000000000000000011111 H!
#1255
0)!
#1259
#1260
b00000000000000000000000110100100 )
b00010 *
b00000000000000000000000000011111 ,
b0000000001101010 :
b000000000000000001101010 ;
b00000000000000000000000001101010 ?
b00000000000000000000000000011111 C
b0000000110101000 a
b01000000001000001101011100110011 b
b11111111111111111111111111111111 c
b00000000000000000000000000011111 e
0j
b000000 p
b00000000000000000000000110101000 q
b00000000000000000000000110101100 r
b00000000000000000000000110101100 s
b0110011 t
b00001 u
b00010 v
b01110 w
b101 x
b0100000 y
b10000001100000011000000110000001 z
b0111 !!
0"!
b00000000000000000000010000000010 #!
b0011111111111111 &!
b0000000001101010 '!
1)!
b00000000000000000000000110100100 ?!
b00010 A!
b00000000000000000000000000011111 B!
b11111111111111111111111111111111 C!
b00000000000000000000000000000000 D!
b11111111111111111111111111111111 H!
#1265
0)!
#1269
#1270
b00000000000000000000000110101000 )
b01110 *
b11111111111111111111111111111111 ,
b0000000001101011 :
b000000000000000001101011 ;
b00000000000000000000000001101011 ?
b11111111111111111111111111111111 O
b0000000110101100 a
b11111111111100000000001110010011 b
b00000000000000000000000000000000 e
b00000000000000000000000110101100 q
b00000000000000000000000110110000 r
b00000000000000000000000110110000 s
b0010011 t
b00000 u
b11111 v
b00111 w
b000 x
b1111111 y
b00000000000000000000000000000000 z
b11111111111111111111111111111111 }
b0000 !!
1"!
b11111111111111111111111111111111 #!
b0000000001101011 '!
1)!
b00000000000000000000000110101000 ?!
b01110 A!
b11111111111111111111111111111111 B!
#1275
0)!
#1279
#1280
b00000000000000000000000110101100 )
b00111 *
b0000000001101100 :
b000000000000000001101100 ;
b00000000000000000000000001101100 ?
b11111111111111111111111111111111 H
b0000000110110000 a
b00000001000000000000000110010011 b
b00000000000000000000000000010000 c
1j
b100000 p
b00000000000000000000000110110000 q
b00000000000000000000000110110100 r
b00000000000000000000000110110100 s
b10000 v
b00011 w
b0000000 y
b00000000000000000000000000010000 }
b00000000000000000000000000010000 #!
b0000000000000100 &!
b0000000001101100 '!
1)!
b00000000000000000000000110101100 ?!
b00111 A!
b10000000000000000000001110110111 C!
b10000000000000000000001110110111 D!
b00000000000000000000000000010000 H!
#1285
0)!
#1289
#1290
b00000000000000000000000110110000 )
b00011 *
b00000000000000000000000000010000 ,
b0000000001101101 :
b000000000000000001101101 ;
b00000000000000000000000001101101 ?
b00000000000000000000000000010000 D
b0000000110110100 a
b00111110011101110001011001100011 b
b00000000000000000000000000000000 c
b11111111111111111111111111111111 e
b00000000000000000000000110110100 q
b00000000000000000000000110111000 r
b00000000000000000000000110111000 s
b1100011 t
b01110 u
b00111 v
b01100 w
b001 x
b0011111 y
b11111111111111111111111111111111 z
0{
b11111111111111111111111111111111 }
1~
b1010 !!
0"!
b00000000000000000000001111101100 #!
b010 $!
b0000000000000000 &!
b0000000001101101 '!
1)!
b00000000000000000000000110110000 ?!
b00011 A!
b00000000000000000000000000010000 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#1295
0)!
#1299
#1300
b00000000000000000000000110110100 )
b01100 *
0+
b00000000000000000000000000000000 ,
b0000000001101110 :
b000000000000000001101110 ;
b00000000000000000000000001101110 ?
b0000000110111000 a
b10000001100000011000000010110111 b
b00000000000000000000000000010000 c
b00000000000000000000000000000000 e
b00000000000000000000000110111000 q
b00000000000000000000000110111100 r
b00000000000000000000000110111100 s
b0110111 t
b00011 u
b11000 v
b00001 w
b000 x
b1000000 y
b00000000000000000000000000010000 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b10000001100000011000000000000000 #!
b011 $!
b0000000000000100 &!
b0000000001101110 '!
1)!
b00000000000000000000000110110100 ?!
0@!
b01100 A!
b00000000000000000000000000000000 B!
b10000000000000000000001110110111 C!
b10000000000000000000001110110111 D!
b10000001100000011000000000000000 H!
#1305
0)!
#1309
#1310
b00000000000000000000000110111000 )
b00001 *
1+
b10000001100000011000000000000000 ,
b0000000001101111 :
b000000000000000001101111 ;
b00000000000000000000000001101111 ?
b10000001100000011000000000000000 B
b0000000110111100 a
b00011000000100001000000010010011 b
b10000001100000011000000110000001 c
b10000001100000011000000000000000 e
b00000000000000000000000110111100 q
b00000000000000000000000111000000 r
b00000000000000000000000111000000 s
b0010011 t
b00001 u
b00001 v
b0001100 y
b10000001100000011000000000000000 z
b00 |
b00000000000000000000000110000001 }
1"!
b00000000000000000000000110000001 #!
b000 $!
b0010000001100000 &!
b0000000001101111 '!
1)!
b00000000000000000000000110111000 ?!
1@!
b00001 A!
b10000001100000011000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b10000001100000011000000110000001 H!
#1315
0)!
#1319
#1320
b00000000000000000000000110111100 )
b10000001100000011000000110000001 ,
b0000000001110000 :
b000000000000000001110000 ;
b00000000000000000000000001110000 ?
b10000001100000011000000110000001 B
b0000000111000000 a
b11111100000000000000000100010011 b
b11111111111111111111111111000000 c
b00000000000000000000000000000000 e
0j
b000000 p
b00000000000000000000000111000000 q
b00000000000000000000000111000100 r
b00000000000000000000000111000100 s
b00000 u
b00000 v
b00010 w
b1111110 y
b00000000000000000000000000000000 z
b11111111111111111111111111000000 }
b11111111111111111111111111000000 #!
b0011111111110000 &!
b0000000001110000 '!
1)!
b00000000000000000000000110111100 ?!
b10000001100000011000000110000001 B!
b11111111111111111111111111111111 C!
b11111111111111111111111111000000 H!
#1325
0)!
#1329
#1330
b00000000000000000000000111000000 )
b00010 *
b11111111111111111111111111000000 ,
b0000000001110001 :
b000000000000000001110001 ;
b00000000000000000000000001110001 ?
b11111111111111111111111111000000 C
b0000000111000100 a
b01000000001000001101011100110011 b
b10000001100000011000000110000001 c
b11111111111111111111111111000000 e
1j
b100000 p
b00000000000000000000000111000100 q
b00000000000000000000000111001000 r
b00000000000000000000000111001000 s
b0110011 t
b00001 u
b00010 v
b01110 w
b101 x
b0100000 y
b10000001100000011000000110000001 z
b0111 !!
0"!
b00000000000000000000010000000010 #!
b0010000001100000 &!
b0000000001110001 '!
1)!
b00000000000000000000000111000000 ?!
b00010 A!
b11111111111111111111111111000000 B!
b00000000000000000000000000000000 C!
b10000001100000011000000110000001 H!
#1335
0)!
#1339
#1340
b00000000000000000000000111000100 )
b01110 *
b10000001100000011000000110000001 ,
b0000000001110010 :
b000000000000000001110010 ;
b00000000000000000000000001110010 ?
b10000001100000011000000110000001 O
b0000000111001000 a
b10000001100000011000001110110111 b
b00000000000000000000000000010000 c
b00000000000000000000000000000000 e
b00000000000000000000000111001000 q
b00000000000000000000000111001100 r
b00000000000000000000000111001100 s
b0110111 t
b00011 u
b11000 v
b00111 w
b000 x
b1000000 y
b00000000000000000000000000010000 z
b11 |
b00000000000000000000000000000000 }
b0000 !!
b10000001100000011000000000000000 #!
b011 $!
b0000000000000100 &!
b0000000001110010 '!
1)!
b00000000000000000000000111000100 ?!
b01110 A!
b10000001100000011000000110000001 B!
b10000000000000000000001110110111 C!
b10000000000000000000001110110111 D!
b10000001100000011000000000000000 H!
#1345
0)!
#1349
#1350
b00000000000000000000000111001000 )
b00111 *
b10000001100000011000000000000000 ,
b0000000001110011 :
b000000000000000001110011 ;
b00000000000000000000000001110011 ?
b10000001100000011000000000000000 H
b0000000111001100 a
b00011000000100111000001110010011 b
b10000001100000011000000110000001 c
b10000001100000011000000110000001 e
b00000000000000000000000111001100 q
b00000000000000000000000111010000 r
b00000000000000000000000111010000 s
b0010011 t
b00111 u
b00001 v
b0001100 y
b10000001100000011000000000000000 z
b00 |
b00000000000000000000000110000001 }
1"!
b00000000000000000000000110000001 #!
b000 $!
b0010000001100000 &!
b0000000001110011 '!
1)!
b00000000000000000000000111001000 ?!
b00111 A!
b10000001100000011000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b10000001100000011000000110000001 H!
#1355
0)!
#1359
#1360
b00000000000000000000000111001100 )
b10000001100000011000000110000001 ,
b0000000001110100 :
b000000000000000001110100 ;
b00000000000000000000000001110100 ?
b10000001100000011000000110000001 H
b0000000111010000 a
b00000001000100000000000110010011 b
b00000000000000000000000000010001 c
b00000000000000000000000000000000 e
b00000000000000000000000111010000 q
b00000000000000000000000111010100 r
b00000000000000000000000111010100 s
b00000 u
b10001 v
b00011 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000010001 }
b00000000000000000000000000010001 #!
b0000000000000100 &!
b0000000001110100 '!
1)!
b00000000000000000000000111001100 ?!
b10000001100000011000000110000001 B!
b10000000000000000000001110110111 C!
b10000000000000000000001110110111 D!
b00000000000000000000000000010001 H!
#1365
0)!
#1369
#1370
b00000000000000000000000111010000 )
b00011 *
b00000000000000000000000000010001 ,
b0000000001110101 :
b000000000000000001110101 ;
b00000000000000000000000001110101 ?
b00000000000000000000000000010001 D
b0000000111010100 a
b00111100011101110001011001100011 b
b00000000000000000000000000000000 c
b10000001100000011000000110000001 e
b00000000000000000000000111010100 q
b00000000000000000000000111011000 r
b00000000000000000000000111011000 s
b1100011 t
b01110 u
b00111 v
b01100 w
b001 x
b0011110 y
b10000001100000011000000110000001 z
0{
b10000001100000011000000110000001 }
1~
b1010 !!
0"!
b00000000000000000000001111001100 #!
b010 $!
b0000000000000000 &!
b0000000001110101 '!
1)!
b00000000000000000000000111010000 ?!
b00011 A!
b00000000000000000000000000010001 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#1375
0)!
#1379
#1380
b00000000000000000000000111010100 )
b01100 *
0+
b00000000000000000000000000000000 ,
b0000000001110110 :
b000000000000000001110110 ;
b00000000000000000000000001110110 ?
b0000000111011000 a
b10000001100000011000000010110111 b
b00000000000000000000000000010001 c
b00000000000000000000000000000000 e
b00000000000000000000000111011000 q
b00000000000000000000000111011100 r
b00000000000000000000000111011100 s
b0110111 t
b00011 u
b11000 v
b00001 w
b000 x
b1000000 y
b00000000000000000000000000010001 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b10000001100000011000000000000000 #!
b011 $!
b0000000000000100 &!
b0000000001110110 '!
1)!
b00000000000000000000000111010100 ?!
0@!
b01100 A!
b00000000000000000000000000000000 B!
b10000000000000000000001110110111 C!
b10000000000000000000001110110111 D!
b10000001100000011000000000000000 H!
#1385
0)!
#1389
#1390
b00000000000000000000000111011000 )
b00001 *
1+
b10000001100000011000000000000000 ,
b0000000001110111 :
b000000000000000001110111 ;
b00000000000000000000000001110111 ?
b10000001100000011000000000000000 B
b0000000111011100 a
b00011000000100001000000010010011 b
b10000001100000011000000110000001 c
b10000001100000011000000000000000 e
b00000000000000000000000111011100 q
b00000000000000000000000111100000 r
b00000000000000000000000111100000 s
b0010011 t
b00001 u
b00001 v
b0001100 y
b10000001100000011000000000000000 z
b00 |
b00000000000000000000000110000001 }
1"!
b00000000000000000000000110000001 #!
b000 $!
b0010000001100000 &!
b0000000001110111 '!
1)!
b00000000000000000000000111011000 ?!
1@!
b00001 A!
b10000001100000011000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b10000001100000011000000110000001 H!
#1395
0)!
#1399
#1400
b00000000000000000000000111011100 )
b10000001100000011000000110000001 ,
b0000000001111000 :
b000000000000000001111000 ;
b00000000000000000000000001111000 ?
b10000001100000011000000110000001 B
b0000000111100000 a
b11111100000100000000000100010011 b
b11111111111111111111111111000001 c
b10000001100000011000000110000001 e
0j
b000000 p
b00000000000000000000000111100000 q
b00000000000000000000000111100100 r
b00000000000000000000000111100100 s
b00000 u
b00010 w
b1111110 y
b00000000000000000000000000000000 z
b11111111111111111111111111000001 }
b11111111111111111111111111000001 #!
b0011111111110000 &!
b0000000001111000 '!
1)!
b00000000000000000000000111011100 ?!
b10000001100000011000000110000001 B!
b11111111111111111111111111111111 C!
b11111111111111111111111111000001 H!
#1405
0)!
#1409
#1410
b00000000000000000000000111100000 )
b00010 *
b11111111111111111111111111000001 ,
b0000000001111001 :
b000000000000000001111001 ;
b00000000000000000000000001111001 ?
b11111111111111111111111111000001 C
b0000000111100100 a
b01000000001000001101011100110011 b
b11000000110000001100000011000000 c
b11111111111111111111111111000001 e
1j
b100000 p
b00000000000000000000000111100100 q
b00000000000000000000000111101000 r
b00000000000000000000000111101000 s
b0110011 t
b00001 u
b00010 v
b01110 w
b101 x
b0100000 y
b10000001100000011000000110000001 z
b0111 !!
0"!
b00000000000000000000010000000010 #!
b0011000000110000 &!
b0000000001111001 '!
1)!
b00000000000000000000000111100000 ?!
b00010 A!
b11111111111111111111111111000001 B!
b00000000000000000000000000000000 C!
b11000000110000001100000011000000 H!
#1415
0)!
#1419
#1420
b00000000000000000000000111100100 )
b01110 *
b11000000110000001100000011000000 ,
b0000000001111010 :
b000000000000000001111010 ;
b00000000000000000000000001111010 ?
b11000000110000001100000011000000 O
b0000000111101000 a
b11000000110000001100001110110111 b
b10000001100000011000000110000001 c
b00000000000000000000000000000000 e
b00000000000000000000000111101000 q
b00000000000000000000000111101100 r
b00000000000000000000000111101100 s
b0110111 t
b01100 v
b00111 w
b100 x
b1100000 y
b11 |
b00000000000000000000000000000000 }
b0000 !!
b11000000110000001100000000000000 #!
b011 $!
b0010000001100000 &!
b0000000001111010 '!
1)!
b00000000000000000000000111100100 ?!
b01110 A!
b11000000110000001100000011000000 B!
b11000000110000001100000000000000 H!
#1425
0)!
#1429
#1430
b00000000000000000000000111101000 )
b00111 *
b11000000110000001100000000000000 ,
b0000000001111011 :
b000000000000000001111011 ;
b00000000000000000000000001111011 ?
b11000000110000001100000000000000 H
b0000000111101100 a
b00001100000000111000001110010011 b
b11000000110000001100000011000000 c
b00000000000000000000000111101100 q
b00000000000000000000000111110000 r
b00000000000000000000000111110000 s
b0010011 t
b00111 u
b00000 v
b000 x
b0000110 y
b11000000110000001100000000000000 z
b00 |
b00000000000000000000000011000000 }
1"!
b00000000000000000000000011000000 #!
b000 $!
b0011000000110000 &!
b0000000001111011 '!
1)!
b00000000000000000000000111101000 ?!
b00111 A!
b11000000110000001100000000000000 B!
b11000000110000001100000011000000 H!
#1435
0)!
#1439
#1440
b00000000000000000000000111101100 )
b11000000110000001100000011000000 ,
b0000000001111100 :
b000000000000000001111100 ;
b00000000000000000000000001111100 ?
b11000000110000001100000011000000 H
b0000000111110000 a
b00000001001000000000000110010011 b
b00000000000000000000000000010010 c
b00000000000000000000000111110000 q
b00000000000000000000000111110100 r
b00000000000000000000000111110100 s
b00000 u
b10010 v
b00011 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000010010 }
b00000000000000000000000000010010 #!
b0000000000000100 &!
b0000000001111100 '!
1)!
b00000000000000000000000111101100 ?!
b11000000110000001100000011000000 B!
b10000000000000000000001110110111 C!
b10000000000000000000001110110111 D!
b00000000000000000000000000010010 H!
#1445
0)!
#1449
#1450
b00000000000000000000000111110000 )
b00011 *
b00000000000000000000000000010010 ,
b0000000001111101 :
b000000000000000001111101 ;
b00000000000000000000000001111101 ?
b00000000000000000000000000010010 D
b0000000111110100 a
b00111010011101110001011001100011 b
b00000000000000000000000000000000 c
b11000000110000001100000011000000 e
b00000000000000000000000111110100 q
b00000000000000000000000111111000 r
b00000000000000000000000111111000 s
b1100011 t
b01110 u
b00111 v
b01100 w
b001 x
b0011101 y
b11000000110000001100000011000000 z
0{
b11000000110000001100000011000000 }
1~
b1010 !!
0"!
b00000000000000000000001110101100 #!
b010 $!
b0000000000000000 &!
b0000000001111101 '!
1)!
b00000000000000000000000111110000 ?!
b00011 A!
b00000000000000000000000000010010 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#1455
0)!
#1459
#1460
b00000000000000000000000111110100 )
b01100 *
0+
b00000000000000000000000000000000 ,
b0000000001111110 :
b000000000000000001111110 ;
b00000000000000000000000001111110 ?
b0000000111111000 a
b10000001100000011000000010110111 b
b00000000000000000000000000010010 c
b00000000000000000000000000000000 e
b00000000000000000000000111111000 q
b00000000000000000000000111111100 r
b00000000000000000000000111111100 s
b0110111 t
b00011 u
b11000 v
b00001 w
b000 x
b1000000 y
b00000000000000000000000000010010 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b10000001100000011000000000000000 #!
b011 $!
b0000000000000100 &!
b0000000001111110 '!
1)!
b00000000000000000000000111110100 ?!
0@!
b01100 A!
b00000000000000000000000000000000 B!
b10000000000000000000001110110111 C!
b10000000000000000000001110110111 D!
b10000001100000011000000000000000 H!
#1465
0)!
#1469
#1470
b00000000000000000000000111111000 )
b00001 *
1+
b10000001100000011000000000000000 ,
b0000000001111111 :
b000000000000000001111111 ;
b00000000000000000000000001111111 ?
b10000001100000011000000000000000 B
b0000000111111100 a
b00011000000100001000000010010011 b
b10000001100000011000000110000001 c
b10000001100000011000000000000000 e
b00000000000000000000000111111100 q
b00000000000000000000001000000000 r
b00000000000000000000001000000000 s
b0010011 t
b00001 u
b00001 v
b0001100 y
b10000001100000011000000000000000 z
b00 |
b00000000000000000000000110000001 }
1"!
b00000000000000000000000110000001 #!
b000 $!
b0010000001100000 &!
b0000000001111111 '!
1)!
b00000000000000000000000111111000 ?!
1@!
b00001 A!
b10000001100000011000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b10000001100000011000000110000001 H!
#1475
0)!
#1479
#1480
b00000000000000000000000111111100 )
b10000001100000011000000110000001 ,
b0000000010000000 :
b000000000000000010000000 ;
b00000000000000000000000010000000 ?
b10000001100000011000000110000001 B
b0000001000000000 a
b11111100011100000000000100010011 b
b11111111111111111111111111000111 c
b11000000110000001100000011000000 e
0j
b000000 p
b00000000000000000000001000000000 q
b00000000000000000000001000000100 r
b00000000000000000000001000000100 s
b00000 u
b00111 v
b00010 w
b1111110 y
b00000000000000000000000000000000 z
b11111111111111111111111111000111 }
b11111111111111111111111111000111 #!
b0011111111110001 &!
b0000000010000000 '!
1)!
b00000000000000000000000111111100 ?!
b10000001100000011000000110000001 B!
b11111111111111111111111111111111 C!
b11111111111111111111111111000111 H!
#1485
0)!
#1489
#1490
b00000000000000000000001000000000 )
b00010 *
b11111111111111111111111111000111 ,
b0000000010000001 :
b000000000000000010000001 ;
b00000000000000000000000010000001 ?
b11111111111111111111111111000111 C
b0000001000000100 a
b01000000001000001101011100110011 b
b11111111000000110000001100000011 c
b11111111111111111111111111000111 e
1j
b100000 p
b00000000000000000000001000000100 q
b00000000000000000000001000001000 r
b00000000000000000000001000001000 s
b0110011 t
b00001 u
b00010 v
b01110 w
b101 x
b0100000 y
b10000001100000011000000110000001 z
b0111 !!
0"!
b00000000000000000000010000000010 #!
b0000000011000000 &!
b0000000010000001 '!
1)!
b00000000000000000000001000000000 ?!
b00010 A!
b11111111111111111111111111000111 B!
b01000000001000001101011100110011 C!
b01000000001000001101011100110011 D!
b11111111000000110000001100000011 H!
#1495
0)!
#1499
#1500
b00000000000000000000001000000100 )
b01110 *
b11111111000000110000001100000011 ,
b0000000010000010 :
b000000000000000010000010 ;
b00000000000000000000000010000010 ?
b11111111000000110000001100000011 O
b0000001000001000 a
b11111111000000110000001110110111 b
b00000000000000000000000000000000 c
b00000000000000000000000000000000 e
b00000000000000000000001000001000 q
b00000000000000000000001000001100 r
b00000000000000000000001000001100 s
b0110111 t
b00110 u
b10000 v
b00111 w
b000 x
b1111111 y
b00000000000000000000000000000000 z
b11 |
b00000000000000000000000000000000 }
b0000 !!
b11111111000000110000000000000000 #!
b011 $!
b0000000000000000 &!
b0000000010000010 '!
1)!
b00000000000000000000001000000100 ?!
b01110 A!
b11111111000000110000001100000011 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b11111111000000110000000000000000 H!
#1505
0)!
#1509
#1510
b00000000000000000000001000001000 )
b00111 *
b11111111000000110000000000000000 ,
b0000000010000011 :
b000000000000000010000011 ;
b00000000000000000000000010000011 ?
b11111111000000110000000000000000 H
b0000001000001100 a
b00110000001100111000001110010011 b
b11111111000000110000001100000011 c
b00000000000000000000000000010010 e
b00000000000000000000001000001100 q
b00000000000000000000001000010000 r
b00000000000000000000001000010000 s
b0010011 t
b00111 u
b00011 v
b0011000 y
b11111111000000110000000000000000 z
b00 |
b00000000000000000000001100000011 }
1"!
b00000000000000000000001100000011 #!
b000 $!
b0000000011000000 &!
b0000000010000011 '!
1)!
b00000000000000000000001000001000 ?!
b00111 A!
b11111111000000110000000000000000 B!
b01000000001000001101011100110011 C!
b01000000001000001101011100110011 D!
b11111111000000110000001100000011 H!
#1515
0)!
#1519
#1520
b00000000000000000000001000001100 )
b11111111000000110000001100000011 ,
b0000000010000100 :
b000000000000000010000100 ;
b00000000000000000000000010000100 ?
b11111111000000110000001100000011 H
b0000001000010000 a
b00000001001100000000000110010011 b
b00000000000000000000000000010011 c
b00000000000000000000000000000000 e
b00000000000000000000001000010000 q
b00000000000000000000001000010100 r
b00000000000000000000001000010100 s
b00000 u
b10011 v
b00011 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000010011 }
b00000000000000000000000000010011 #!
b0000000000000100 &!
b0000000010000100 '!
1)!
b00000000000000000000001000001100 ?!
b11111111000000110000001100000011 B!
b10000000000000000000001110110111 C!
b10000000000000000000001110110111 D!
b00000000000000000000000000010011 H!
#1525
0)!
#1529
#1530
b00000000000000000000001000010000 )
b00011 *
b00000000000000000000000000010011 ,
b0000000010000101 :
b000000000000000010000101 ;
b00000000000000000000000010000101 ?
b00000000000000000000000000010011 D
b0000001000010100 a
b00111000011101110001011001100011 b
b00000000000000000000000000000000 c
b11111111000000110000001100000011 e
b00000000000000000000001000010100 q
b00000000000000000000001000011000 r
b00000000000000000000001000011000 s
b1100011 t
b01110 u
b00111 v
b01100 w
b001 x
b0011100 y
b11111111000000110000001100000011 z
0{
b11111111000000110000001100000011 }
1~
b1010 !!
0"!
b00000000000000000000001110001100 #!
b010 $!
b0000000000000000 &!
b0000000010000101 '!
1)!
b00000000000000000000001000010000 ?!
b00011 A!
b00000000000000000000000000010011 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#1535
0)!
#1539
#1540
b00000000000000000000001000010100 )
b01100 *
0+
b00000000000000000000000000000000 ,
b0000000010000110 :
b000000000000000010000110 ;
b00000000000000000000000010000110 ?
b0000001000011000 a
b10000001100000011000000010110111 b
b00000000000000000000000000010011 c
b00000000000000000000000000000000 e
b00000000000000000000001000011000 q
b00000000000000000000001000011100 r
b00000000000000000000001000011100 s
b0110111 t
b00011 u
b11000 v
b00001 w
b000 x
b1000000 y
b00000000000000000000000000010011 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b10000001100000011000000000000000 #!
b011 $!
b0000000000000100 &!
b0000000010000110 '!
1)!
b00000000000000000000001000010100 ?!
0@!
b01100 A!
b00000000000000000000000000000000 B!
b10000000000000000000001110110111 C!
b10000000000000000000001110110111 D!
b10000001100000011000000000000000 H!
#1545
0)!
#1549
#1550
b00000000000000000000001000011000 )
b00001 *
1+
b10000001100000011000000000000000 ,
b0000000010000111 :
b000000000000000010000111 ;
b00000000000000000000000010000111 ?
b10000001100000011000000000000000 B
b0000001000011100 a
b00011000000100001000000010010011 b
b10000001100000011000000110000001 c
b10000001100000011000000000000000 e
b00000000000000000000001000011100 q
b00000000000000000000001000100000 r
b00000000000000000000001000100000 s
b0010011 t
b00001 u
b00001 v
b0001100 y
b10000001100000011000000000000000 z
b00 |
b00000000000000000000000110000001 }
1"!
b00000000000000000000000110000001 #!
b000 $!
b0010000001100000 &!
b0000000010000111 '!
1)!
b00000000000000000000001000011000 ?!
1@!
b00001 A!
b10000001100000011000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b10000001100000011000000110000001 H!
#1555
0)!
#1559
#1560
b00000000000000000000001000011100 )
b10000001100000011000000110000001 ,
b0000000010001000 :
b000000000000000010001000 ;
b00000000000000000000000010001000 ?
b10000001100000011000000110000001 B
b0000001000100000 a
b11111100111000000000000100010011 b
b11111111111111111111111111001110 c
b11111111000000110000001100000011 e
0j
b000000 p
b00000000000000000000001000100000 q
b00000000000000000000001000100100 r
b00000000000000000000001000100100 s
b00000 u
b01110 v
b00010 w
b1111110 y
b00000000000000000000000000000000 z
b11111111111111111111111111001110 }
b11111111111111111111111111001110 #!
b0011111111110011 &!
b0000000010001000 '!
1)!
b00000000000000000000001000011100 ?!
b10000001100000011000000110000001 B!
b11111111111111111111111111111111 C!
b11111111111111111111111111001110 H!
#1565
0)!
#1569
#1570
b00000000000000000000001000100000 )
b00010 *
b11111111111111111111111111001110 ,
b0000000010001001 :
b000000000000000010001001 ;
b00000000000000000000000010001001 ?
b11111111111111111111111111001110 C
b0000001000100100 a
b01000000001000001101011100110011 b
b11111111111111100000011000000110 c
b11111111111111111111111111001110 e
1j
b100000 p
b00000000000000000000001000100100 q
b00000000000000000000001000101000 r
b00000000000000000000001000101000 s
b0110011 t
b00001 u
b00010 v
b01110 w
b101 x
b0100000 y
b10000001100000011000000110000001 z
b0111 !!
0"!
b00000000000000000000010000000010 #!
b0000000110000001 &!
b0000000010001001 '!
1)!
b00000000000000000000001000100000 ?!
b00010 A!
b11111111111111111111111111001110 B!
b00000000000000000000000000000000 C!
b11111111111111100000011000000110 H!
#1575
0)!
#1579
#1580
b00000000000000000000001000100100 )
b01110 *
b11111111111111100000011000000110 ,
b0000000010001010 :
b000000000000000010001010 ;
b00000000000000000000000010001010 ?
b11111111111111100000011000000110 O
b0000001000101000 a
b11111111111111100000001110110111 b
b00000000000000000000000000000000 c
b00000000000000000000000000000000 e
b00000000000000000000001000101000 q
b00000000000000000000001000101100 r
b00000000000000000000001000101100 s
b0110111 t
b11100 u
b11111 v
b00111 w
b000 x
b1111111 y
b00000000000000000000000000000000 z
b11 |
b00000000000000000000000000000000 }
b0000 !!
b11111111111111100000000000000000 #!
b011 $!
b0000000000000000 &!
b0000000010001010 '!
1)!
b00000000000000000000001000100100 ?!
b01110 A!
b11111111111111100000011000000110 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b11111111111111100000000000000000 H!
#1585
0)!
#1589
#1590
b00000000000000000000001000101000 )
b00111 *
b11111111111111100000000000000000 ,
b0000000010001011 :
b000000000000000010001011 ;
b00000000000000000000000010001011 ?
b11111111111111100000000000000000 H
b0000001000101100 a
b01100000011000111000001110010011 b
b11111111111111100000011000000110 c
b00000000000000000000001000101100 q
b00000000000000000000001000110000 r
b00000000000000000000001000110000 s
b0010011 t
b00111 u
b00110 v
b0110000 y
b11111111111111100000000000000000 z
b00 |
b00000000000000000000011000000110 }
1"!
b00000000000000000000011000000110 #!
b000 $!
b0000000110000001 &!
b0000000010001011 '!
1)!
b00000000000000000000001000101000 ?!
b00111 A!
b11111111111111100000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b11111111111111100000011000000110 H!
#1595
0)!
#1599
#1600
b00000000000000000000001000101100 )
b11111111111111100000011000000110 ,
b0000000010001100 :
b000000000000000010001100 ;
b00000000000000000000000010001100 ?
b11111111111111100000011000000110 H
b0000001000110000 a
b00000001010000000000000110010011 b
b00000000000000000000000000010100 c
b00000000000000000000001000110000 q
b00000000000000000000001000110100 r
b00000000000000000000001000110100 s
b00000 u
b10100 v
b00011 w
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000010100 }
b00000000000000000000000000010100 #!
b0000000000000101 &!
b0000000010001100 '!
1)!
b00000000000000000000001000101100 ?!
b11111111111111100000011000000110 B!
b00000000001000000000000110010011 C!
b00000000001000000000000110010011 D!
b00000000000000000000000000010100 H!
#1605
0)!
#1609
#1610
b00000000000000000000001000110000 )
b00011 *
b00000000000000000000000000010100 ,
b0000000010001101 :
b000000000000000010001101 ;
b00000000000000000000000010001101 ?
b00000000000000000000000000010100 D
b0000001000110100 a
b00110110011101110001011001100011 b
b00000000000000000000000000000000 c
b11111111111111100000011000000110 e
b00000000000000000000001000110100 q
b00000000000000000000001000111000 r
b00000000000000000000001000111000 s
b1100011 t
b01110 u
b00111 v
b01100 w
b001 x
b0011011 y
b11111111111111100000011000000110 z
0{
b11111111111111100000011000000110 }
1~
b1010 !!
0"!
b00000000000000000000001101101100 #!
b010 $!
b0000000000000000 &!
b0000000010001101 '!
1)!
b00000000000000000000001000110000 ?!
b00011 A!
b00000000000000000000000000010100 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#1615
0)!
#1619
#1620
b00000000000000000000001000110100 )
b01100 *
0+
b00000000000000000000000000000000 ,
b0000000010001110 :
b000000000000000010001110 ;
b00000000000000000000000010001110 ?
b0000001000111000 a
b10000001100000011000000010110111 b
b00000000000000000000000000010100 c
b00000000000000000000000000000000 e
b00000000000000000000001000111000 q
b00000000000000000000001000111100 r
b00000000000000000000001000111100 s
b0110111 t
b00011 u
b11000 v
b00001 w
b000 x
b1000000 y
b00000000000000000000000000010100 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b10000001100000011000000000000000 #!
b011 $!
b0000000000000101 &!
b0000000010001110 '!
1)!
b00000000000000000000001000110100 ?!
0@!
b01100 A!
b00000000000000000000000000000000 B!
b00000000001000000000000110010011 C!
b00000000001000000000000110010011 D!
b10000001100000011000000000000000 H!
#1625
0)!
#1629
#1630
b00000000000000000000001000111000 )
b00001 *
1+
b10000001100000011000000000000000 ,
b0000000010001111 :
b000000000000000010001111 ;
b00000000000000000000000010001111 ?
b10000001100000011000000000000000 B
b0000001000111100 a
b00011000000100001000000010010011 b
b10000001100000011000000110000001 c
b10000001100000011000000000000000 e
b00000000000000000000001000111100 q
b00000000000000000000001001000000 r
b00000000000000000000001001000000 s
b0010011 t
b00001 u
b00001 v
b0001100 y
b10000001100000011000000000000000 z
b00 |
b00000000000000000000000110000001 }
1"!
b00000000000000000000000110000001 #!
b000 $!
b0010000001100000 &!
b0000000010001111 '!
1)!
b00000000000000000000001000111000 ?!
1@!
b00001 A!
b10000001100000011000000000000000 B!
b00000000000000000000000000000000 C!
b00000000000000000000000000000000 D!
b10000001100000011000000110000001 H!
#1635
0)!
#1639
#1640
b00000000000000000000001000111100 )
b10000001100000011000000110000001 ,
b0000000010010000 :
b000000000000000010010000 ;
b00000000000000000000000010010000 ?
b10000001100000011000000110000001 B
b0000001001000000 a
b11111111111100000000000100010011 b
b11111111111111111111111111111111 c
b00000000000000000000000000000000 e
0j
b000000 p
b00000000000000000000001001000000 q
b00000000000000000000001001000100 r
b00000000000000000000001001000100 s
b00000 u
b11111 v
b00010 w
b1111111 y
b00000000000000000000000000000000 z
b11111111111111111111111111111111 }
b11111111111111111111111111111111 #!
b0011111111111111 &!
b0000000010010000 '!
1)!
b00000000000000000000001000111100 ?!
b10000001100000011000000110000001 B!
b11111111111111111111111111111111 C!
b11111111111111111111111111111111 H!
#1645
0)!
#1649
#1650
b00000000000000000000001001000000 )
b00010 *
b11111111111111111111111111111111 ,
b0000000010010001 :
b000000000000000010010001 ;
b00000000000000000000000010010001 ?
b11111111111111111111111111111111 C
b0000001001000100 a
b01000000001000001101011100110011 b
b11111111111111111111111111111111 e
b00000000000000000000001001000100 q
b00000000000000000000001001001000 r
b00000000000000000000001001001000 s
b0110011 t
b00001 u
b00010 v
b01110 w
b101 x
b0100000 y
b10000001100000011000000110000001 z
b0111 !!
0"!
b00000000000000000000010000000010 #!
b0000000010010001 '!
1)!
b00000000000000000000001001000000 ?!
b00010 A!
b11111111111111111111111111111111 B!
#1655
0)!
#1659
#1660
b00000000000000000000001001000100 )
b01110 *
b0000000010010010 :
b000000000000000010010010 ;
b00000000000000000000000010010010 ?
b11111111111111111111111111111111 O
b0000001001001000 a
b11111111111100000000001110010011 b
b00000000000000000000000000000000 e
b00000000000000000000001001001000 q
b00000000000000000000001001001100 r
b00000000000000000000001001001100 s
b0010011 t
b00000 u
b11111 v
b00111 w
b000 x
b1111111 y
b00000000000000000000000000000000 z
b0000 !!
1"!
b11111111111111111111111111111111 #!
b0000000010010010 '!
1)!
b00000000000000000000001001000100 ?!
b01110 A!
#1665
0)!
#1669
#1670
b00000000000000000000001001001000 )
b00111 *
b0000000010010011 :
b000000000000000010010011 ;
b00000000000000000000000010010011 ?
b11111111111111111111111111111111 H
b0000001001001100 a
b00000001010100000000000110010011 b
b00000000000000000000000000010101 c
1j
b100000 p
b00000000000000000000001001001100 q
b00000000000000000000001001010000 r
b00000000000000000000001001010000 s
b10101 v
b00011 w
b0000000 y
b00000000000000000000000000010101 }
b00000000000000000000000000010101 #!
b0000000000000101 &!
b0000000010010011 '!
1)!
b00000000000000000000001001001000 ?!
b00111 A!
b00000000001000000000000110010011 C!
b00000000001000000000000110010011 D!
b00000000000000000000000000010101 H!
#1675
0)!
#1679
#1680
b00000000000000000000001001001100 )
b00011 *
b00000000000000000000000000010101 ,
b0000000010010100 :
b000000000000000010010100 ;
b00000000000000000000000010010100 ?
b00000000000000000000000000010101 D
b0000001001010000 a
b00110100011101110001100001100011 b
b00000000000000000000000000000000 c
b11111111111111111111111111111111 e
b00000000000000000000001001010000 q
b00000000000000000000001001010100 r
b00000000000000000000001001010100 s
b1100011 t
b01110 u
b00111 v
b10000 w
b001 x
b0011010 y
b11111111111111111111111111111111 z
0{
b11111111111111111111111111111111 }
1~
b1010 !!
0"!
b00000000000000000000001101010000 #!
b010 $!
b0000000000000000 &!
b0000000010010100 '!
1)!
b00000000000000000000001001001100 ?!
b00011 A!
b00000000000000000000000000010101 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#1685
0)!
#1689
#1690
b00000000000000000000001001010000 )
b10000 *
0+
b00000000000000000000000000000000 ,
b0000000010010101 :
b000000000000000010010101 ;
b00000000000000000000000010010101 ?
b0000001001010100 a
b10000000000000000000000010110111 b
b00000000000000000000000000000000 e
b00000000000000000000001001010100 q
b00000000000000000000001001011000 r
b00000000000000000000001001011000 s
b0110111 t
b00000 u
b00000 v
b00001 w
b000 x
b1000000 y
b00000000000000000000000000000000 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b10000000000000000000000000000000 #!
b011 $!
b0000000010010101 '!
1)!
b00000000000000000000001001010000 ?!
0@!
b10000 A!
b00000000000000000000000000000000 B!
b10000000000000000000000000000000 H!
#1695
0)!
#1699
#1700
b00000000000000000000001001010100 )
b00001 *
1+
b10000000000000000000000000000000 ,
b0000000010010110 :
b000000000000000010010110 ;
b00000000000000000000000010010110 ?
b10000000000000000000000000000000 B
b0000001001011000 a
b00000000011100000000000100010011 b
b00000000000000000000000000000111 c
b11111111111111111111111111111111 e
b00000000000000000000001001011000 q
b00000000000000000000001001011100 r
b00000000000000000000001001011100 s
b0010011 t
b00111 v
b00010 w
b0000000 y
b00 |
b00000000000000000000000000000111 }
1"!
b00000000000000000000000000000111 #!
b000 $!
b0000000000000001 &!
b0000000010010110 '!
1)!
b00000000000000000000001001010100 ?!
1@!
b00001 A!
b10000000000000000000000000000000 B!
b10000000000000000000000010110111 C!
b10000000000000000000000010110111 D!
b00000000000000000000000000000111 H!
#1705
0)!
#1709
#1710
b00000000000000000000001001011000 )
b00010 *
b00000000000000000000000000000111 ,
b0000000010010111 :
b000000000000000010010111 ;
b00000000000000000000000010010111 ?
b00000000000000000000000000000111 C
b0000001001011100 a
b01000000001000001101000010110011 b
b11111111000000000000000000000000 c
b00000000000000000000000000000111 e
b00000000000000000000001001011100 q
b00000000000000000000001001100000 r
b00000000000000000000001001100000 s
b0110011 t
b00001 u
b00010 v
b00001 w
b101 x
b0100000 y
b10000000000000000000000000000000 z
b0111 !!
0"!
b00000000000000000000010000000010 #!
b0000000000000000 &!
b0000000010010111 '!
1)!
b00000000000000000000001001011000 ?!
b00010 A!
b00000000000000000000000000000111 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b11111111000000000000000000000000 H!
#1715
0)!
#1719
#1720
b00000000000000000000001001011100 )
b00001 *
b11111111000000000000000000000000 ,
b0000000010011000 :
b000000000000000010011000 ;
b00000000000000000000000010011000 ?
b11111111000000000000000000000000 B
b0000001001100000 a
b11111111000000000000001110110111 b
b00000000000000000000000000000000 c
b00000000000000000000000000000000 e
b00000000000000000000001001100000 q
b00000000000000000000001001100100 r
b00000000000000000000001001100100 s
b0110111 t
b00000 u
b10000 v
b00111 w
b000 x
b1111111 y
b00000000000000000000000000000000 z
b11 |
b00000000000000000000000000000000 }
b0000 !!
b11111111000000000000000000000000 #!
b011 $!
b0000000010011000 '!
1)!
b00000000000000000000001001011100 ?!
b00001 A!
b11111111000000000000000000000000 B!
#1725
0)!
#1729
#1730
b00000000000000000000001001100000 )
b00111 *
b0000000010011001 :
b000000000000000010011001 ;
b00000000000000000000000010011001 ?
b11111111000000000000000000000000 H
b0000001001100100 a
b00000001011000000000000110010011 b
b00000000000000000000000000010110 c
b00000000000000000000001001100100 q
b00000000000000000000001001101000 r
b00000000000000000000001001101000 s
b0010011 t
b10110 v
b00011 w
b0000000 y
b00 |
b00000000000000000000000000010110 }
1"!
b00000000000000000000000000010110 #!
b000 $!
b0000000000000101 &!
b0000000010011001 '!
1)!
b00000000000000000000001001100000 ?!
b00111 A!
b00000000001000000000000110010011 C!
b00000000001000000000000110010011 D!
b00000000000000000000000000010110 H!
#1735
0)!
#1739
#1740
b00000000000000000000001001100100 )
b00011 *
b00000000000000000000000000010110 ,
b0000000010011010 :
b000000000000000010011010 ;
b00000000000000000000000010011010 ?
b00000000000000000000000000010110 D
b0000001001101000 a
b00110010011100001001110001100011 b
b00000000000000000000000000000000 c
b11111111000000000000000000000000 e
b00000000000000000000001001101000 q
b00000000000000000000001001101100 r
b00000000000000000000001001101100 s
b1100011 t
b00001 u
b00111 v
b11000 w
b001 x
b0011001 y
b11111111000000000000000000000000 z
0{
b11111111000000000000000000000000 }
1~
b1010 !!
0"!
b00000000000000000000001100111000 #!
b010 $!
b0000000000000000 &!
b0000000010011010 '!
1)!
b00000000000000000000001001100100 ?!
b00011 A!
b00000000000000000000000000010110 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#1745
0)!
#1749
#1750
b00000000000000000000001001101000 )
b11000 *
0+
b00000000000000000000000000000000 ,
b0000000010011011 :
b000000000000000010011011 ;
b00000000000000000000000010011011 ?
b0000001001101100 a
b10000000000000000000000010110111 b
b00000000000000000000000000000000 e
b00000000000000000000001001101100 q
b00000000000000000000001001110000 r
b00000000000000000000001001110000 s
b0110111 t
b00000 u
b00000 v
b00001 w
b000 x
b1000000 y
b00000000000000000000000000000000 z
1{
b11 |
b00000000000000000000000000000000 }
0~
b0000 !!
b10000000000000000000000000000000 #!
b011 $!
b0000000010011011 '!
1)!
b00000000000000000000001001101000 ?!
0@!
b11000 A!
b00000000000000000000000000000000 B!
b10000000000000000000000000000000 H!
#1755
0)!
#1759
#1760
b00000000000000000000001001101100 )
b00001 *
1+
b10000000000000000000000000000000 ,
b0000000010011100 :
b000000000000000010011100 ;
b00000000000000000000000010011100 ?
b10000000000000000000000000000000 B
b0000001001110000 a
b00000000111000000000000100010011 b
b00000000000000000000000000001110 c
b11111111111111111111111111111111 e
b00000000000000000000001001110000 q
b00000000000000000000001001110100 r
b00000000000000000000001001110100 s
b0010011 t
b01110 v
b00010 w
b0000000 y
b00 |
b00000000000000000000000000001110 }
1"!
b00000000000000000000000000001110 #!
b000 $!
b0000000000000011 &!
b0000000010011100 '!
1)!
b00000000000000000000001001101100 ?!
1@!
b00001 A!
b10000000000000000000000000000000 B!
b01000000001000001101011100110011 C!
b01000000001000001101011100110011 D!
b00000000000000000000000000001110 H!
#1765
0)!
#1769
#1770
b00000000000000000000001001110000 )
b00010 *
b00000000000000000000000000001110 ,
b0000000010011101 :
b000000000000000010011101 ;
b00000000000000000000000010011101 ?
b00000000000000000000000000001110 C
b0000001001110100 a
b01000000001000001101000100110011 b
b11111111111111100000000000000000 c
b00000000000000000000000000001110 e
b00000000000000000000001001110100 q
b00000000000000000000001001111000 r
b00000000000000000000001001111000 s
b0110011 t
b00001 u
b00010 v
b101 x
b0100000 y
b10000000000000000000000000000000 z
b0111 !!
0"!
b00000000000000000000010000000010 #!
b0000000000000000 &!
b0000000010011101 '!
1)!
b00000000000000000000001001110000 ?!
b00010 A!
b00000000000000000000000000001110 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b11111111111111100000000000000000 H!
#1775
0)!
#1779
#1780
b00000000000000000000001001110100 )
b11111111111111100000000000000000 ,
b0000000010011110 :
b000000000000000010011110 ;
b00000000000000000000000010011110 ?
b11111111111111100000000000000000 C
b0000001001111000 a
b11111111111111100000001110110111 b
b00000000000000000000000000000000 c
b00000000000000000000000000000000 e
b00000000000000000000001001111000 q
b00000000000000000000001001111100 r
b00000000000000000000001001111100 s
b0110111 t
b11100 u
b11111 v
b00111 w
b000 x
b1111111 y
b00000000000000000000000000000000 z
b11 |
b00000000000000000000000000000000 }
b0000 !!
b11111111111111100000000000000000 #!
b011 $!
b0000000010011110 '!
1)!
b00000000000000000000001001110100 ?!
b11111111111111100000000000000000 B!
#1785
0)!
#1789
#1790
b00000000000000000000001001111000 )
b00111 *
b0000000010011111 :
b000000000000000010011111 ;
b00000000000000000000000010011111 ?
b11111111111111100000000000000000 H
b0000001001111100 a
b00000001011100000000000110010011 b
b00000000000000000000000000010111 c
b00000000000000000000001001111100 q
b00000000000000000000001010000000 r
b00000000000000000000001010000000 s
b0010011 t
b00000 u
b10111 v
b00011 w
b0000000 y
b00 |
b00000000000000000000000000010111 }
1"!
b00000000000000000000000000010111 #!
b000 $!
b0000000000000101 &!
b0000000010011111 '!
1)!
b00000000000000000000001001111000 ?!
b00111 A!
b00000000001000000000000110010011 C!
b00000000001000000000000110010011 D!
b00000000000000000000000000010111 H!
#1795
0)!
#1799
#1800
b00000000000000000000001001111100 )
b00011 *
b00000000000000000000000000010111 ,
b0000000010100000 :
b000000000000000010100000 ;
b00000000000000000000000010100000 ?
b00000000000000000000000000010111 D
b0000001010000000 a
b00110010011100010001000001100011 b
b00000000000000000000000000000000 c
b11111111111111100000000000000000 e
b00000000000000000000001010000000 q
b00000000000000000000001010000100 r
b00000000000000000000001010000100 s
b1100011 t
b00010 u
b00111 v
b00000 w
b001 x
b0011001 y
b11111111111111100000000000000000 z
0{
b11111111111111100000000000000000 }
1~
b1010 !!
0"!
b00000000000000000000001100100000 #!
b010 $!
b0000000000000000 &!
b0000000010100000 '!
1)!
b00000000000000000000001001111100 ?!
b00011 A!
b00000000000000000000000000010111 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#1805
0)!
#1809
#1810
b00000000000000000000001010000000 )
b00000 *
0+
b00000000000000000000000000000000 ,
b0000000010100001 :
b000000000000000010100001 ;
b00000000000000000000000010100001 ?
b0000001010000100 a
b00000000011100000000000010010011 b
b00000000000000000000000000000111 c
b00000000000000000000001010000100 q
b00000000000000000000001010001000 r
b00000000000000000000001010001000 s
b0010011 t
b00000 u
b00001 w
b000 x
b0000000 y
b00000000000000000000000000000000 z
1{
b00000000000000000000000000000111 }
0~
b0000 !!
1"!
b00000000000000000000000000000111 #!
b000 $!
b0000000000000001 &!
b0000000010100001 '!
1)!
b00000000000000000000001010000000 ?!
0@!
b00000 A!
b00000000000000000000000000000000 B!
b10000000000000000000000010110111 C!
b10000000000000000000000010110111 D!
b00000000000000000000000000000111 H!
#1815
0)!
#1819
#1820
b00000000000000000000001010000100 )
b00001 *
1+
b00000000000000000000000000000111 ,
b0000000010100010 :
b000000000000000010100010 ;
b00000000000000000000000010100010 ?
b00000000000000000000000000000111 B
b0000001010001000 a
b01000000000100001101000010110011 b
b00000000000000000000000000000000 c
b00000000000000000000000000000111 e
b00000000000000000000001010001000 q
b00000000000000000000001010001100 r
b00000000000000000000001010001100 s
b0110011 t
b00001 u
b00001 v
b101 x
b0100000 y
b00000000000000000000000000000111 z
b0111 !!
0"!
b00000000000000000000010000000001 #!
b0000000000000000 &!
b0000000010100010 '!
1)!
b00000000000000000000001010000100 ?!
1@!
b00001 A!
b00000000000000000000000000000111 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#1825
0)!
#1829
#1830
b00000000000000000000001010001000 )
b00000000000000000000000000000000 ,
b0000000010100011 :
b000000000000000010100011 ;
b00000000000000000000000010100011 ?
b00000000000000000000000000000000 B
b0000001010001100 a
b00000000000000000000001110010011 b
b00000000000000000000000000000000 e
b00000000000000000000001010001100 q
b00000000000000000000001010010000 r
b00000000000000000000001010010000 s
b0010011 t
b00000 u
b00000 v
b00111 w
b000 x
b0000000 y
b00000000000000000000000000000000 z
b00000000000000000000000000000000 }
b0000 !!
1"!
b00000000000000000000000000000000 #!
b0000000010100011 '!
1)!
b00000000000000000000001010001000 ?!
b00000000000000000000000000000000 B!
#1835
0)!
#1839
#1840
b00000000000000000000001010001100 )
b00111 *
b0000000010100100 :
b000000000000000010100100 ;
b00000000000000000000000010100100 ?
b00000000000000000000000000000000 H
b0000001010010000 a
b00000001100000000000000110010011 b
b00000000000000000000000000011000 c
b00000000000000000000001010010000 q
b00000000000000000000001010010100 r
b00000000000000000000001010010100 s
b11000 v
b00011 w
b00000000000000000000000000011000 }
b00000000000000000000000000011000 #!
b0000000000000110 &!
b0000000010100100 '!
1)!
b00000000000000000000001010001100 ?!
b00111 A!
b01011000011101110001010001100011 C!
b01011000011101110001010001100011 D!
b00000000000000000000000000011000 H!
#1845
0)!
#1849
#1850
b00000000000000000000001010010000 )
b00011 *
b00000000000000000000000000011000 ,
b0000000010100101 :
b000000000000000010100101 ;
b00000000000000000000000010100101 ?
b00000000000000000000000000011000 D
b0000001010010100 a
b00110000011100001001011001100011 b
b00000000000000000000000000000000 c
b00000000000000000000001010010100 q
b00000000000000000000001010011000 r
b00000000000000000000001010011000 s
b1100011 t
b00001 u
b00111 v
b01100 w
b001 x
b0011000 y
0{
b00000000000000000000000000000000 }
1~
b1010 !!
0"!
b00000000000000000000001100001100 #!
b010 $!
b0000000000000000 &!
b0000000010100101 '!
1)!
b00000000000000000000001010010000 ?!
b00011 A!
b00000000000000000000000000011000 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b00000000000000000000000000000000 H!
#1855
0)!
#1859
#1860
b00000000000000000000001010010100 )
b01100 *
0+
b00000000000000000000000000000000 ,
b0000000010100110 :
b000000000000000010100110 ;
b00000000000000000000000010100110 ?
b0000001010011000 a
b00000000000000000000001000010011 b
b00000000000000000000001010011000 q
b00000000000000000000001010011100 r
b00000000000000000000001010011100 s
b0010011 t
b00000 u
b00000 v
b00100 w
b000 x
b0000000 y
1{
0~
b0000 !!
1"!
b00000000000000000000000000000000 #!
b000 $!
b0000000010100110 '!
1)!
b00000000000000000000001010010100 ?!
0@!
b01100 A!
b00000000000000000000000000000000 B!
#1865
0)!
#1869
#1870
b00000000000000000000001010011000 )
b00100 *
1+
b0000000010100111 :
b000000000000000010100111 ;
b00000000000000000000000010100111 ?
b0000001010011100 a
b10000000000000000000000010110111 b
b00000000000000000000001010011100 q
b00000000000000000000001010100000 r
b00000000000000000000001010100000 s
b0110111 t
b00001 w
b1000000 y
b11 |
0"!
b10000000000000000000000000000000 #!
b011 $!
b0000000010100111 '!
1)!
b00000000000000000000001010011000 ?!
1@!
b00100 A!
b10000000000000000000000000000000 H!
#1875
0)!
#1879
#1880
b00000000000000000000001010011100 )
b00001 *
b10000000000000000000000000000000 ,
b0000000010101000 :
b000000000000000010101000 ;
b00000000000000000000000010101000 ?
b10000000000000000000000000000000 B
b0000001010100000 a
b00000000011100000000000100010011 b
b00000000000000000000000000000111 c
b00000000000000000000001010100000 q
b00000000000000000000001010100100 r
b00000000000000000000001010100100 s
b0010011 t
b00111 v
b00010 w
b0000000 y
b00 |
b00000000000000000000000000000111 }
1"!
b00000000000000000000000000000111 #!
b000 $!
b0000000000000001 &!
b0000000010101000 '!
1)!
b00000000000000000000001010011100 ?!
b00001 A!
b10000000000000000000000000000000 B!
b10000000000000000000000010110111 C!
b10000000000000000000000010110111 D!
b00000000000000000000000000000111 H!
#1885
0)!
#1889
#1890
b00000000000000000000001010100000 )
b00010 *
b00000000000000000000000000000111 ,
b0000000010101001 :
b000000000000000010101001 ;
b00000000000000000000000010101001 ?
b00000000000000000000000000000111 C
b0000001010100100 a
b01000000001000001101011100110011 b
b11111111000000000000000000000000 c
b00000000000000000000000000000111 e
b00000000000000000000001010100100 q
b00000000000000000000001010101000 r
b00000000000000000000001010101000 s
b0110011 t
b00001 u
b00010 v
b01110 w
b101 x
b0100000 y
b10000000000000000000000000000000 z
b0111 !!
0"!
b00000000000000000000010000000010 #!
b0000000000000000 &!
b0000000010101001 '!
1)!
b00000000000000000000001010100000 ?!
b00010 A!
b00000000000000000000000000000111 B!
b00000000010000000000000001101111 C!
b00000000010000000000000001101111 D!
b11111111000000000000000000000000 H!
#1895
0)!
#1899
#1900
b00000000000000000000001010100100 )
b01110 *
b11111111000000000000000000000000 ,
b0000000010101010 :
b000000000000000010101010 ;
b00000000000000000000000010101010 ?
b11111111000000000000000000000000 O
b0000001010101000 a
b00000000000001110000001100010011 b
b00000000000000000000000000000000 e
b00000000000000000000001010101000 q
b00000000000000000000001010101100 r
b00000000000000000000001010101100 s
b0010011 t
b01110 u
b00000 v
b00110 w
b000 x
b0000000 y
b11111111000000000000000000000000 z
b00000000000000000000000000000000 }
b0000 !!
1"!
b00000000000000000000000000000000 #!
b0000000010101010 '!
1)!
b00000000000000000000001010100100 ?!
b01110 A!
b11111111000000000000000000000000 B!
#1905
0)!
#1909
#1910
b00000000000000000000001010101000 )
b00110 *
b0000000010101011 :
b000000000000000010101011 ;
b00000000000000000000000010101011 ?
b11111111000000000000000000000000 G
b0000001010101100 a
b00000000000100100000001000010011 b
b00000000000000000000000000000001 c
b10000000000000000000000000000000 e
b00000000000000000000001010101100 q
b00000000000000000000001010110000 r
b00000000000000000000001010110000 s
b00100 u
b00001 v
b00100 w
b00000000000000000000000000000000 z
b00000000000000000000000000000001 }
b00000000000000000000000000000001 #!
b0000000010101011 '!
1)!
b00000000000000000000001010101000 ?!
b00110 A!
b00000000000000000000000000000001 H!
#1915
0)!
#1919
#1920
b00000000000000000000001010101100 )
b00100 *
b00000000000000000000000000000001 ,
b0000000010101100 :
b000000000000000010101100 ;
b00000000000000000000000010101100 ?
b00000000000000000000000000000001 E
b0000001010110000 a
b00000000001000000000001010010011 b
b00000000000000000000000000000010 c
b00000000000000000000000000000111 e
b00000000000000000000001010110000 q
b00000000000000000000001010110100 r
b00000000000000000000001010110100 s
b00000 u
b00010 v
b00101 w
b00000000000000000000000000000010 }
b00000000000000000000000000000010 #!
b0000000010101100 '!
1)!
b00000000000000000000001010101100 ?!
b00100 A!
b00000000000000000000000000000001 B!
b00000000000000000000000000000010 H!
#1925
0)!
#1929
#1930
b00000000000000000000001010110000 )
b00101 *
b00000000000000000000000000000010 ,
b0000000010101101 :
b000000000000000010101101 ;
b00000000000000000000000010101101 ?
b00000000000000000000000000000010 F
b0000001010110100 a
b11111110010100100001010011100011 b
b00000000000000000000000000000000 c
b00000000000000000000000000000010 e
b00000000000000000000001010110100 q
b00000000000000000000001010111000 r
b00000000000000000000001010111000 s
b1100011 t
b00100 u
b00101 v
b01001 w
b001 x
b1111111 y
b00000000000000000000000000000001 z
0{
b1010 !!
0"!
b11111111111111111111111111101000 #!
b010 $!
b0000000010101101 '!
1)!
b00000000000000000000001010110000 ?!
b00101 A!
b00000000000000000000000000000010 B!
b00000000000000000000000000000000 H!
#1935
0)!
#1939
#1940
b00000000000000000000001010110100 )
b01001 *
0+
b00000000000000000000000000000000 ,
b0000000010101110 :
b000000000000000010101110 ;
b00000000000000000000000010101110 ?
b0000001010111000 a
b11111111000000000000001110110111 b
b00000000000000000000000000000000 e
b00000000000000000000001010111000 q
b00000000000000000000001010111100 r
b00000000000000000000001010111100 s
b0110111 t
b00000 u
b10000 v
b00111 w
b000 x
b00000000000000000000000000000000 z
1{
b11 |
b00000000000000000000000000000000 }
b0000 !!
b11111111000000000000000000000000 #!
b011 $!
b0000000010101110 '!
1)!
b00000000000000000000001010110100 ?!
0@!
b01001 A!
b00000000000000000000000000000000 B!
b11111111000000000000000000000000 H!
#1945
0)!
#1949
#1950
b00000000000000000000001010111000 )
b00111 *
1+
b11111111000000000000000000000000 ,
b0000000010101111 :
b000000000000000010101111 ;
b00000000000000000000000010101111 ?
b11111111000000000000000000000000 H
b0000001010111100 a
b00000001100100000000000110010011 b
b00000000000000000000000000011001 c
b00000000000000000000001010111100 q
b00000000000000000000001011000000 r
b00000000000000000000001011000000 s
b0010011 t
b11001 v
b00011 w
b0000000 y
b00 |
b00000000000000000000000000011001 }
1"!
b00000000000000000000000000011001 #!
b000 $!
b0000000000000110 &!
b0000000010101111 '!
1)!
b00000000000000000000001010111000 ?!
1@!
b00111 A!
b11111111000000000000000000000000 B!
b01011000011101110001010001100011 C!
b01011000011101110001010001100011 D!
b00000000000000000000000000011001 H!
#1955
0)!
