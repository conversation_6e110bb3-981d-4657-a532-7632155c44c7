// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Tracing implementation internals
#include "verilated_vcd_c.h"
#include "VminiRV_SoC__Syms.h"


void VminiRV_SoC___024root__trace_chg_sub_0(VminiRV_SoC___024root* vlSelf, VerilatedVcd* tracep);

void VminiRV_SoC___024root__trace_chg_top_0(void* voidSelf, VerilatedVcd* tracep) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VminiRV_SoC___024root__trace_chg_top_0\n"); );
    // Init
    VminiRV_SoC___024root* const __restrict vlSelf VL_ATTR_UNUSED = static_cast<VminiRV_SoC___024root*>(voidSelf);
    VminiRV_SoC__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    if (VL_UNLIKELY(!vlSymsp->__Vm_activity)) return;
    // Body
    VminiRV_SoC___024root__trace_chg_sub_0((&vlSymsp->TOP), tracep);
}

void VminiRV_SoC___024root__trace_chg_sub_0(VminiRV_SoC___024root* vlSelf, VerilatedVcd* tracep) {
    if (false && vlSelf) {}  // Prevent unused
    VminiRV_SoC__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    VL_DEBUG_IF(VL_DBG_MSGF("+    VminiRV_SoC___024root__trace_chg_sub_0\n"); );
    // Init
    uint32_t* const oldp VL_ATTR_UNUSED = tracep->oldp(vlSymsp->__Vm_baseCode + 1);
    // Body
    if (VL_UNLIKELY(vlSelf->__Vm_traceActivity[0U])) {
        tracep->chgIData(oldp+0,(vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__i),32);
        tracep->chgIData(oldp+1,(vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__j),32);
        tracep->chgIData(oldp+2,(vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__mem_file),32);
        tracep->chgIData(oldp+3,(vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__i),32);
        tracep->chgIData(oldp+4,(vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__j),32);
        tracep->chgIData(oldp+5,(vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__mem_file),32);
    }
    if (VL_UNLIKELY(vlSelf->__Vm_traceActivity[1U])) {
        tracep->chgIData(oldp+6,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_pc),32);
        tracep->chgCData(oldp+7,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_rd),5);
        tracep->chgBit(oldp+8,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_rf_we));
        tracep->chgIData(oldp+9,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_rf_wD),32);
        tracep->chgBit(oldp+10,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__have_prev_inst));
        tracep->chgCData(oldp+11,(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_sync1),5);
        tracep->chgCData(oldp+12,(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_sync2),5);
        tracep->chgCData(oldp+13,(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_stable),5);
        tracep->chgIData(oldp+14,(vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[0]),20);
        tracep->chgIData(oldp+15,(vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[1]),20);
        tracep->chgIData(oldp+16,(vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[2]),20);
        tracep->chgIData(oldp+17,(vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[3]),20);
        tracep->chgIData(oldp+18,(vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[4]),20);
        tracep->chgIData(oldp+19,(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data),32);
        tracep->chgIData(oldp+20,(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__pending_data),32);
        tracep->chgBit(oldp+21,(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__data_pending));
        tracep->chgCData(oldp+22,(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt),3);
        tracep->chgSData(oldp+23,(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_clk_cnt),16);
        tracep->chgIData(oldp+24,(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__update_limit_cnt),24);
        tracep->chgCData(oldp+25,((0xfU & ((0U == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                            ? vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data
                                            : ((1U 
                                                == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                                ? (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                   >> 4U)
                                                : (
                                                   (2U 
                                                    == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                                    ? 
                                                   (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                    >> 8U)
                                                    : 
                                                   ((3U 
                                                     == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                                     ? 
                                                    (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                     >> 0xcU)
                                                     : 
                                                    ((4U 
                                                      == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                                      ? 
                                                     (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                      >> 0x10U)
                                                      : 
                                                     ((5U 
                                                       == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                                       ? 
                                                      (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                       >> 0x14U)
                                                       : 
                                                      ((6U 
                                                        == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                                        ? 
                                                       (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                        >> 0x18U)
                                                        : 
                                                       (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                        >> 0x1cU)))))))))),4);
        tracep->chgCData(oldp+26,(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__seg_code),7);
        tracep->chgIData(oldp+27,(vlSelf->miniRV_SoC__DOT__U_Timer__DOT__counter0),32);
        tracep->chgIData(oldp+28,(vlSelf->miniRV_SoC__DOT__U_Timer__DOT__counter1),32);
        tracep->chgIData(oldp+29,(vlSelf->miniRV_SoC__DOT__U_Timer__DOT__threshold),32);
    }
    if (VL_UNLIKELY(vlSelf->__Vm_traceActivity[2U])) {
        tracep->chgIData(oldp+30,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[0]),32);
        tracep->chgIData(oldp+31,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[1]),32);
        tracep->chgIData(oldp+32,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[2]),32);
        tracep->chgIData(oldp+33,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[3]),32);
        tracep->chgIData(oldp+34,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[4]),32);
        tracep->chgIData(oldp+35,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[5]),32);
        tracep->chgIData(oldp+36,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[6]),32);
        tracep->chgIData(oldp+37,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[7]),32);
        tracep->chgIData(oldp+38,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[8]),32);
        tracep->chgIData(oldp+39,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[9]),32);
        tracep->chgIData(oldp+40,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[10]),32);
        tracep->chgIData(oldp+41,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[11]),32);
        tracep->chgIData(oldp+42,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[12]),32);
        tracep->chgIData(oldp+43,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[13]),32);
        tracep->chgIData(oldp+44,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[14]),32);
        tracep->chgIData(oldp+45,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[15]),32);
        tracep->chgIData(oldp+46,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[16]),32);
        tracep->chgIData(oldp+47,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[17]),32);
        tracep->chgIData(oldp+48,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[18]),32);
        tracep->chgIData(oldp+49,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[19]),32);
        tracep->chgIData(oldp+50,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[20]),32);
        tracep->chgIData(oldp+51,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[21]),32);
        tracep->chgIData(oldp+52,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[22]),32);
        tracep->chgIData(oldp+53,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[23]),32);
        tracep->chgIData(oldp+54,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[24]),32);
        tracep->chgIData(oldp+55,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[25]),32);
        tracep->chgIData(oldp+56,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[26]),32);
        tracep->chgIData(oldp+57,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[27]),32);
        tracep->chgIData(oldp+58,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[28]),32);
        tracep->chgIData(oldp+59,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[29]),32);
        tracep->chgIData(oldp+60,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[30]),32);
        tracep->chgIData(oldp+61,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[31]),32);
    }
    if (VL_UNLIKELY(vlSelf->__Vm_traceActivity[3U])) {
        tracep->chgSData(oldp+62,((0xffffU & vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current)),16);
        tracep->chgIData(oldp+63,(vlSelf->miniRV_SoC__DOT__inst),32);
        tracep->chgIData(oldp+64,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C),32);
        tracep->chgBit(oldp+65,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__ram_we));
        tracep->chgIData(oldp+66,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD2),32);
        tracep->chgBit(oldp+67,(((IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__ram_we) 
                                 & (0xfffffU != (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C 
                                                 >> 0xcU)))));
        tracep->chgBit(oldp+68,(vlSelf->miniRV_SoC__DOT__we_bridge2dig));
        tracep->chgBit(oldp+69,(vlSelf->miniRV_SoC__DOT__we_bridge2tim));
        tracep->chgBit(oldp+70,(((IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__ram_we) 
                                 & (0xfffff060U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C))));
        tracep->chgBit(oldp+71,((0xfffffU != (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C 
                                              >> 0xcU))));
        tracep->chgBit(oldp+72,((0xfffff000U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)));
        tracep->chgBit(oldp+73,(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_tim));
        tracep->chgBit(oldp+74,((0xfffff060U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)));
        tracep->chgBit(oldp+75,((0xfffff070U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)));
        tracep->chgBit(oldp+76,((0xfffff078U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)));
        tracep->chgCData(oldp+77,(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit),6);
        tracep->chgIData(oldp+78,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current),32);
        tracep->chgIData(oldp+79,(((0U == (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op))
                                    ? ((IData)(4U) 
                                       + vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current)
                                    : ((1U == (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op))
                                        ? (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current 
                                           + vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_ext)
                                        : ((2U == (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op))
                                            ? (0xfffffffeU 
                                               & vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                            : ((IData)(4U) 
                                               + vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current))))),32);
        tracep->chgIData(oldp+80,(((IData)(4U) + vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current)),32);
        tracep->chgCData(oldp+81,((0x7fU & vlSelf->miniRV_SoC__DOT__inst)),7);
        tracep->chgCData(oldp+82,((0x1fU & (vlSelf->miniRV_SoC__DOT__inst 
                                            >> 0xfU))),5);
        tracep->chgCData(oldp+83,((0x1fU & (vlSelf->miniRV_SoC__DOT__inst 
                                            >> 0x14U))),5);
        tracep->chgCData(oldp+84,((0x1fU & (vlSelf->miniRV_SoC__DOT__inst 
                                            >> 7U))),5);
        tracep->chgCData(oldp+85,((7U & (vlSelf->miniRV_SoC__DOT__inst 
                                         >> 0xcU))),3);
        tracep->chgCData(oldp+86,((vlSelf->miniRV_SoC__DOT__inst 
                                   >> 0x19U)),7);
        tracep->chgIData(oldp+87,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1),32);
        tracep->chgBit(oldp+88,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_we));
        tracep->chgCData(oldp+89,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel),2);
        tracep->chgIData(oldp+90,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B),32);
        tracep->chgBit(oldp+91,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_f));
        tracep->chgCData(oldp+92,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op),4);
        tracep->chgBit(oldp+93,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alub_sel));
        tracep->chgIData(oldp+94,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_ext),32);
        tracep->chgCData(oldp+95,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op),3);
        tracep->chgCData(oldp+96,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op),2);
        tracep->chgSData(oldp+97,((0x3fffU & (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C 
                                              >> 2U))),16);
        tracep->chgSData(oldp+98,((0x3fffU & (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current 
                                              >> 2U))),16);
    }
    tracep->chgBit(oldp+99,(vlSelf->fpga_rst));
    tracep->chgBit(oldp+100,(vlSelf->fpga_clk));
    tracep->chgSData(oldp+101,(vlSelf->sw),16);
    tracep->chgCData(oldp+102,(vlSelf->button),5);
    tracep->chgCData(oldp+103,(vlSelf->dig_en),8);
    tracep->chgBit(oldp+104,(vlSelf->DN_A0));
    tracep->chgBit(oldp+105,(vlSelf->DN_A1));
    tracep->chgBit(oldp+106,(vlSelf->DN_B0));
    tracep->chgBit(oldp+107,(vlSelf->DN_B1));
    tracep->chgBit(oldp+108,(vlSelf->DN_C0));
    tracep->chgBit(oldp+109,(vlSelf->DN_C1));
    tracep->chgBit(oldp+110,(vlSelf->DN_D0));
    tracep->chgBit(oldp+111,(vlSelf->DN_D1));
    tracep->chgBit(oldp+112,(vlSelf->DN_E0));
    tracep->chgBit(oldp+113,(vlSelf->DN_E1));
    tracep->chgBit(oldp+114,(vlSelf->DN_F0));
    tracep->chgBit(oldp+115,(vlSelf->DN_F1));
    tracep->chgBit(oldp+116,(vlSelf->DN_G0));
    tracep->chgBit(oldp+117,(vlSelf->DN_G1));
    tracep->chgBit(oldp+118,(vlSelf->DN_DP0));
    tracep->chgBit(oldp+119,(vlSelf->DN_DP1));
    tracep->chgSData(oldp+120,(vlSelf->led),16);
    tracep->chgBit(oldp+121,(vlSelf->debug_wb_have_inst));
    tracep->chgIData(oldp+122,(vlSelf->debug_wb_pc),32);
    tracep->chgBit(oldp+123,(vlSelf->debug_wb_ena));
    tracep->chgCData(oldp+124,(vlSelf->debug_wb_reg),5);
    tracep->chgIData(oldp+125,(vlSelf->debug_wb_value),32);
    tracep->chgIData(oldp+126,(((0x20U & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                 ? vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__mem
                                [(0x3fffU & (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C 
                                             >> 2U))]
                                 : ((0x10U & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                     ? 0xffffffffU : 
                                    ((8U & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                      ? ((4U & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                          ? 0xffffffffU
                                          : ((2U & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                              ? 0xffffffffU
                                              : ((1U 
                                                  & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                                  ? 0xffffffffU
                                                  : 
                                                 ((0xfffff020U 
                                                   == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                                   ? vlSelf->miniRV_SoC__DOT__U_Timer__DOT__counter0
                                                   : 
                                                  ((0xfffff024U 
                                                    == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                                    ? vlSelf->miniRV_SoC__DOT__U_Timer__DOT__threshold
                                                    : 0U)))))
                                      : ((4U & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                          ? 0xffffffffU
                                          : ((2U & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                              ? ((1U 
                                                  & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                                  ? 0xffffffffU
                                                  : 
                                                 ((0xfffff070U 
                                                   == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                                   ? (IData)(vlSelf->sw)
                                                   : 0U))
                                              : ((1U 
                                                  & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                                  ? 
                                                 ((0xfffff078U 
                                                   == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                                   ? (IData)(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_stable)
                                                   : 0U)
                                                  : 0xffffffffU))))))),32);
    tracep->chgIData(oldp+127,(vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__mem
                               [(0x3fffU & (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C 
                                            >> 2U))]),32);
    tracep->chgIData(oldp+128,(((0xfffff020U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                 ? vlSelf->miniRV_SoC__DOT__U_Timer__DOT__counter0
                                 : ((0xfffff024U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                     ? vlSelf->miniRV_SoC__DOT__U_Timer__DOT__threshold
                                     : 0U))),32);
    tracep->chgIData(oldp+129,(((0xfffff070U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                 ? (IData)(vlSelf->sw)
                                 : 0U)),32);
    tracep->chgIData(oldp+130,(((0xfffff078U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                 ? (IData)(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_stable)
                                 : 0U)),32);
    tracep->chgIData(oldp+131,(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wD),32);
}

void VminiRV_SoC___024root__trace_cleanup(void* voidSelf, VerilatedVcd* /*unused*/) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VminiRV_SoC___024root__trace_cleanup\n"); );
    // Init
    VminiRV_SoC___024root* const __restrict vlSelf VL_ATTR_UNUSED = static_cast<VminiRV_SoC___024root*>(voidSelf);
    VminiRV_SoC__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    // Body
    vlSymsp->__Vm_activity = false;
    vlSymsp->TOP.__Vm_traceActivity[0U] = 0U;
    vlSymsp->TOP.__Vm_traceActivity[1U] = 0U;
    vlSymsp->TOP.__Vm_traceActivity[2U] = 0U;
    vlSymsp->TOP.__Vm_traceActivity[3U] = 0U;
}
