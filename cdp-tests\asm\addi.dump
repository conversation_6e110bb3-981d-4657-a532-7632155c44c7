
addi:     file format elf32-littleriscv


Disassembly of section .text.init:

00000000 <_start>:
   0:	0040006f          	jal	x0,4 <reset_vector>

00000004 <reset_vector>:
   4:	00000093          	addi	x1,x0,0
   8:	00008713          	addi	x14,x1,0
   c:	00000393          	addi	x7,x0,0
  10:	00200193          	addi	x3,x0,2
  14:	26771c63          	bne	x14,x7,28c <fail>

00000018 <test_3>:
  18:	00100093          	addi	x1,x0,1
  1c:	00108713          	addi	x14,x1,1
  20:	00200393          	addi	x7,x0,2
  24:	00300193          	addi	x3,x0,3
  28:	26771263          	bne	x14,x7,28c <fail>

0000002c <test_4>:
  2c:	00300093          	addi	x1,x0,3
  30:	00708713          	addi	x14,x1,7
  34:	00a00393          	addi	x7,x0,10
  38:	00400193          	addi	x3,x0,4
  3c:	24771863          	bne	x14,x7,28c <fail>

00000040 <test_5>:
  40:	00000093          	addi	x1,x0,0
  44:	80008713          	addi	x14,x1,-2048
  48:	80000393          	addi	x7,x0,-2048
  4c:	00500193          	addi	x3,x0,5
  50:	22771e63          	bne	x14,x7,28c <fail>

00000054 <test_6>:
  54:	800000b7          	lui	x1,0x80000
  58:	00008713          	addi	x14,x1,0 # 80000000 <_end+0x7fffe000>
  5c:	800003b7          	lui	x7,0x80000
  60:	00600193          	addi	x3,x0,6
  64:	22771463          	bne	x14,x7,28c <fail>

00000068 <test_7>:
  68:	800000b7          	lui	x1,0x80000
  6c:	80008713          	addi	x14,x1,-2048 # 7ffff800 <_end+0x7fffd800>
  70:	800003b7          	lui	x7,0x80000
  74:	80038393          	addi	x7,x7,-2048 # 7ffff800 <_end+0x7fffd800>
  78:	00700193          	addi	x3,x0,7
  7c:	20771863          	bne	x14,x7,28c <fail>

00000080 <test_8>:
  80:	00000093          	addi	x1,x0,0
  84:	7ff08713          	addi	x14,x1,2047
  88:	7ff00393          	addi	x7,x0,2047
  8c:	00800193          	addi	x3,x0,8
  90:	1e771e63          	bne	x14,x7,28c <fail>

00000094 <test_9>:
  94:	800000b7          	lui	x1,0x80000
  98:	fff08093          	addi	x1,x1,-1 # 7fffffff <_end+0x7fffdfff>
  9c:	00008713          	addi	x14,x1,0
  a0:	800003b7          	lui	x7,0x80000
  a4:	fff38393          	addi	x7,x7,-1 # 7fffffff <_end+0x7fffdfff>
  a8:	00900193          	addi	x3,x0,9
  ac:	1e771063          	bne	x14,x7,28c <fail>

000000b0 <test_10>:
  b0:	800000b7          	lui	x1,0x80000
  b4:	fff08093          	addi	x1,x1,-1 # 7fffffff <_end+0x7fffdfff>
  b8:	7ff08713          	addi	x14,x1,2047
  bc:	800003b7          	lui	x7,0x80000
  c0:	7fe38393          	addi	x7,x7,2046 # 800007fe <_end+0x7fffe7fe>
  c4:	00a00193          	addi	x3,x0,10
  c8:	1c771263          	bne	x14,x7,28c <fail>

000000cc <test_11>:
  cc:	800000b7          	lui	x1,0x80000
  d0:	7ff08713          	addi	x14,x1,2047 # 800007ff <_end+0x7fffe7ff>
  d4:	800003b7          	lui	x7,0x80000
  d8:	7ff38393          	addi	x7,x7,2047 # 800007ff <_end+0x7fffe7ff>
  dc:	00b00193          	addi	x3,x0,11
  e0:	1a771663          	bne	x14,x7,28c <fail>

000000e4 <test_12>:
  e4:	800000b7          	lui	x1,0x80000
  e8:	fff08093          	addi	x1,x1,-1 # 7fffffff <_end+0x7fffdfff>
  ec:	80008713          	addi	x14,x1,-2048
  f0:	7ffff3b7          	lui	x7,0x7ffff
  f4:	7ff38393          	addi	x7,x7,2047 # 7ffff7ff <_end+0x7fffd7ff>
  f8:	00c00193          	addi	x3,x0,12
  fc:	18771863          	bne	x14,x7,28c <fail>

00000100 <test_13>:
 100:	00000093          	addi	x1,x0,0
 104:	fff08713          	addi	x14,x1,-1
 108:	fff00393          	addi	x7,x0,-1
 10c:	00d00193          	addi	x3,x0,13
 110:	16771e63          	bne	x14,x7,28c <fail>

00000114 <test_14>:
 114:	fff00093          	addi	x1,x0,-1
 118:	00108713          	addi	x14,x1,1
 11c:	00000393          	addi	x7,x0,0
 120:	00e00193          	addi	x3,x0,14
 124:	16771463          	bne	x14,x7,28c <fail>

00000128 <test_15>:
 128:	fff00093          	addi	x1,x0,-1
 12c:	fff08713          	addi	x14,x1,-1
 130:	ffe00393          	addi	x7,x0,-2
 134:	00f00193          	addi	x3,x0,15
 138:	14771a63          	bne	x14,x7,28c <fail>

0000013c <test_16>:
 13c:	800000b7          	lui	x1,0x80000
 140:	fff08093          	addi	x1,x1,-1 # 7fffffff <_end+0x7fffdfff>
 144:	00108713          	addi	x14,x1,1
 148:	800003b7          	lui	x7,0x80000
 14c:	01000193          	addi	x3,x0,16
 150:	12771e63          	bne	x14,x7,28c <fail>

00000154 <test_17>:
 154:	00d00093          	addi	x1,x0,13
 158:	00b08093          	addi	x1,x1,11
 15c:	01800393          	addi	x7,x0,24
 160:	01100193          	addi	x3,x0,17
 164:	12709463          	bne	x1,x7,28c <fail>

00000168 <test_18>:
 168:	00000213          	addi	x4,x0,0
 16c:	00d00093          	addi	x1,x0,13
 170:	00b08713          	addi	x14,x1,11
 174:	00070313          	addi	x6,x14,0
 178:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 17c:	00200293          	addi	x5,x0,2
 180:	fe5216e3          	bne	x4,x5,16c <test_18+0x4>
 184:	01800393          	addi	x7,x0,24
 188:	01200193          	addi	x3,x0,18
 18c:	10731063          	bne	x6,x7,28c <fail>

00000190 <test_19>:
 190:	00000213          	addi	x4,x0,0
 194:	00d00093          	addi	x1,x0,13
 198:	00a08713          	addi	x14,x1,10
 19c:	00000013          	addi	x0,x0,0
 1a0:	00070313          	addi	x6,x14,0
 1a4:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 1a8:	00200293          	addi	x5,x0,2
 1ac:	fe5214e3          	bne	x4,x5,194 <test_19+0x4>
 1b0:	01700393          	addi	x7,x0,23
 1b4:	01300193          	addi	x3,x0,19
 1b8:	0c731a63          	bne	x6,x7,28c <fail>

000001bc <test_20>:
 1bc:	00000213          	addi	x4,x0,0
 1c0:	00d00093          	addi	x1,x0,13
 1c4:	00908713          	addi	x14,x1,9
 1c8:	00000013          	addi	x0,x0,0
 1cc:	00000013          	addi	x0,x0,0
 1d0:	00070313          	addi	x6,x14,0
 1d4:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 1d8:	00200293          	addi	x5,x0,2
 1dc:	fe5212e3          	bne	x4,x5,1c0 <test_20+0x4>
 1e0:	01600393          	addi	x7,x0,22
 1e4:	01400193          	addi	x3,x0,20
 1e8:	0a731263          	bne	x6,x7,28c <fail>

000001ec <test_21>:
 1ec:	00000213          	addi	x4,x0,0
 1f0:	00d00093          	addi	x1,x0,13
 1f4:	00b08713          	addi	x14,x1,11
 1f8:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 1fc:	00200293          	addi	x5,x0,2
 200:	fe5218e3          	bne	x4,x5,1f0 <test_21+0x4>
 204:	01800393          	addi	x7,x0,24
 208:	01500193          	addi	x3,x0,21
 20c:	08771063          	bne	x14,x7,28c <fail>

00000210 <test_22>:
 210:	00000213          	addi	x4,x0,0
 214:	00d00093          	addi	x1,x0,13
 218:	00000013          	addi	x0,x0,0
 21c:	00a08713          	addi	x14,x1,10
 220:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 224:	00200293          	addi	x5,x0,2
 228:	fe5216e3          	bne	x4,x5,214 <test_22+0x4>
 22c:	01700393          	addi	x7,x0,23
 230:	01600193          	addi	x3,x0,22
 234:	04771c63          	bne	x14,x7,28c <fail>

00000238 <test_23>:
 238:	00000213          	addi	x4,x0,0
 23c:	00d00093          	addi	x1,x0,13
 240:	00000013          	addi	x0,x0,0
 244:	00000013          	addi	x0,x0,0
 248:	00908713          	addi	x14,x1,9
 24c:	00120213          	addi	x4,x4,1 # 1 <_start+0x1>
 250:	00200293          	addi	x5,x0,2
 254:	fe5214e3          	bne	x4,x5,23c <test_23+0x4>
 258:	01600393          	addi	x7,x0,22
 25c:	01700193          	addi	x3,x0,23
 260:	02771663          	bne	x14,x7,28c <fail>

00000264 <test_24>:
 264:	02000093          	addi	x1,x0,32
 268:	02000393          	addi	x7,x0,32
 26c:	01800193          	addi	x3,x0,24
 270:	00709e63          	bne	x1,x7,28c <fail>

00000274 <test_25>:
 274:	02100093          	addi	x1,x0,33
 278:	03208013          	addi	x0,x1,50
 27c:	00000393          	addi	x7,x0,0
 280:	01900193          	addi	x3,x0,25
 284:	00701463          	bne	x0,x7,28c <fail>
 288:	00301e63          	bne	x0,x3,2a4 <pass>

0000028c <fail>:
 28c:	00018063          	beq	x3,x0,28c <fail>
 290:	00119193          	slli	x3,x3,0x1
 294:	0011e193          	ori	x3,x3,1
 298:	05d00893          	addi	x17,x0,93
 29c:	00018513          	addi	x10,x3,0
 2a0:	00000073          	ecall

000002a4 <pass>:
 2a4:	00100193          	addi	x3,x0,1
 2a8:	05d00893          	addi	x17,x0,93
 2ac:	00000513          	addi	x10,x0,0
 2b0:	00000073          	ecall
 2b4:	c0001073          	unimp
 2b8:	0000                	c.unimp
 2ba:	0000                	c.unimp
 2bc:	0000                	c.unimp
 2be:	0000                	c.unimp
 2c0:	0000                	c.unimp
 2c2:	0000                	c.unimp
