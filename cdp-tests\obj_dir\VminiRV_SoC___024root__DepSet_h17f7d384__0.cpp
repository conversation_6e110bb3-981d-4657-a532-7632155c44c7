// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design implementation internals
// See VminiRV_SoC.h for the primary calling header

#include "verilated.h"

#include "VminiRV_SoC___024root.h"

extern const VlUnpacked<CData/*6:0*/, 16> VminiRV_SoC__ConstPool__TABLE_h122acf83_0;
extern const VlUnpacked<CData/*0:0*/, 128> VminiRV_SoC__ConstPool__TABLE_hdd9a1d96_0;
extern const VlUnpacked<CData/*0:0*/, 128> VminiRV_SoC__ConstPool__TABLE_ha8b9bce6_0;
extern const VlUnpacked<CData/*0:0*/, 128> VminiRV_SoC__ConstPool__TABLE_hec3cfdef_0;
extern const VlUnpacked<CData/*0:0*/, 128> VminiRV_SoC__ConstPool__TABLE_h264d9dbb_0;
extern const VlUnpacked<CData/*0:0*/, 128> VminiRV_SoC__ConstPool__TABLE_h19c0cfb1_0;
extern const VlUnpacked<CData/*0:0*/, 128> VminiRV_SoC__ConstPool__TABLE_hccebc244_0;
extern const VlUnpacked<CData/*0:0*/, 128> VminiRV_SoC__ConstPool__TABLE_hbe5b3f5b_0;
extern const VlUnpacked<CData/*0:0*/, 128> VminiRV_SoC__ConstPool__TABLE_hc9e0ac50_0;

VL_INLINE_OPT void VminiRV_SoC___024root___sequent__TOP__0(VminiRV_SoC___024root* vlSelf) {
    if (false && vlSelf) {}  // Prevent unused
    VminiRV_SoC__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    VL_DEBUG_IF(VL_DBG_MSGF("+    VminiRV_SoC___024root___sequent__TOP__0\n"); );
    // Init
    CData/*3:0*/ __Vtableidx1;
    CData/*6:0*/ __Vtableidx2;
    CData/*0:0*/ __Vdly__miniRV_SoC__DOT__U_Dig__DOT__data_pending;
    IData/*23:0*/ __Vdly__miniRV_SoC__DOT__U_Dig__DOT__update_limit_cnt;
    SData/*15:0*/ __Vdly__miniRV_SoC__DOT__U_Dig__DOT__scan_clk_cnt;
    CData/*2:0*/ __Vdly__miniRV_SoC__DOT__U_Dig__DOT__scan_cnt;
    IData/*31:0*/ __Vdly__miniRV_SoC__DOT__U_Timer__DOT__counter0;
    IData/*31:0*/ __Vdly__miniRV_SoC__DOT__U_Timer__DOT__counter1;
    CData/*0:0*/ __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v0;
    CData/*4:0*/ __Vdly__miniRV_SoC__DOT__U_Button__DOT__button_stable;
    CData/*0:0*/ __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v1;
    IData/*19:0*/ __Vdlyvval__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v2;
    CData/*0:0*/ __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v2;
    CData/*0:0*/ __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v3;
    CData/*0:0*/ __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v4;
    CData/*0:0*/ __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v5;
    IData/*19:0*/ __Vdlyvval__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v6;
    CData/*0:0*/ __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v6;
    CData/*0:0*/ __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v7;
    CData/*0:0*/ __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v8;
    CData/*0:0*/ __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v9;
    IData/*19:0*/ __Vdlyvval__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v10;
    CData/*0:0*/ __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v10;
    CData/*0:0*/ __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v11;
    CData/*0:0*/ __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v12;
    CData/*0:0*/ __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v13;
    IData/*19:0*/ __Vdlyvval__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v14;
    CData/*0:0*/ __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v14;
    CData/*0:0*/ __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v15;
    CData/*0:0*/ __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v16;
    CData/*0:0*/ __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v17;
    IData/*19:0*/ __Vdlyvval__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v18;
    CData/*0:0*/ __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v18;
    CData/*0:0*/ __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v19;
    // Body
    __Vdly__miniRV_SoC__DOT__U_Dig__DOT__scan_clk_cnt 
        = vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_clk_cnt;
    __Vdly__miniRV_SoC__DOT__U_Dig__DOT__scan_cnt = vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt;
    __Vdly__miniRV_SoC__DOT__U_Button__DOT__button_stable 
        = vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_stable;
    __Vdly__miniRV_SoC__DOT__U_Dig__DOT__update_limit_cnt 
        = vlSelf->miniRV_SoC__DOT__U_Dig__DOT__update_limit_cnt;
    __Vdly__miniRV_SoC__DOT__U_Dig__DOT__data_pending 
        = vlSelf->miniRV_SoC__DOT__U_Dig__DOT__data_pending;
    __Vdly__miniRV_SoC__DOT__U_Timer__DOT__counter1 
        = vlSelf->miniRV_SoC__DOT__U_Timer__DOT__counter1;
    __Vdly__miniRV_SoC__DOT__U_Timer__DOT__counter0 
        = vlSelf->miniRV_SoC__DOT__U_Timer__DOT__counter0;
    __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v0 = 0U;
    __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v1 = 0U;
    __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v2 = 0U;
    __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v3 = 0U;
    __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v4 = 0U;
    __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v5 = 0U;
    __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v6 = 0U;
    __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v7 = 0U;
    __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v8 = 0U;
    __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v9 = 0U;
    __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v10 = 0U;
    __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v11 = 0U;
    __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v12 = 0U;
    __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v13 = 0U;
    __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v14 = 0U;
    __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v15 = 0U;
    __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v16 = 0U;
    __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v17 = 0U;
    __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v18 = 0U;
    __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v19 = 0U;
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__have_prev_inst 
        = (1U & (~ (IData)(vlSelf->fpga_rst)));
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_rf_we 
        = ((~ (IData)(vlSelf->fpga_rst)) & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_we));
    if (vlSelf->fpga_rst) {
        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_rf_wD = 0U;
        __Vdly__miniRV_SoC__DOT__U_Dig__DOT__scan_clk_cnt = 0U;
        __Vdly__miniRV_SoC__DOT__U_Dig__DOT__scan_cnt = 0U;
        vlSelf->led = 0U;
        vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data = 0U;
        __Vdly__miniRV_SoC__DOT__U_Dig__DOT__data_pending = 0U;
        __Vdly__miniRV_SoC__DOT__U_Dig__DOT__update_limit_cnt = 0U;
        __Vdly__miniRV_SoC__DOT__U_Timer__DOT__counter0 = 0U;
        __Vdly__miniRV_SoC__DOT__U_Timer__DOT__counter1 = 0U;
        __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v0 = 1U;
        __Vdly__miniRV_SoC__DOT__U_Button__DOT__button_stable 
            = (0x1eU & (IData)(__Vdly__miniRV_SoC__DOT__U_Button__DOT__button_stable));
        __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v4 = 1U;
        __Vdly__miniRV_SoC__DOT__U_Button__DOT__button_stable 
            = (0x1dU & (IData)(__Vdly__miniRV_SoC__DOT__U_Button__DOT__button_stable));
        __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v8 = 1U;
        __Vdly__miniRV_SoC__DOT__U_Button__DOT__button_stable 
            = (0x1bU & (IData)(__Vdly__miniRV_SoC__DOT__U_Button__DOT__button_stable));
        __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v12 = 1U;
        __Vdly__miniRV_SoC__DOT__U_Button__DOT__button_stable 
            = (0x17U & (IData)(__Vdly__miniRV_SoC__DOT__U_Button__DOT__button_stable));
        __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v16 = 1U;
        __Vdly__miniRV_SoC__DOT__U_Button__DOT__button_stable 
            = (0xfU & (IData)(__Vdly__miniRV_SoC__DOT__U_Button__DOT__button_stable));
        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_rd = 0U;
        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_pc = 0U;
    } else {
        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_rf_wD 
            = vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wD;
        if ((0x61a8U <= (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_clk_cnt))) {
            __Vdly__miniRV_SoC__DOT__U_Dig__DOT__scan_cnt 
                = (7U & ((IData)(1U) + (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt)));
            __Vdly__miniRV_SoC__DOT__U_Dig__DOT__scan_clk_cnt = 0U;
        } else {
            __Vdly__miniRV_SoC__DOT__U_Dig__DOT__scan_clk_cnt 
                = (0xffffU & ((IData)(1U) + (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_clk_cnt)));
        }
        if (((IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__ram_we) 
             & (0xfffff060U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C))) {
            vlSelf->led = (0xffffU & vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD2);
        }
        if (((IData)(vlSelf->miniRV_SoC__DOT__we_bridge2dig) 
             & (0xfffff000U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C))) {
            __Vdly__miniRV_SoC__DOT__U_Dig__DOT__data_pending = 1U;
        }
        if ((0xbebc20U <= vlSelf->miniRV_SoC__DOT__U_Dig__DOT__update_limit_cnt)) {
            __Vdly__miniRV_SoC__DOT__U_Dig__DOT__update_limit_cnt = 0U;
            if (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__data_pending) {
                vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                    = vlSelf->miniRV_SoC__DOT__U_Dig__DOT__pending_data;
                __Vdly__miniRV_SoC__DOT__U_Dig__DOT__data_pending = 0U;
            }
        } else {
            __Vdly__miniRV_SoC__DOT__U_Dig__DOT__update_limit_cnt 
                = (0xffffffU & ((IData)(1U) + vlSelf->miniRV_SoC__DOT__U_Dig__DOT__update_limit_cnt));
        }
        if ((vlSelf->miniRV_SoC__DOT__U_Timer__DOT__counter1 
             >= (vlSelf->miniRV_SoC__DOT__U_Timer__DOT__threshold 
                 - (IData)(1U)))) {
            __Vdly__miniRV_SoC__DOT__U_Timer__DOT__counter0 
                = ((IData)(1U) + vlSelf->miniRV_SoC__DOT__U_Timer__DOT__counter0);
            __Vdly__miniRV_SoC__DOT__U_Timer__DOT__counter1 = 0U;
        } else {
            __Vdly__miniRV_SoC__DOT__U_Timer__DOT__counter1 
                = ((IData)(1U) + vlSelf->miniRV_SoC__DOT__U_Timer__DOT__counter1);
        }
        if (vlSelf->miniRV_SoC__DOT__we_bridge2tim) {
            if ((0xfffff020U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)) {
                __Vdly__miniRV_SoC__DOT__U_Timer__DOT__counter0 
                    = vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD2;
            }
        }
        if (((1U & (IData)(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_sync2)) 
             != (1U & (IData)(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_stable)))) {
            if ((0x5b8d8U <= vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt
                 [0U])) {
                __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v1 = 1U;
                __Vdly__miniRV_SoC__DOT__U_Button__DOT__button_stable 
                    = ((0x1eU & (IData)(__Vdly__miniRV_SoC__DOT__U_Button__DOT__button_stable)) 
                       | (1U & (IData)(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_sync2)));
            } else {
                __Vdlyvval__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v2 
                    = (0xfffffU & ((IData)(1U) + vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt
                                   [0U]));
                __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v2 = 1U;
            }
        } else {
            __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v3 = 1U;
        }
        if (((1U & ((IData)(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_sync2) 
                    >> 1U)) != (1U & ((IData)(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_stable) 
                                      >> 1U)))) {
            if ((0x5b8d8U <= vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt
                 [1U])) {
                __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v5 = 1U;
                __Vdly__miniRV_SoC__DOT__U_Button__DOT__button_stable 
                    = ((0x1dU & (IData)(__Vdly__miniRV_SoC__DOT__U_Button__DOT__button_stable)) 
                       | (2U & (IData)(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_sync2)));
            } else {
                __Vdlyvval__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v6 
                    = (0xfffffU & ((IData)(1U) + vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt
                                   [1U]));
                __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v6 = 1U;
            }
        } else {
            __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v7 = 1U;
        }
        if (((1U & ((IData)(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_sync2) 
                    >> 2U)) != (1U & ((IData)(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_stable) 
                                      >> 2U)))) {
            if ((0x5b8d8U <= vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt
                 [2U])) {
                __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v9 = 1U;
                __Vdly__miniRV_SoC__DOT__U_Button__DOT__button_stable 
                    = ((0x1bU & (IData)(__Vdly__miniRV_SoC__DOT__U_Button__DOT__button_stable)) 
                       | (4U & (IData)(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_sync2)));
            } else {
                __Vdlyvval__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v10 
                    = (0xfffffU & ((IData)(1U) + vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt
                                   [2U]));
                __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v10 = 1U;
            }
        } else {
            __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v11 = 1U;
        }
        if (((1U & ((IData)(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_sync2) 
                    >> 3U)) != (1U & ((IData)(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_stable) 
                                      >> 3U)))) {
            if ((0x5b8d8U <= vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt
                 [3U])) {
                __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v13 = 1U;
                __Vdly__miniRV_SoC__DOT__U_Button__DOT__button_stable 
                    = ((0x17U & (IData)(__Vdly__miniRV_SoC__DOT__U_Button__DOT__button_stable)) 
                       | (8U & (IData)(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_sync2)));
            } else {
                __Vdlyvval__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v14 
                    = (0xfffffU & ((IData)(1U) + vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt
                                   [3U]));
                __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v14 = 1U;
            }
        } else {
            __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v15 = 1U;
        }
        if (((1U & ((IData)(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_sync2) 
                    >> 4U)) != (1U & ((IData)(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_stable) 
                                      >> 4U)))) {
            if ((0x5b8d8U <= vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt
                 [4U])) {
                __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v17 = 1U;
                __Vdly__miniRV_SoC__DOT__U_Button__DOT__button_stable 
                    = ((0xfU & (IData)(__Vdly__miniRV_SoC__DOT__U_Button__DOT__button_stable)) 
                       | (0x10U & (IData)(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_sync2)));
            } else {
                __Vdlyvval__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v18 
                    = (0xfffffU & ((IData)(1U) + vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt
                                   [4U]));
                __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v18 = 1U;
            }
        } else {
            __Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v19 = 1U;
        }
        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_rd 
            = (0x1fU & (vlSelf->miniRV_SoC__DOT__inst 
                        >> 7U));
        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_pc 
            = vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current;
    }
    vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_clk_cnt 
        = __Vdly__miniRV_SoC__DOT__U_Dig__DOT__scan_clk_cnt;
    vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt = __Vdly__miniRV_SoC__DOT__U_Dig__DOT__scan_cnt;
    vlSelf->miniRV_SoC__DOT__U_Dig__DOT__data_pending 
        = __Vdly__miniRV_SoC__DOT__U_Dig__DOT__data_pending;
    vlSelf->miniRV_SoC__DOT__U_Dig__DOT__update_limit_cnt 
        = __Vdly__miniRV_SoC__DOT__U_Dig__DOT__update_limit_cnt;
    vlSelf->miniRV_SoC__DOT__U_Timer__DOT__counter1 
        = __Vdly__miniRV_SoC__DOT__U_Timer__DOT__counter1;
    vlSelf->miniRV_SoC__DOT__U_Timer__DOT__counter0 
        = __Vdly__miniRV_SoC__DOT__U_Timer__DOT__counter0;
    if (__Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v0) {
        vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[0U] = 0U;
    }
    if (__Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v1) {
        vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[0U] = 0U;
    }
    if (__Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v2) {
        vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[0U] 
            = __Vdlyvval__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v2;
    }
    if (__Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v3) {
        vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[0U] = 0U;
    }
    if (__Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v4) {
        vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[1U] = 0U;
    }
    if (__Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v5) {
        vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[1U] = 0U;
    }
    if (__Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v6) {
        vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[1U] 
            = __Vdlyvval__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v6;
    }
    if (__Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v7) {
        vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[1U] = 0U;
    }
    if (__Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v8) {
        vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[2U] = 0U;
    }
    if (__Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v9) {
        vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[2U] = 0U;
    }
    if (__Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v10) {
        vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[2U] 
            = __Vdlyvval__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v10;
    }
    if (__Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v11) {
        vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[2U] = 0U;
    }
    if (__Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v12) {
        vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[3U] = 0U;
    }
    if (__Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v13) {
        vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[3U] = 0U;
    }
    if (__Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v14) {
        vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[3U] 
            = __Vdlyvval__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v14;
    }
    if (__Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v15) {
        vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[3U] = 0U;
    }
    if (__Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v16) {
        vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[4U] = 0U;
    }
    if (__Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v17) {
        vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[4U] = 0U;
    }
    if (__Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v18) {
        vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[4U] 
            = __Vdlyvval__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v18;
    }
    if (__Vdlyvset__miniRV_SoC__DOT__U_Button__DOT__debounce_cnt__v19) {
        vlSelf->miniRV_SoC__DOT__U_Button__DOT__debounce_cnt[4U] = 0U;
    }
    vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_stable 
        = __Vdly__miniRV_SoC__DOT__U_Button__DOT__button_stable;
    vlSelf->debug_wb_have_inst = vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__have_prev_inst;
    vlSelf->debug_wb_ena = vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_rf_we;
    vlSelf->debug_wb_value = vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_rf_wD;
    vlSelf->dig_en = 0U;
    vlSelf->dig_en = ((IData)(vlSelf->dig_en) | (0xffU 
                                                 & ((IData)(1U) 
                                                    << (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))));
    __Vtableidx1 = (0xfU & ((0U == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                             ? vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data
                             : ((1U == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                 ? (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                    >> 4U) : ((2U == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                               ? (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                  >> 8U)
                                               : ((3U 
                                                   == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                                   ? 
                                                  (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                   >> 0xcU)
                                                   : 
                                                  ((4U 
                                                    == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                                    ? 
                                                   (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                    >> 0x10U)
                                                    : 
                                                   ((5U 
                                                     == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                                     ? 
                                                    (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                     >> 0x14U)
                                                     : 
                                                    ((6U 
                                                      == (IData)(vlSelf->miniRV_SoC__DOT__U_Dig__DOT__scan_cnt))
                                                      ? 
                                                     (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                      >> 0x18U)
                                                      : 
                                                     (vlSelf->miniRV_SoC__DOT__U_Dig__DOT__display_data 
                                                      >> 0x1cU)))))))));
    vlSelf->miniRV_SoC__DOT__U_Dig__DOT__seg_code = 
        VminiRV_SoC__ConstPool__TABLE_h122acf83_0[__Vtableidx1];
    if (vlSelf->fpga_rst) {
        vlSelf->miniRV_SoC__DOT__U_Dig__DOT__pending_data = 0U;
        vlSelf->miniRV_SoC__DOT__U_Timer__DOT__threshold = 0x17d7840U;
        vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_sync2 = 0U;
    } else {
        if (((IData)(vlSelf->miniRV_SoC__DOT__we_bridge2dig) 
             & (0xfffff000U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C))) {
            vlSelf->miniRV_SoC__DOT__U_Dig__DOT__pending_data 
                = vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD2;
        }
        if (vlSelf->miniRV_SoC__DOT__we_bridge2tim) {
            if ((0xfffff020U != vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)) {
                if ((0xfffff024U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)) {
                    vlSelf->miniRV_SoC__DOT__U_Timer__DOT__threshold 
                        = vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD2;
                }
            }
        }
        vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_sync2 
            = vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_sync1;
    }
    vlSelf->debug_wb_reg = vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_rd;
    vlSelf->debug_wb_pc = vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__prev_pc;
    __Vtableidx2 = vlSelf->miniRV_SoC__DOT__U_Dig__DOT__seg_code;
    vlSelf->DN_G0 = VminiRV_SoC__ConstPool__TABLE_hdd9a1d96_0
        [__Vtableidx2];
    vlSelf->DN_F0 = VminiRV_SoC__ConstPool__TABLE_ha8b9bce6_0
        [__Vtableidx2];
    vlSelf->DN_E0 = VminiRV_SoC__ConstPool__TABLE_hec3cfdef_0
        [__Vtableidx2];
    vlSelf->DN_D0 = VminiRV_SoC__ConstPool__TABLE_h264d9dbb_0
        [__Vtableidx2];
    vlSelf->DN_C0 = VminiRV_SoC__ConstPool__TABLE_h19c0cfb1_0
        [__Vtableidx2];
    vlSelf->DN_B0 = VminiRV_SoC__ConstPool__TABLE_hccebc244_0
        [__Vtableidx2];
    vlSelf->DN_A0 = VminiRV_SoC__ConstPool__TABLE_hbe5b3f5b_0
        [__Vtableidx2];
    vlSelf->DN_G1 = VminiRV_SoC__ConstPool__TABLE_hdd9a1d96_0
        [__Vtableidx2];
    vlSelf->DN_F1 = VminiRV_SoC__ConstPool__TABLE_ha8b9bce6_0
        [__Vtableidx2];
    vlSelf->DN_E1 = VminiRV_SoC__ConstPool__TABLE_hec3cfdef_0
        [__Vtableidx2];
    vlSelf->DN_D1 = VminiRV_SoC__ConstPool__TABLE_h264d9dbb_0
        [__Vtableidx2];
    vlSelf->DN_C1 = VminiRV_SoC__ConstPool__TABLE_h19c0cfb1_0
        [__Vtableidx2];
    vlSelf->DN_B1 = VminiRV_SoC__ConstPool__TABLE_hccebc244_0
        [__Vtableidx2];
    vlSelf->DN_A1 = VminiRV_SoC__ConstPool__TABLE_hbe5b3f5b_0
        [__Vtableidx2];
    vlSelf->DN_DP0 = VminiRV_SoC__ConstPool__TABLE_hc9e0ac50_0
        [__Vtableidx2];
    vlSelf->DN_DP1 = VminiRV_SoC__ConstPool__TABLE_hc9e0ac50_0
        [__Vtableidx2];
    vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_sync1 
        = ((IData)(vlSelf->fpga_rst) ? 0U : (IData)(vlSelf->button));
}

VL_INLINE_OPT void VminiRV_SoC___024root___sequent__TOP__1(VminiRV_SoC___024root* vlSelf) {
    if (false && vlSelf) {}  // Prevent unused
    VminiRV_SoC__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    VL_DEBUG_IF(VL_DBG_MSGF("+    VminiRV_SoC___024root___sequent__TOP__1\n"); );
    // Init
    CData/*4:0*/ __Vdlyvdim0__miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers__v0;
    IData/*31:0*/ __Vdlyvval__miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers__v0;
    CData/*0:0*/ __Vdlyvset__miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers__v0;
    IData/*19:0*/ __Vdlyvdim0__miniRV_SoC__DOT__Mem_DRAM__DOT__mem__v0;
    IData/*31:0*/ __Vdlyvval__miniRV_SoC__DOT__Mem_DRAM__DOT__mem__v0;
    CData/*0:0*/ __Vdlyvset__miniRV_SoC__DOT__Mem_DRAM__DOT__mem__v0;
    // Body
    __Vdlyvset__miniRV_SoC__DOT__Mem_DRAM__DOT__mem__v0 = 0U;
    __Vdlyvset__miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers__v0 = 0U;
    if (VL_UNLIKELY((1U & (~ (IData)(vlSelf->fpga_rst))))) {
        VL_WRITEF("DEBUG: PC=%x, inst=%x, rd=%2#, rf_we=%b, rf_wD=%x\n",
                  32,vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current,
                  32,vlSelf->miniRV_SoC__DOT__inst,
                  5,(0x1fU & (vlSelf->miniRV_SoC__DOT__inst 
                              >> 7U)),1,(IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_we),
                  32,vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wD);
    }
    if (((IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__ram_we) 
         & (0xfffffU != (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C 
                         >> 0xcU)))) {
        __Vdlyvval__miniRV_SoC__DOT__Mem_DRAM__DOT__mem__v0 
            = vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD2;
        __Vdlyvset__miniRV_SoC__DOT__Mem_DRAM__DOT__mem__v0 = 1U;
        __Vdlyvdim0__miniRV_SoC__DOT__Mem_DRAM__DOT__mem__v0 
            = (0x3fffU & (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C 
                          >> 2U));
    }
    if (((IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_we) 
         & (0U != (0x1fU & (vlSelf->miniRV_SoC__DOT__inst 
                            >> 7U))))) {
        __Vdlyvval__miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers__v0 
            = vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wD;
        __Vdlyvset__miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers__v0 = 1U;
        __Vdlyvdim0__miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers__v0 
            = (0x1fU & (vlSelf->miniRV_SoC__DOT__inst 
                        >> 7U));
    }
    if (__Vdlyvset__miniRV_SoC__DOT__Mem_DRAM__DOT__mem__v0) {
        vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__mem[__Vdlyvdim0__miniRV_SoC__DOT__Mem_DRAM__DOT__mem__v0] 
            = __Vdlyvval__miniRV_SoC__DOT__Mem_DRAM__DOT__mem__v0;
    }
    if (__Vdlyvset__miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers__v0) {
        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers[__Vdlyvdim0__miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers__v0] 
            = __Vdlyvval__miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers__v0;
    }
}

VL_INLINE_OPT void VminiRV_SoC___024root___sequent__TOP__2(VminiRV_SoC___024root* vlSelf) {
    if (false && vlSelf) {}  // Prevent unused
    VminiRV_SoC__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    VL_DEBUG_IF(VL_DBG_MSGF("+    VminiRV_SoC___024root___sequent__TOP__2\n"); );
    // Body
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current 
        = ((IData)(vlSelf->fpga_rst) ? 0U : vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_next);
    vlSelf->miniRV_SoC__DOT__inst = vlSelf->miniRV_SoC__DOT__Mem_IROM__DOT__mem
        [(0x3fffU & (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current 
                     >> 2U))];
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_we = 0U;
    if ((0x40U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((0x20U & vlSelf->miniRV_SoC__DOT__inst)) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 4U)))) {
                if ((8U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                            if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_we = 1U;
                            }
                        }
                    }
                } else if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_we = 1U;
                        }
                    }
                }
            }
        }
    } else if ((0x20U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((0x10U & vlSelf->miniRV_SoC__DOT__inst)) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 3U)))) {
                if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_we = 1U;
                        }
                    }
                } else if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_we = 1U;
                    }
                }
            }
        } else if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                             >> 3U)))) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 2U)))) {
                if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_we = 0U;
                    }
                }
            }
        }
    } else if ((0x10U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                      >> 3U)))) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 2U)))) {
                if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_we = 1U;
                    }
                }
            }
        }
    } else if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                         >> 3U)))) {
        if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                      >> 2U)))) {
            if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_we = 1U;
                }
            }
        }
    }
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__ram_we = 0U;
    if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst >> 6U)))) {
        if ((0x20U & vlSelf->miniRV_SoC__DOT__inst)) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 4U)))) {
                if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                              >> 3U)))) {
                    if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                                  >> 2U)))) {
                        if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                            if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__ram_we = 1U;
                            }
                        }
                    }
                }
            }
        }
    }
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel = 0U;
    if ((0x40U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((0x20U & vlSelf->miniRV_SoC__DOT__inst)) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 4U)))) {
                if ((8U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                            if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel = 2U;
                            }
                        }
                    }
                } else if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel = 2U;
                        }
                    }
                }
                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
                if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                              >> 3U)))) {
                    if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                            if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
                            }
                        }
                    } else if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op 
                                = ((0U == (7U & (vlSelf->miniRV_SoC__DOT__inst 
                                                 >> 0xcU)))
                                    ? 0xaU : ((4U == 
                                               (7U 
                                                & (vlSelf->miniRV_SoC__DOT__inst 
                                                   >> 0xcU)))
                                               ? 0xbU
                                               : 0xaU));
                        }
                    }
                }
            } else {
                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
            }
        } else {
            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
        }
    } else if ((0x20U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((0x10U & vlSelf->miniRV_SoC__DOT__inst)) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 3U)))) {
                if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel = 3U;
                        }
                    }
                } else if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel = 0U;
                    }
                }
                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
                if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                              >> 2U)))) {
                    if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op 
                                = ((0x4000U & vlSelf->miniRV_SoC__DOT__inst)
                                    ? ((0x2000U & vlSelf->miniRV_SoC__DOT__inst)
                                        ? ((0x1000U 
                                            & vlSelf->miniRV_SoC__DOT__inst)
                                            ? 2U : 3U)
                                        : ((0x1000U 
                                            & vlSelf->miniRV_SoC__DOT__inst)
                                            ? ((0x40000000U 
                                                & vlSelf->miniRV_SoC__DOT__inst)
                                                ? 7U
                                                : 6U)
                                            : 4U)) : 
                                   ((0x2000U & vlSelf->miniRV_SoC__DOT__inst)
                                     ? 0U : ((0x1000U 
                                              & vlSelf->miniRV_SoC__DOT__inst)
                                              ? 5U : 
                                             ((0x40000000U 
                                               & vlSelf->miniRV_SoC__DOT__inst)
                                               ? 1U
                                               : 0U))));
                        }
                    }
                }
            } else {
                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
            }
        } else {
            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 3U)))) {
                if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                              >> 2U)))) {
                    if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
                        }
                    }
                }
            }
        }
    } else if ((0x10U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                      >> 3U)))) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 2U)))) {
                if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel = 0U;
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op 
                            = ((0x4000U & vlSelf->miniRV_SoC__DOT__inst)
                                ? ((0x2000U & vlSelf->miniRV_SoC__DOT__inst)
                                    ? ((0x1000U & vlSelf->miniRV_SoC__DOT__inst)
                                        ? 2U : 3U) : 
                                   ((0x1000U & vlSelf->miniRV_SoC__DOT__inst)
                                     ? ((0x40000000U 
                                         & vlSelf->miniRV_SoC__DOT__inst)
                                         ? 7U : 6U)
                                     : 4U)) : ((0x2000U 
                                                & vlSelf->miniRV_SoC__DOT__inst)
                                                ? 0U
                                                : (
                                                   (0x1000U 
                                                    & vlSelf->miniRV_SoC__DOT__inst)
                                                    ? 5U
                                                    : 0U)));
                    } else {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
                    }
                } else {
                    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
                }
            } else {
                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
            }
        } else {
            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
        }
    } else if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                         >> 3U)))) {
        if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                      >> 2U)))) {
            if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel = 1U;
                    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
                    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
                } else {
                    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
                }
            } else {
                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
            }
        } else {
            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
        }
    } else {
        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op = 0U;
    }
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1 
        = ((0U == (0x1fU & (vlSelf->miniRV_SoC__DOT__inst 
                            >> 0xfU))) ? 0U : vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers
           [(0x1fU & (vlSelf->miniRV_SoC__DOT__inst 
                      >> 0xfU))]);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alub_sel = 0U;
    if ((0x40U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((0x20U & vlSelf->miniRV_SoC__DOT__inst)) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 4U)))) {
                if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                              >> 3U)))) {
                    if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                            if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alub_sel = 1U;
                            }
                        }
                    } else if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alub_sel = 0U;
                        }
                    }
                }
            }
        }
    } else if ((0x20U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((0x10U & vlSelf->miniRV_SoC__DOT__inst)) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 3U)))) {
                if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                              >> 2U)))) {
                    if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alub_sel = 0U;
                        }
                    }
                }
            }
        } else if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                             >> 3U)))) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 2U)))) {
                if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alub_sel = 1U;
                    }
                }
            }
        }
    } else if ((0x10U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                      >> 3U)))) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 2U)))) {
                if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alub_sel = 1U;
                    }
                }
            }
        }
    } else if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                         >> 3U)))) {
        if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                      >> 2U)))) {
            if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alub_sel = 1U;
                }
            }
        }
    }
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD2 
        = ((0U == (0x1fU & (vlSelf->miniRV_SoC__DOT__inst 
                            >> 0x14U))) ? 0U : vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__U_RF__DOT__registers
           [(0x1fU & (vlSelf->miniRV_SoC__DOT__inst 
                      >> 0x14U))]);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op = 0U;
    if ((0x40U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((0x20U & vlSelf->miniRV_SoC__DOT__inst)) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 4U)))) {
                if ((8U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                            if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op = 4U;
                            }
                        }
                    }
                } else if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op = 0U;
                        }
                    }
                } else if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op = 2U;
                    }
                }
            }
        }
    } else if ((0x20U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((0x10U & vlSelf->miniRV_SoC__DOT__inst)) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 3U)))) {
                if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op = 3U;
                        }
                    }
                }
            }
        } else if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                             >> 3U)))) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 2U)))) {
                if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op = 1U;
                    }
                }
            }
        }
    } else if ((0x10U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                      >> 3U)))) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 2U)))) {
                if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op 
                            = ((0x4000U & vlSelf->miniRV_SoC__DOT__inst)
                                ? ((0x2000U & vlSelf->miniRV_SoC__DOT__inst)
                                    ? 0U : ((0x1000U 
                                             & vlSelf->miniRV_SoC__DOT__inst)
                                             ? 5U : 0U))
                                : ((0x2000U & vlSelf->miniRV_SoC__DOT__inst)
                                    ? 0U : ((0x1000U 
                                             & vlSelf->miniRV_SoC__DOT__inst)
                                             ? 5U : 0U)));
                    }
                }
            }
        }
    } else if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                         >> 3U)))) {
        if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                      >> 2U)))) {
            if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op = 0U;
                }
            }
        }
    }
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_ext 
        = ((4U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op))
            ? ((2U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op))
                ? (((- (IData)((vlSelf->miniRV_SoC__DOT__inst 
                                >> 0x1fU))) << 0xcU) 
                   | (vlSelf->miniRV_SoC__DOT__inst 
                      >> 0x14U)) : ((1U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op))
                                     ? (0x1fU & (vlSelf->miniRV_SoC__DOT__inst 
                                                 >> 0x14U))
                                     : (((- (IData)(
                                                    (vlSelf->miniRV_SoC__DOT__inst 
                                                     >> 0x1fU))) 
                                         << 0x15U) 
                                        | ((0x100000U 
                                            & (vlSelf->miniRV_SoC__DOT__inst 
                                               >> 0xbU)) 
                                           | ((0xff000U 
                                               & vlSelf->miniRV_SoC__DOT__inst) 
                                              | ((0x800U 
                                                  & (vlSelf->miniRV_SoC__DOT__inst 
                                                     >> 9U)) 
                                                 | (0x7feU 
                                                    & (vlSelf->miniRV_SoC__DOT__inst 
                                                       >> 0x14U))))))))
            : ((2U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op))
                ? ((1U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op))
                    ? (0xfffff000U & vlSelf->miniRV_SoC__DOT__inst)
                    : (((- (IData)((vlSelf->miniRV_SoC__DOT__inst 
                                    >> 0x1fU))) << 0xdU) 
                       | ((0x1000U & (vlSelf->miniRV_SoC__DOT__inst 
                                      >> 0x13U)) | 
                          ((0x800U & (vlSelf->miniRV_SoC__DOT__inst 
                                      << 4U)) | ((0x7e0U 
                                                  & (vlSelf->miniRV_SoC__DOT__inst 
                                                     >> 0x14U)) 
                                                 | (0x1eU 
                                                    & (vlSelf->miniRV_SoC__DOT__inst 
                                                       >> 7U)))))))
                : ((1U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_op))
                    ? (((- (IData)((vlSelf->miniRV_SoC__DOT__inst 
                                    >> 0x1fU))) << 0xcU) 
                       | ((0xfe0U & (vlSelf->miniRV_SoC__DOT__inst 
                                     >> 0x14U)) | (0x1fU 
                                                   & (vlSelf->miniRV_SoC__DOT__inst 
                                                      >> 7U))))
                    : (((- (IData)((vlSelf->miniRV_SoC__DOT__inst 
                                    >> 0x1fU))) << 0xcU) 
                       | (vlSelf->miniRV_SoC__DOT__inst 
                          >> 0x14U)))));
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B = 
        ((IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alub_sel)
          ? vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_ext
          : vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD2);
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_f = (IData)(
                                                            ((0xaU 
                                                              == 
                                                              (0xeU 
                                                               & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))) 
                                                             & ((1U 
                                                                 & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
                                                                 ? 
                                                                VL_LTS_III(32, vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1, vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B)
                                                                 : 
                                                                (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1 
                                                                 == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B))));
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C = 
        ((8U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
          ? ((4U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
              ? (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1 
                 + vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B)
              : ((2U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
                  ? 0U : ((1U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
                           ? ((vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1 
                               < vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B)
                               ? 1U : 0U) : (VL_LTS_III(32, vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1, vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B)
                                              ? 1U : 0U))))
          : ((4U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
              ? ((2U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
                  ? ((1U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
                      ? VL_SHIFTRS_III(32,32,5, vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1, 
                                       (0x1fU & vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B))
                      : (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1 
                         >> (0x1fU & vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B)))
                  : ((1U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
                      ? (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1 
                         << (0x1fU & vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B))
                      : (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1 
                         ^ vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B)))
              : ((2U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
                  ? ((1U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
                      ? (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1 
                         | vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B)
                      : (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1 
                         & vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B))
                  : ((1U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_op))
                      ? (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1 
                         - vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B)
                      : (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_rD1 
                         + vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_B)))));
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op = 0U;
    if ((0x40U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((0x20U & vlSelf->miniRV_SoC__DOT__inst)) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 4U)))) {
                if ((8U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                            if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                                vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op = 1U;
                            }
                        }
                    }
                } else if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op = 2U;
                        }
                    }
                } else if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op 
                            = ((0U == (7U & (vlSelf->miniRV_SoC__DOT__inst 
                                             >> 0xcU)))
                                ? ((IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_f)
                                    ? 1U : 0U) : ((4U 
                                                   == 
                                                   (7U 
                                                    & (vlSelf->miniRV_SoC__DOT__inst 
                                                       >> 0xcU)))
                                                   ? 
                                                  ((IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_f)
                                                    ? 1U
                                                    : 0U)
                                                   : 0U));
                    }
                }
            }
        }
    } else if ((0x20U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((0x10U & vlSelf->miniRV_SoC__DOT__inst)) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 3U)))) {
                if ((4U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                        if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                            vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op = 0U;
                        }
                    }
                } else if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op = 0U;
                    }
                }
            }
        } else if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                             >> 3U)))) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 2U)))) {
                if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op = 0U;
                    }
                }
            }
        }
    } else if ((0x10U & vlSelf->miniRV_SoC__DOT__inst)) {
        if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                      >> 3U)))) {
            if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                          >> 2U)))) {
                if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                    if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                        vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op = 0U;
                    }
                }
            }
        }
    } else if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                         >> 3U)))) {
        if ((1U & (~ (vlSelf->miniRV_SoC__DOT__inst 
                      >> 2U)))) {
            if ((2U & vlSelf->miniRV_SoC__DOT__inst)) {
                if ((1U & vlSelf->miniRV_SoC__DOT__inst)) {
                    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op = 0U;
                }
            }
        }
    }
    vlSelf->miniRV_SoC__DOT__we_bridge2dig = ((IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__ram_we) 
                                              & (0xfffff000U 
                                                 == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C));
    vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_tim 
        = ((0xfffff020U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C) 
           | (0xfffff024U == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C));
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_next 
        = ((0U == (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op))
            ? ((IData)(4U) + vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current)
            : ((1U == (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op))
                ? (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current 
                   + vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_ext)
                : ((2U == (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__npc_op))
                    ? (0xfffffffeU & vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                    : ((IData)(4U) + vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current))));
    vlSelf->miniRV_SoC__DOT__we_bridge2tim = ((IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__ram_we) 
                                              & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_tim));
    vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit 
        = (((0xfffffU != (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C 
                          >> 0xcU)) << 5U) | (((0xfffff000U 
                                                == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C) 
                                               << 4U) 
                                              | (((IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_tim) 
                                                  << 3U) 
                                                 | (((0xfffff060U 
                                                      == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C) 
                                                     << 2U) 
                                                    | (((0xfffff070U 
                                                         == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C) 
                                                        << 1U) 
                                                       | (0xfffff078U 
                                                          == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C))))));
}

VL_INLINE_OPT void VminiRV_SoC___024root___combo__TOP__0(VminiRV_SoC___024root* vlSelf) {
    if (false && vlSelf) {}  // Prevent unused
    VminiRV_SoC__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    VL_DEBUG_IF(VL_DBG_MSGF("+    VminiRV_SoC___024root___combo__TOP__0\n"); );
    // Body
    vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wD = 
        ((2U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel))
          ? ((1U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel))
              ? vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__sext_ext
              : ((IData)(4U) + vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__pc_current))
          : ((1U & (IData)(vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__rf_wsel))
              ? ((0x20U & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                  ? vlSelf->miniRV_SoC__DOT__Mem_DRAM__DOT__mem
                 [(0x3fffU & (vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C 
                              >> 2U))] : ((0x10U & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                           ? 0xffffffffU
                                           : ((8U & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                               ? ((4U 
                                                   & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                                   ? 0xffffffffU
                                                   : 
                                                  ((2U 
                                                    & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                                    ? 0xffffffffU
                                                    : 
                                                   ((1U 
                                                     & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                                     ? 0xffffffffU
                                                     : 
                                                    ((0xfffff020U 
                                                      == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                                      ? vlSelf->miniRV_SoC__DOT__U_Timer__DOT__counter0
                                                      : 
                                                     ((0xfffff024U 
                                                       == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                                       ? vlSelf->miniRV_SoC__DOT__U_Timer__DOT__threshold
                                                       : 0U)))))
                                               : ((4U 
                                                   & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                                   ? 0xffffffffU
                                                   : 
                                                  ((2U 
                                                    & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                                    ? 
                                                   ((1U 
                                                     & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                                     ? 0xffffffffU
                                                     : 
                                                    ((0xfffff070U 
                                                      == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                                      ? (IData)(vlSelf->sw)
                                                      : 0U))
                                                    : 
                                                   ((1U 
                                                     & (IData)(vlSelf->miniRV_SoC__DOT__Bridge__DOT__access_bit))
                                                     ? 
                                                    ((0xfffff078U 
                                                      == vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C)
                                                      ? (IData)(vlSelf->miniRV_SoC__DOT__U_Button__DOT__button_stable)
                                                      : 0U)
                                                     : 0xffffffffU))))))
              : vlSelf->miniRV_SoC__DOT__Core_cpu__DOT__alu_C));
}

void VminiRV_SoC___024root___eval(VminiRV_SoC___024root* vlSelf) {
    if (false && vlSelf) {}  // Prevent unused
    VminiRV_SoC__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    VL_DEBUG_IF(VL_DBG_MSGF("+    VminiRV_SoC___024root___eval\n"); );
    // Body
    if ((((IData)(vlSelf->fpga_clk) & (~ (IData)(vlSelf->__Vclklast__TOP__fpga_clk))) 
         | ((IData)(vlSelf->fpga_rst) & (~ (IData)(vlSelf->__Vclklast__TOP__fpga_rst))))) {
        VminiRV_SoC___024root___sequent__TOP__0(vlSelf);
        vlSelf->__Vm_traceActivity[1U] = 1U;
    }
    if (((IData)(vlSelf->fpga_clk) & (~ (IData)(vlSelf->__Vclklast__TOP__fpga_clk)))) {
        VminiRV_SoC___024root___sequent__TOP__1(vlSelf);
        vlSelf->__Vm_traceActivity[2U] = 1U;
    }
    if ((((IData)(vlSelf->fpga_clk) & (~ (IData)(vlSelf->__Vclklast__TOP__fpga_clk))) 
         | ((IData)(vlSelf->fpga_rst) & (~ (IData)(vlSelf->__Vclklast__TOP__fpga_rst))))) {
        VminiRV_SoC___024root___sequent__TOP__2(vlSelf);
        vlSelf->__Vm_traceActivity[3U] = 1U;
    }
    VminiRV_SoC___024root___combo__TOP__0(vlSelf);
    // Final
    vlSelf->__Vclklast__TOP__fpga_clk = vlSelf->fpga_clk;
    vlSelf->__Vclklast__TOP__fpga_rst = vlSelf->fpga_rst;
}

#ifdef VL_DEBUG
void VminiRV_SoC___024root___eval_debug_assertions(VminiRV_SoC___024root* vlSelf) {
    if (false && vlSelf) {}  // Prevent unused
    VminiRV_SoC__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    VL_DEBUG_IF(VL_DBG_MSGF("+    VminiRV_SoC___024root___eval_debug_assertions\n"); );
    // Body
    if (VL_UNLIKELY((vlSelf->fpga_rst & 0xfeU))) {
        Verilated::overWidthError("fpga_rst");}
    if (VL_UNLIKELY((vlSelf->fpga_clk & 0xfeU))) {
        Verilated::overWidthError("fpga_clk");}
    if (VL_UNLIKELY((vlSelf->button & 0xe0U))) {
        Verilated::overWidthError("button");}
}
#endif  // VL_DEBUG
